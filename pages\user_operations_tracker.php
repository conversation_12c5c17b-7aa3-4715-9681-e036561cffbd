<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/activity_logger.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$selected_user_id = $_GET['user_id'] ?? null;
$days_filter = $_GET['days'] ?? 30;
$operation_filter = $_GET['operation'] ?? '';

$page_title = 'متتبع عمليات المستخدمين';
include_once '../includes/header_inner.php';

// Get all users for selection
try {
    $stmt = $pdo->query("
        SELECT u.user_id, u.username, u.full_name, r.role_name, c.center_name, u.is_active, u.created_at
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.role_id 
        LEFT JOIN centers c ON u.center_id = c.center_id 
        ORDER BY u.full_name
    ");
    $all_users = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'خطأ في استرجاع قائمة المستخدمين: ' . $e->getMessage();
}

// Get selected user details
$selected_user = null;
if ($selected_user_id) {
    foreach ($all_users as $user) {
        if ($user['user_id'] == $selected_user_id) {
            $selected_user = $user;
            break;
        }
    }
}
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-user-cog me-2"></i> متتبع عمليات المستخدمين</h1>
        <div>
            <a href="user_activity.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة لنشاط المستخدمين
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <!-- User Selection Panel -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-users me-2"></i>اختيار المستخدم</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="user_id" class="form-label">المستخدم</label>
                    <select name="user_id" id="user_id" class="form-select" onchange="this.form.submit()">
                        <option value="">-- اختر مستخدم --</option>
                        <?php foreach ($all_users as $user): ?>
                            <option value="<?php echo $user['user_id']; ?>" 
                                    <?php echo ($selected_user_id == $user['user_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['full_name'] . ' (' . $user['username'] . ') - ' . $user['role_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="days" class="form-label">فترة البحث</label>
                    <select name="days" id="days" class="form-select" onchange="this.form.submit()">
                        <option value="7" <?php echo ($days_filter == 7) ? 'selected' : ''; ?>>آخر 7 أيام</option>
                        <option value="30" <?php echo ($days_filter == 30) ? 'selected' : ''; ?>>آخر 30 يوم</option>
                        <option value="90" <?php echo ($days_filter == 90) ? 'selected' : ''; ?>>آخر 3 أشهر</option>
                        <option value="365" <?php echo ($days_filter == 365) ? 'selected' : ''; ?>>آخر سنة</option>
                        <option value="0" <?php echo ($days_filter == 0) ? 'selected' : ''; ?>>جميع الفترات</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="operation" class="form-label">نوع العملية</label>
                    <select name="operation" id="operation" class="form-select" onchange="this.form.submit()">
                        <option value="">جميع العمليات</option>
                        <option value="AUTH" <?php echo ($operation_filter == 'AUTH') ? 'selected' : ''; ?>>تسجيل دخول/خروج</option>
                        <option value="USER" <?php echo ($operation_filter == 'USER') ? 'selected' : ''; ?>>إدارة المستخدمين</option>
                        <option value="ATTENDANCE" <?php echo ($operation_filter == 'ATTENDANCE') ? 'selected' : ''; ?>>تسجيل الحضور</option>
                        <option value="MEMORIZATION" <?php echo ($operation_filter == 'MEMORIZATION') ? 'selected' : ''; ?>>تقييم الحفظ</option>
                        <option value="ANNOUNCEMENT" <?php echo ($operation_filter == 'ANNOUNCEMENT') ? 'selected' : ''; ?>>الإعلانات</option>
                        <option value="WHATSAPP" <?php echo ($operation_filter == 'WHATSAPP') ? 'selected' : ''; ?>>رسائل الواتساب</option>
                        <option value="ENROLLMENT" <?php echo ($operation_filter == 'ENROLLMENT') ? 'selected' : ''; ?>>التسجيل في الحلقات</option>
                        <option value="SYSTEM" <?php echo ($operation_filter == 'SYSTEM') ? 'selected' : ''; ?>>عمليات النظام</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block w-100">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <?php if ($selected_user): ?>
        <!-- User Profile Card -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-user me-2"></i>ملف المستخدم: <?php echo htmlspecialchars($selected_user['full_name']); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr><td><strong>اسم المستخدم:</strong></td><td><code><?php echo htmlspecialchars($selected_user['username']); ?></code></td></tr>
                            <tr><td><strong>الاسم الكامل:</strong></td><td><?php echo htmlspecialchars($selected_user['full_name']); ?></td></tr>
                            <tr><td><strong>الدور:</strong></td><td><span class="badge bg-info"><?php echo htmlspecialchars($selected_user['role_name']); ?></span></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr><td><strong>المركز:</strong></td><td><?php echo htmlspecialchars($selected_user['center_name'] ?? 'غير محدد'); ?></td></tr>
                            <tr><td><strong>الحالة:</strong></td><td><span class="badge <?php echo $selected_user['is_active'] ? 'bg-success' : 'bg-danger'; ?>"><?php echo $selected_user['is_active'] ? 'نشط' : 'غير نشط'; ?></span></td></tr>
                            <tr><td><strong>تاريخ التسجيل:</strong></td><td><?php echo $selected_user['created_at']; ?></td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <?php
        // Get comprehensive user operations
        try {
            $user_operations = [];
            $total_operations = 0;
            
            // Build date filter
            $date_condition = "";
            $date_params = [];
            if ($days_filter > 0) {
                $date_condition = " AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
                $date_params[] = $days_filter;
            }
            
            // Build operation filter
            $operation_condition = "";
            if ($operation_filter) {
                $operation_condition = " AND category = ?";
                $date_params[] = $operation_filter;
            }

            // 1. Get from activity_logs (comprehensive)
            $stmt = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
            if ($stmt->rowCount() > 0) {
                $stmt = $pdo->prepare("
                    SELECT 'activity_logs' as source, category, action, description, 
                           ip_address, user_agent, additional_data, created_at
                    FROM activity_logs 
                    WHERE user_id = ? $date_condition $operation_condition
                    ORDER BY created_at DESC
                ");
                $params = array_merge([$selected_user_id], $date_params);
                $stmt->execute($params);
                $activity_logs = $stmt->fetchAll();
                $user_operations = array_merge($user_operations, $activity_logs);
            }

            // 2. Get login/logout from users table (registration)
            if (empty($operation_filter) || $operation_filter == 'USER') {
                $user_operations[] = [
                    'source' => 'users',
                    'category' => 'USER',
                    'action' => 'user_registered',
                    'description' => 'تم تسجيل المستخدم في النظام',
                    'ip_address' => null,
                    'user_agent' => null,
                    'additional_data' => null,
                    'created_at' => $selected_user['created_at']
                ];
            }

            // 3. Get from announcements (if user created any)
            $stmt = $pdo->query("SHOW TABLES LIKE 'announcements'");
            if ($stmt->rowCount() > 0 && (empty($operation_filter) || $operation_filter == 'ANNOUNCEMENT')) {
                // Check which column exists
                $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'sender_user_id'");
                $stmt->execute();
                $has_sender = $stmt->rowCount() > 0;
                
                $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'created_by_user_id'");
                $stmt->execute();
                $has_created_by = $stmt->rowCount() > 0;
                
                $user_id_column = $has_sender ? 'sender_user_id' : 'created_by_user_id';
                
                if ($has_sender || $has_created_by) {
                    $stmt = $pdo->prepare("
                        SELECT 'announcements' as source, 'ANNOUNCEMENT' as category, 
                               'announcement_created' as action,
                               CONCAT('إنشاء إعلان: ', title, ' (مستهدف: ', COALESCE(target_role, 'all'), ')') as description,
                               NULL as ip_address, NULL as user_agent, NULL as additional_data,
                               created_at
                        FROM announcements 
                        WHERE $user_id_column = ? $date_condition
                        ORDER BY created_at DESC
                    ");
                    $params = array_merge([$selected_user_id], $date_params);
                    $stmt->execute($params);
                    $announcements = $stmt->fetchAll();
                    $user_operations = array_merge($user_operations, $announcements);
                }
            }

            // 4. Get from attendance_records (if user recorded attendance)
            $stmt = $pdo->query("SHOW TABLES LIKE 'attendance_records'");
            if ($stmt->rowCount() > 0 && (empty($operation_filter) || $operation_filter == 'ATTENDANCE')) {
                $stmt = $pdo->prepare("
                    SELECT 'attendance_records' as source, 'ATTENDANCE' as category,
                           'attendance_recorded' as action,
                           CONCAT('تسجيل حضور: ', s.full_name, ' - ', 
                                  CASE ar.status 
                                      WHEN 'present' THEN 'حاضر'
                                      WHEN 'absent_excused' THEN 'غائب بعذر'
                                      WHEN 'absent_unexcused' THEN 'غائب بدون عذر'
                                      WHEN 'late' THEN 'متأخر'
                                      ELSE ar.status
                                  END, ' - ', ar.session_date) as description,
                           NULL as ip_address, NULL as user_agent, NULL as additional_data,
                           COALESCE(ar.recorded_at, CONCAT(ar.session_date, ' 12:00:00')) as created_at
                    FROM attendance_records ar
                    JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
                    JOIN users s ON sce.student_user_id = s.user_id
                    WHERE ar.recorded_by_user_id = ? $date_condition
                    ORDER BY created_at DESC
                ");
                $params = array_merge([$selected_user_id], $date_params);
                $stmt->execute($params);
                $attendance_records = $stmt->fetchAll();
                $user_operations = array_merge($user_operations, $attendance_records);
            }

            // 5. Get from memorization_progress (if user evaluated)
            $stmt = $pdo->query("SHOW TABLES LIKE 'memorization_progress'");
            if ($stmt->rowCount() > 0 && (empty($operation_filter) || $operation_filter == 'MEMORIZATION')) {
                $stmt = $pdo->prepare("
                    SELECT 'memorization_progress' as source, 'MEMORIZATION' as category,
                           'memorization_evaluated' as action,
                           CONCAT('تقييم حفظ: ', s.full_name, ' - سورة ', mp.surah_name,
                                  ' (آية ', mp.ayah_from, ' إلى ', mp.ayah_to, ') - ', mp.memorization_quality) as description,
                           NULL as ip_address, NULL as user_agent, NULL as additional_data,
                           mp.recitation_date as created_at
                    FROM memorization_progress mp
                    JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
                    JOIN users s ON sce.student_user_id = s.user_id
                    WHERE mp.recorded_by_user_id = ? $date_condition
                    ORDER BY created_at DESC
                ");
                $params = array_merge([$selected_user_id], $date_params);
                $stmt->execute($params);
                $memorization_records = $stmt->fetchAll();
                $user_operations = array_merge($user_operations, $memorization_records);
            }

            // 6. Get enrollments (if user is a student)
            if (empty($operation_filter) || $operation_filter == 'ENROLLMENT') {
                $stmt = $pdo->prepare("
                    SELECT 'student_circle_enrollments' as source, 'ENROLLMENT' as category,
                           'student_enrolled' as action,
                           CONCAT('تسجيل في حلقة: ', c.circle_name) as description,
                           NULL as ip_address, NULL as user_agent, NULL as additional_data,
                           sce.enrollment_date as created_at
                    FROM student_circle_enrollments sce
                    JOIN circles c ON sce.circle_id = c.circle_id
                    WHERE sce.student_user_id = ? $date_condition
                    ORDER BY created_at DESC
                ");
                $params = array_merge([$selected_user_id], $date_params);
                $stmt->execute($params);
                $enrollments = $stmt->fetchAll();
                $user_operations = array_merge($user_operations, $enrollments);
            }

            // Sort all operations by date
            usort($user_operations, function($a, $b) {
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });

            $total_operations = count($user_operations);

        } catch (PDOException $e) {
            $error = 'خطأ في استرجاع عمليات المستخدم: ' . $e->getMessage();
        }
        ?>

        <!-- Operations Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary"><?php echo $total_operations; ?></h3>
                        <p class="mb-0">إجمالي العمليات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <?php
                        $auth_operations = array_filter($user_operations, function($op) { return $op['category'] == 'AUTH'; });
                        ?>
                        <h3 class="text-success"><?php echo count($auth_operations); ?></h3>
                        <p class="mb-0">عمليات المصادقة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <?php
                        $work_operations = array_filter($user_operations, function($op) { 
                            return in_array($op['category'], ['ATTENDANCE', 'MEMORIZATION', 'ANNOUNCEMENT', 'USER']); 
                        });
                        ?>
                        <h3 class="text-info"><?php echo count($work_operations); ?></h3>
                        <p class="mb-0">عمليات العمل</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <?php
                        $recent_operations = array_filter($user_operations, function($op) { 
                            return strtotime($op['created_at']) >= strtotime('-7 days'); 
                        });
                        ?>
                        <h3 class="text-warning"><?php echo count($recent_operations); ?></h3>
                        <p class="mb-0">آخر 7 أيام</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Operations Timeline -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-timeline me-2"></i>سجل العمليات المفصل</h5>
                <small class="text-muted">
                    عرض <?php echo min(100, $total_operations); ?> من أصل <?php echo $total_operations; ?> عملية
                    <?php if ($days_filter > 0): ?>
                        (آخر <?php echo $days_filter; ?> يوم)
                    <?php endif; ?>
                </small>
            </div>
            <div class="card-body">
                <?php if (!empty($user_operations)): ?>
                    <div class="timeline">
                        <?php 
                        $displayed_operations = array_slice($user_operations, 0, 100);
                        foreach ($displayed_operations as $index => $operation): 
                            $category_colors = [
                                'AUTH' => 'primary',
                                'USER' => 'success',
                                'ATTENDANCE' => 'info',
                                'MEMORIZATION' => 'warning',
                                'ANNOUNCEMENT' => 'danger',
                                'WHATSAPP' => 'dark',
                                'ENROLLMENT' => 'secondary',
                                'SYSTEM' => 'light'
                            ];
                            $color = $category_colors[$operation['category']] ?? 'secondary';
                            
                            $category_icons = [
                                'AUTH' => 'fas fa-sign-in-alt',
                                'USER' => 'fas fa-user-cog',
                                'ATTENDANCE' => 'fas fa-calendar-check',
                                'MEMORIZATION' => 'fas fa-book-open',
                                'ANNOUNCEMENT' => 'fas fa-bullhorn',
                                'WHATSAPP' => 'fab fa-whatsapp',
                                'ENROLLMENT' => 'fas fa-user-plus',
                                'SYSTEM' => 'fas fa-cog'
                            ];
                            $icon = $category_icons[$operation['category']] ?? 'fas fa-circle';
                        ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-<?php echo $color; ?>">
                                    <i class="<?php echo $icon; ?> text-white"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <span class="badge bg-<?php echo $color; ?> me-2"><?php echo $operation['category']; ?></span>
                                                <?php echo htmlspecialchars($operation['description']); ?>
                                            </h6>
                                            <p class="text-muted mb-1">
                                                <small>
                                                    <i class="fas fa-clock me-1"></i><?php echo $operation['created_at']; ?>
                                                    <i class="fas fa-database ms-3 me-1"></i><?php echo $operation['source']; ?>
                                                    <?php if ($operation['ip_address']): ?>
                                                        <i class="fas fa-globe ms-3 me-1"></i><?php echo $operation['ip_address']; ?>
                                                    <?php endif; ?>
                                                </small>
                                            </p>
                                            <?php if ($operation['additional_data']): ?>
                                                <details class="mt-2">
                                                    <summary class="text-primary" style="cursor: pointer;">
                                                        <small>عرض التفاصيل الإضافية</small>
                                                    </summary>
                                                    <pre class="bg-light p-2 mt-1 small"><?php echo htmlspecialchars($operation['additional_data']); ?></pre>
                                                </details>
                                            <?php endif; ?>
                                        </div>
                                        <small class="text-muted">#<?php echo $index + 1; ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <?php if ($total_operations > 100): ?>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            تم عرض أول 100 عملية فقط. لعرض المزيد، استخدم فلاتر أكثر تحديداً.
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد عمليات للمستخدم المحدد</h5>
                        <p class="text-muted">جرب تغيير فترة البحث أو نوع العملية</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">اختر مستخدماً لعرض عملياته</h5>
                <p class="text-muted">استخدم القائمة المنسدلة أعلاه لاختيار المستخدم الذي تريد تتبع عملياته</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #fff;
    box-shadow: 0 0 0 3px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #dee2e6;
}
</style>

<?php include_once '../includes/footer.php'; ?>
