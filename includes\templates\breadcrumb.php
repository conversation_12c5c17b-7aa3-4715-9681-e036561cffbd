<?php
/**
 * Breadcrumb Template
 * 
 * This template displays a breadcrumb navigation
 * 
 * @param array $breadcrumbs - Array of breadcrumbs [['title' => 'Home', 'url' => 'index.php'], ['title' => 'Current Page']]
 */

// Default empty breadcrumbs if not set
$breadcrumbs = $breadcrumbs ?? [];
?>

<?php if (!empty($breadcrumbs)): ?>
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <?php foreach ($breadcrumbs as $index => $crumb): ?>
            <?php if (isset($crumb['url']) && $index < count($breadcrumbs) - 1): ?>
                <li class="breadcrumb-item"><a href="<?php echo $crumb['url']; ?>"><?php echo $crumb['title']; ?></a></li>
            <?php else: ?>
                <li class="breadcrumb-item active" aria-current="page"><?php echo $crumb['title']; ?></li>
            <?php endif; ?>
        <?php endforeach; ?>
    </ol>
</nav>
<?php endif; ?>
