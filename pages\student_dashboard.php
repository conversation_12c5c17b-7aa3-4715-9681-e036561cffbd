<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has student role
if (!is_logged_in() || !has_role('student')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Get student information
$student_id = $_SESSION['user_id'];

// Get student's circle information
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name, c.level, c.schedule_details,
               t.user_id AS teacher_id, t.full_name AS teacher_name,
               ce.center_name, sce.enrollment_id
        FROM student_circle_enrollments sce
        JOIN circles c ON sce.circle_id = c.circle_id
        JOIN users t ON c.teacher_user_id = t.user_id
        JOIN centers ce ON c.center_id = ce.center_id
        WHERE sce.student_user_id = ? AND sce.status = 'approved'
    ");
    $stmt->execute([$student_id]);
    $circle = $stmt->fetch();

    if ($circle) {
        $enrollment_id = $circle['enrollment_id'];
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
}

// Get student's memorization progress
if (isset($enrollment_id)) {
    try {
        // Get overall progress statistics
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS total_recitations,
                   SUM(CASE WHEN memorization_quality = 'excellent' THEN 1 ELSE 0 END) AS excellent_count,
                   SUM(CASE WHEN memorization_quality = 'very_good' THEN 1 ELSE 0 END) AS very_good_count,
                   SUM(CASE WHEN memorization_quality = 'good' THEN 1 ELSE 0 END) AS good_count,
                   SUM(CASE WHEN memorization_quality = 'fair' THEN 1 ELSE 0 END) AS fair_count,
                   SUM(CASE WHEN memorization_quality = 'poor' THEN 1 ELSE 0 END) AS poor_count
            FROM memorization_progress
            WHERE enrollment_id = ?
        ");
        $stmt->execute([$enrollment_id]);
        $progress_stats = $stmt->fetch();

        // Get recent memorization progress
        $stmt = $pdo->prepare("
            SELECT progress_id, surah_name, ayah_from, ayah_to,
                   recitation_date, memorization_quality, tajweed_application, fluency,
                   teacher_notes
            FROM memorization_progress
            WHERE enrollment_id = ?
            ORDER BY recitation_date DESC
            LIMIT 5
        ");
        $stmt->execute([$enrollment_id]);
        $recent_progress = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات التقدم: ' . $e->getMessage();
    }

    // Get attendance statistics
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS total_sessions,
                   SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) AS present_count,
                   SUM(CASE WHEN status = 'absent_excused' THEN 1 ELSE 0 END) AS excused_count,
                   SUM(CASE WHEN status = 'absent_unexcused' THEN 1 ELSE 0 END) AS unexcused_count,
                   SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) AS late_count
            FROM attendance_records
            WHERE enrollment_id = ?
        ");
        $stmt->execute([$enrollment_id]);
        $attendance_stats = $stmt->fetch();

        // Calculate attendance percentage
        if ($attendance_stats['total_sessions'] > 0) {
            $attendance_percentage = round(($attendance_stats['present_count'] / $attendance_stats['total_sessions']) * 100);
        } else {
            $attendance_percentage = 0;
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الحضور: ' . $e->getMessage();
    }

    // Get upcoming assignments
    try {
        $stmt = $pdo->prepare("
            SELECT a.assignment_id, a.title, a.description, a.due_date,
                   sa.status, sa.submission_date
            FROM assignments a
            LEFT JOIN student_assignments sa ON a.assignment_id = sa.assignment_id AND sa.student_user_id = ?
            WHERE a.circle_id = ? AND (sa.status IS NULL OR sa.status IN ('pending', 'late_submission'))
            ORDER BY a.due_date ASC
            LIMIT 5
        ");
        $stmt->execute([$student_id, $circle['circle_id']]);
        $upcoming_assignments = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الواجبات: ' . $e->getMessage();
    }

    // Get current memorization schedule
    try {
        $stmt = $pdo->prepare("
            SELECT schedule_id, target_surah_start, target_ayah_start,
                   target_surah_end, target_ayah_end,
                   target_juz, target_page_start, target_page_end,
                   type, assigned_date, due_date, is_completed
            FROM memorization_schedules
            WHERE enrollment_id = ? AND is_completed = FALSE
            ORDER BY due_date ASC
            LIMIT 1
        ");
        $stmt->execute([$enrollment_id]);
        $current_schedule = $stmt->fetch(PDO::FETCH_ASSOC);

        // If no schedule is found, initialize an empty array to avoid errors
        if (!$current_schedule) {
            $current_schedule = [
                'schedule_id' => null,
                'type' => null,
                'assigned_date' => null,
                'due_date' => null,
                'target_surah_start' => null,
                'target_ayah_start' => null,
                'target_surah_end' => null,
                'target_ayah_end' => null,
                'target_juz' => null,
                'target_page_start' => null,
                'target_page_end' => null,
                'is_completed' => null
            ];
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات خطة الحفظ: ' . $e->getMessage();
        // Initialize empty schedule on error
        $current_schedule = [
            'schedule_id' => null,
            'type' => null,
            'assigned_date' => null,
            'due_date' => null,
            'target_surah_start' => null,
            'target_ayah_start' => null,
            'target_surah_end' => null,
            'target_ayah_end' => null,
            'target_juz' => null,
            'target_page_start' => null,
            'target_page_end' => null,
            'is_completed' => null
        ];
    }
}

// Get unread messages
try {
    $stmt = $pdo->prepare("
        SELECT m.message_id, m.subject, m.sent_at, u.full_name AS sender_name
        FROM messages m
        JOIN users u ON m.sender_user_id = u.user_id
        WHERE m.recipient_user_id = ? AND m.read_at IS NULL
        ORDER BY m.sent_at DESC
        LIMIT 5
    ");
    $stmt->execute([$student_id]);
    $unread_messages = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الرسائل: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الطالب - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link active" href="<?php echo get_root_url(); ?>pages/student_dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo get_root_url(); ?>pages/my_progress.php">تقدمي</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo get_root_url(); ?>pages/student_assignments.php">الواجبات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo get_root_url(); ?>pages/messages.php">الرسائل</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="<?php echo get_root_url(); ?>pages/profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="<?php echo get_root_url(); ?>pages/settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo get_root_url(); ?>auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container py-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <h1 class="mb-4">مرحباً، <?php echo $_SESSION['full_name']; ?></h1>

        <?php if (isset($circle)): ?>
            <!-- Circle Information -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card dashboard-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> معلومات الحلقة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5><?php echo isset($circle['circle_name']) ? $circle['circle_name'] : 'غير متوفر'; ?></h5>
                                    <p><strong>المستوى:</strong> <?php echo isset($circle['level']) ? $circle['level'] : 'غير متوفر'; ?></p>
                                    <p><strong>المعلم:</strong> <?php echo isset($circle['teacher_name']) ? $circle['teacher_name'] : 'غير متوفر'; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>المركز:</strong> <?php echo isset($circle['center_name']) ? $circle['center_name'] : 'غير متوفر'; ?></p>
                                    <p><strong>مواعيد الحلقة:</strong> <?php echo isset($circle['schedule_details']) ? $circle['schedule_details'] : 'غير متوفر'; ?></p>
                                    <?php if (isset($circle['teacher_id'])): ?>
                                    <p>
                                        <a href="teacher_contact.php?id=<?php echo $circle['teacher_id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-envelope me-1"></i> التواصل مع المعلم
                                        </a>
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Row -->
            <div class="row mb-4">
                <!-- Attendance Stats -->
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stats-card stats-primary">
                        <i class="fas fa-calendar-check"></i>
                        <div class="stats-value"><?php echo isset($attendance_percentage) ? $attendance_percentage . '%' : '0%'; ?></div>
                        <div class="stats-label">نسبة الحضور</div>
                    </div>
                </div>

                <!-- Memorization Stats -->
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stats-card stats-success">
                        <i class="fas fa-book-reader"></i>
                        <div class="stats-value"><?php echo isset($progress_stats) ? $progress_stats['total_recitations'] : '0'; ?></div>
                        <div class="stats-label">عدد التسميعات</div>
                    </div>
                </div>

                <!-- Excellent Ratings -->
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stats-card stats-warning">
                        <i class="fas fa-star"></i>
                        <div class="stats-value"><?php echo isset($progress_stats) ? $progress_stats['excellent_count'] : '0'; ?></div>
                        <div class="stats-label">تقييم ممتاز</div>
                    </div>
                </div>

                <!-- Pending Assignments -->
                <div class="col-md-6 col-lg-3 mb-3">
                    <div class="stats-card stats-danger">
                        <i class="fas fa-tasks"></i>
                        <div class="stats-value"><?php echo isset($upcoming_assignments) ? count($upcoming_assignments) : '0'; ?></div>
                        <div class="stats-label">واجبات معلقة</div>
                    </div>
                </div>
            </div>

            <!-- Current Memorization Schedule -->
            <?php if (isset($current_schedule) && $current_schedule['schedule_id'] !== null): ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card dashboard-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-bookmark me-2"></i> خطة الحفظ الحالية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><?php echo isset($current_schedule['type']) && $current_schedule['type'] == 'memorization' ? 'حفظ جديد' : 'مراجعة'; ?></h5>
                                        <?php if (!empty($current_schedule['target_surah_start'])): ?>
                                            <p>
                                                <strong>المقطع:</strong>
                                                <?php echo $current_schedule['target_surah_start']; ?>
                                                (<?php echo $current_schedule['target_ayah_start']; ?>) -
                                                <?php echo $current_schedule['target_surah_end']; ?>
                                                (<?php echo $current_schedule['target_ayah_end']; ?>)
                                            </p>
                                        <?php endif; ?>

                                        <?php if (!empty($current_schedule['target_juz'])): ?>
                                            <p><strong>الجزء:</strong> <?php echo $current_schedule['target_juz']; ?></p>
                                        <?php endif; ?>

                                        <?php if (!empty($current_schedule['target_page_start'])): ?>
                                            <p>
                                                <strong>الصفحات:</strong>
                                                <?php echo $current_schedule['target_page_start']; ?> -
                                                <?php echo $current_schedule['target_page_end']; ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>تاريخ التكليف:</strong> <?php echo !empty($current_schedule['assigned_date']) ? date('Y-m-d', strtotime($current_schedule['assigned_date'])) : 'غير محدد'; ?></p>
                                        <p><strong>تاريخ الاستحقاق:</strong> <?php echo !empty($current_schedule['due_date']) ? date('Y-m-d', strtotime($current_schedule['due_date'])) : 'غير محدد'; ?></p>
                                        <?php if (isset($current_schedule['schedule_id'])): ?>
                                        <div class="mt-3">
                                            <button class="btn btn-success" id="mark-completed" data-schedule-id="<?php echo $current_schedule['schedule_id']; ?>">
                                                <i class="fas fa-check me-1"></i> تم الحفظ/المراجعة
                                            </button>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Progress and Upcoming Assignments -->
            <div class="row mb-4">
                <!-- Recent Memorization Progress -->
                <div class="col-md-6 mb-4">
                    <div class="card dashboard-card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i> آخر تقدم في الحفظ</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_progress)): ?>
                                <p class="text-center">لا يوجد تقدم مسجل حالياً.</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>السورة</th>
                                                <th>الآيات</th>
                                                <th>التقييم</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_progress as $progress): ?>
                                                <tr>
                                                    <td><?php echo $progress['surah_name']; ?></td>
                                                    <td><?php echo $progress['ayah_from'] . ' - ' . $progress['ayah_to']; ?></td>
                                                    <td>
                                                        <?php
                                                        $quality = $progress['memorization_quality'];
                                                        $color = '';
                                                        switch ($quality) {
                                                            case 'excellent': $color = 'success'; break;
                                                            case 'very_good': $color = 'primary'; break;
                                                            case 'good': $color = 'info'; break;
                                                            case 'fair': $color = 'warning'; break;
                                                            case 'poor': $color = 'danger'; break;
                                                        }
                                                        echo '<span class="badge bg-' . $color . '">' . ucfirst(str_replace('_', ' ', $quality)) . '</span>';
                                                        ?>
                                                    </td>
                                                    <td><?php echo date('Y-m-d', strtotime($progress['recitation_date'])); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="<?php echo get_root_url(); ?>pages/my_progress.php" class="btn btn-sm btn-outline-info">عرض الكل</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Assignments -->
                <div class="col-md-6 mb-4">
                    <div class="card dashboard-card h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0"><i class="fas fa-tasks me-2"></i> الواجبات القادمة</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($upcoming_assignments)): ?>
                                <p class="text-center">لا توجد واجبات قادمة حالياً.</p>
                            <?php else: ?>
                                <div class="list-group">
                                    <?php foreach ($upcoming_assignments as $assignment): ?>
                                        <a href="<?php echo get_root_url(); ?>pages/assignment_details.php?id=<?php echo $assignment['assignment_id']; ?>" class="list-group-item list-group-item-action">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1"><?php echo $assignment['title']; ?></h6>
                                                <?php
                                                $due_date = strtotime($assignment['due_date']);
                                                $now = time();
                                                $days_left = ceil(($due_date - $now) / (60 * 60 * 24));

                                                if ($days_left < 0) {
                                                    echo '<span class="badge bg-danger">متأخر</span>';
                                                } elseif ($days_left == 0) {
                                                    echo '<span class="badge bg-warning">اليوم</span>';
                                                } else {
                                                    echo '<span class="badge bg-info">' . $days_left . ' يوم متبقي</span>';
                                                }
                                                ?>
                                            </div>
                                            <p class="mb-1"><?php echo substr($assignment['description'], 0, 100) . (strlen($assignment['description']) > 100 ? '...' : ''); ?></p>
                                            <small>تاريخ الاستحقاق: <?php echo date('Y-m-d', $due_date); ?></small>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="<?php echo get_root_url(); ?>pages/student_assignments.php" class="btn btn-sm btn-outline-warning">عرض كل الواجبات</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-info">
                <h4 class="alert-heading">لم يتم تسجيلك في أي حلقة بعد!</h4>
                <p>يبدو أنك لم تنضم إلى أي حلقة تحفيظ حتى الآن. يرجى التواصل مع إدارة المركز للتسجيل في إحدى الحلقات.</p>
            </div>
        <?php endif; ?>

        <!-- Unread Messages -->
        <div class="row">
            <div class="col-md-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-envelope me-2"></i> الرسائل غير المقروءة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($unread_messages)): ?>
                            <p class="text-center">لا توجد رسائل غير مقروءة.</p>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($unread_messages as $message): ?>
                                    <a href="<?php echo get_root_url(); ?>pages/message_details.php?id=<?php echo $message['message_id']; ?>" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?php echo $message['subject']; ?></h6>
                                            <small><?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?></small>
                                        </div>
                                        <p class="mb-1">من: <?php echo $message['sender_name']; ?></p>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="<?php echo get_root_url(); ?>pages/messages.php" class="btn btn-sm btn-outline-secondary">عرض كل الرسائل</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="<?php echo get_root_url(); ?>pages/about.php" class="text-white">عن النظام</a></li>
                        <li><a href="<?php echo get_root_url(); ?>pages/contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>

    <script>
        $(document).ready(function() {
            // Handle mark as completed button
            $('#mark-completed').on('click', function() {
                var scheduleId = $(this).data('schedule-id');

                $.ajax({
                    url: '../includes/update_schedule_status.php',
                    type: 'POST',
                    data: {
                        schedule_id: scheduleId,
                        is_completed: true
                    },
                    success: function(response) {
                        var data = JSON.parse(response);
                        if (data.success) {
                            alert('تم تحديث حالة الخطة بنجاح!');
                            location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.message);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ أثناء معالجة الطلب.');
                    }
                });
            });
        });
    </script>

    <?php
    // Include announcements modal
    include_once '../includes/announcement_modal.php';
    ?>
</body>
</html>
