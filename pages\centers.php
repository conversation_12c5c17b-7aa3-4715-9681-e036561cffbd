<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';

// Handle center actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $center_id = (int)$_GET['id'];
    
    if ($action === 'delete') {
        try {
            // Check if center has circles
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM circles WHERE center_id = ?");
            $stmt->execute([$center_id]);
            $circle_count = $stmt->fetchColumn();
            
            if ($circle_count > 0) {
                $error = 'لا يمكن حذف المركز لأنه يحتوي على حلقات. قم بحذف الحلقات أولاً.';
            } else {
                // Delete center
                $stmt = $pdo->prepare("DELETE FROM centers WHERE center_id = ?");
                $stmt->execute([$center_id]);
                $success = 'تم حذف المركز بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء حذف المركز: ' . $e->getMessage();
        }
    } elseif ($action === 'toggle') {
        try {
            // Toggle center active status
            $stmt = $pdo->prepare("UPDATE centers SET is_active = NOT is_active WHERE center_id = ?");
            $stmt->execute([$center_id]);
            $success = 'تم تغيير حالة المركز بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تغيير حالة المركز: ' . $e->getMessage();
        }
    }
}

// Get centers with statistics
try {
    $stmt = $pdo->prepare("
        SELECT c.*, 
               u.full_name AS director_name,
               (SELECT COUNT(*) FROM circles WHERE center_id = c.center_id) AS circle_count,
               (SELECT COUNT(*) FROM users WHERE center_id = c.center_id AND role_id = 3) AS teacher_count,
               (SELECT COUNT(*) FROM users WHERE center_id = c.center_id AND role_id = 4) AS student_count
        FROM centers c
        LEFT JOIN users u ON c.director_user_id = u.user_id
        ORDER BY c.center_name
    ");
    $stmt->execute();
    $centers = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المراكز - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="system_owner_dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="centers.php">المراكز</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">المستخدمين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="system_settings.php">إعدادات النظام</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <main class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-mosque me-2"></i> إدارة المراكز</h1>
            <a href="add_center.php" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة مركز جديد
            </a>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (empty($centers)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد مراكز مسجلة حالياً. قم بإضافة مركز جديد.
            </div>
        <?php else: ?>
            <div class="card shadow">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="centersTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>المركز</th>
                                    <th>العنوان</th>
                                    <th>مسؤول الاتصال</th>
                                    <th>المدير</th>
                                    <th>الإحصائيات</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($centers as $center): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $center['center_name']; ?></strong>
                                        </td>
                                        <td><?php echo $center['address']; ?></td>
                                        <td>
                                            <?php echo $center['contact_person_name']; ?><br>
                                            <small>
                                                <i class="fas fa-envelope me-1"></i> <?php echo $center['contact_email']; ?><br>
                                                <i class="fas fa-phone me-1"></i> <?php echo $center['contact_phone']; ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($center['director_name']): ?>
                                                <?php echo $center['director_name']; ?>
                                            <?php else: ?>
                                                <span class="text-muted">غير معين</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info me-1" title="الحلقات">
                                                <i class="fas fa-circle me-1"></i> <?php echo $center['circle_count']; ?>
                                            </span>
                                            <span class="badge bg-success me-1" title="المعلمين">
                                                <i class="fas fa-chalkboard-teacher me-1"></i> <?php echo $center['teacher_count']; ?>
                                            </span>
                                            <span class="badge bg-primary" title="الطلاب">
                                                <i class="fas fa-user-graduate me-1"></i> <?php echo $center['student_count']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($center['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="center_details.php?id=<?php echo $center['center_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_center.php?id=<?php echo $center['center_id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="centers.php?action=toggle&id=<?php echo $center['center_id']; ?>" class="btn btn-sm btn-secondary" title="<?php echo $center['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                                    <i class="fas <?php echo $center['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                                </a>
                                                <a href="centers.php?action=delete&id=<?php echo $center['center_id']; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المركز؟')">
                                                    <i class="fas fa-trash-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </main>
    
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#centersTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                },
                "order": [[ 0, "asc" ]]
            });
        });
    </script>
</body>
</html>
