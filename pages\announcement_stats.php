<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/announcement_stats.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Check if announcement ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف الإعلان غير صحيح');
    redirect('pages/announcements.php');
}

$announcement_id = (int)$_GET['id'];
$error = '';
$success = '';

// Get announcement information
try {
    $stmt = $pdo->prepare("
        SELECT a.*, u.full_name AS sender_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        WHERE a.announcement_id = ?
    ");
    $stmt->execute([$announcement_id]);
    $announcement = $stmt->fetch();

    if (!$announcement) {
        set_flash_message('danger', 'الإعلان غير موجود');
        redirect('pages/announcements.php');
    }

    // Get announcement statistics
    $stats = get_announcement_stats($announcement_id);

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الإعلان: ' . $e->getMessage();
}

// Page variables
$page_title = 'إحصائيات الإعلان: ' . $announcement['title'];
$active_page = 'announcements';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'الإعلانات', 'url' => 'announcements.php'],
    ['title' => 'إحصائيات الإعلان']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="fas fa-chart-bar me-2"></i>
        إحصائيات الإعلان: <?php echo $announcement['title']; ?>
    </h1>
    <div>
        <a href="announcements.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-eye me-2"></i> المشاهدات</h5>
            </div>
            <div class="card-body text-center">
                <h1 class="display-4"><?php echo isset($stats['view_count']) ? number_format($stats['view_count']) : '0'; ?></h1>
                <p class="text-muted">إجمالي عدد المشاهدات</p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0"><i class="fas fa-mouse-pointer me-2"></i> النقرات</h5>
            </div>
            <div class="card-body text-center">
                <h1 class="display-4"><?php echo isset($stats['click_count']) ? number_format($stats['click_count']) : '0'; ?></h1>
                <p class="text-muted">إجمالي عدد النقرات</p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0"><i class="fas fa-percentage me-2"></i> معدل النقر إلى الظهور</h5>
            </div>
            <div class="card-body text-center">
                <h1 class="display-4"><?php echo isset($stats['ctr']) ? $stats['ctr'] : '0'; ?>%</h1>
                <p class="text-muted">نسبة النقرات إلى المشاهدات</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i> المشاهدات حسب التاريخ</h5>
            </div>
            <div class="card-body">
                <canvas id="viewsChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i> النقرات حسب التاريخ</h5>
            </div>
            <div class="card-body">
                <canvas id="clicksChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> معلومات الإعلان</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-striped">
                    <tr>
                        <th>العنوان</th>
                        <td><?php echo $announcement['title']; ?></td>
                    </tr>
                    <tr>
                        <th>المرسل</th>
                        <td><?php echo $announcement['sender_name']; ?></td>
                    </tr>
                    <tr>
                        <th>تاريخ الإنشاء</th>
                        <td><?php echo date('Y-m-d H:i', strtotime($announcement['created_at'])); ?></td>
                    </tr>
                    <tr>
                        <th>تاريخ البداية</th>
                        <td><?php echo isset($announcement['start_date']) ? $announcement['start_date'] : date('Y-m-d', strtotime($announcement['created_at'])); ?></td>
                    </tr>
                    <tr>
                        <th>تاريخ النهاية</th>
                        <td><?php echo isset($announcement['end_date']) ? $announcement['end_date'] : date('Y-m-d', strtotime($announcement['created_at'] . ' +30 days')); ?></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-striped">
                    <tr>
                        <th>الفئة المستهدفة</th>
                        <td>
                            <?php
                            switch ($announcement['target_role']) {
                                case 'all':
                                    echo 'الجميع';
                                    break;
                                case 'system_owner':
                                    echo 'مدير النظام';
                                    break;
                                case 'center_admin':
                                    echo 'مدير المركز';
                                    break;
                                case 'teacher':
                                    echo 'المعلم';
                                    break;
                                case 'student':
                                    echo 'الطالب';
                                    break;
                                case 'parent':
                                    echo 'ولي الأمر';
                                    break;
                                case 'center_specific':
                                    echo 'مركز محدد';
                                    break;
                                default:
                                    echo $announcement['target_role'];
                            }
                            ?>
                        </td>
                    </tr>
                    <?php if ($announcement['target_role'] == 'center_specific'): ?>
                    <tr>
                        <th>المركز المستهدف</th>
                        <td>
                            <?php
                            $stmt = $pdo->prepare("SELECT center_name FROM centers WHERE center_id = ?");
                            $stmt->execute([$announcement['target_center_id']]);
                            $center = $stmt->fetch();
                            echo $center ? $center['center_name'] : 'غير محدد';
                            ?>
                        </td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <th>الحالة</th>
                        <td><?php echo isset($announcement['is_active']) && $announcement['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>'; ?></td>
                    </tr>
                    <tr>
                        <th>ظهور للزوار</th>
                        <td><?php echo isset($announcement['is_public']) && $announcement['is_public'] ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-secondary">لا</span>'; ?></td>
                    </tr>
                    <tr>
                        <th>إعلان مميز</th>
                        <td><?php echo isset($announcement['is_featured']) && $announcement['is_featured'] ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-secondary">لا</span>'; ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Prepare data for views chart
        const viewsData = {
            labels: <?php echo json_encode(isset($stats['view_stats_by_date']) && is_array($stats['view_stats_by_date']) ? array_column(array_reverse($stats['view_stats_by_date']), 'date') : []); ?>,
            datasets: [{
                label: 'المشاهدات',
                data: <?php echo json_encode(isset($stats['view_stats_by_date']) && is_array($stats['view_stats_by_date']) ? array_column(array_reverse($stats['view_stats_by_date']), 'count') : []); ?>,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        };

        // Prepare data for clicks chart
        const clicksData = {
            labels: <?php echo json_encode(isset($stats['click_stats_by_date']) && is_array($stats['click_stats_by_date']) ? array_column(array_reverse($stats['click_stats_by_date']), 'date') : []); ?>,
            datasets: [{
                label: 'النقرات',
                data: <?php echo json_encode(isset($stats['click_stats_by_date']) && is_array($stats['click_stats_by_date']) ? array_column(array_reverse($stats['click_stats_by_date']), 'count') : []); ?>,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        };

        // Create views chart
        const viewsCtx = document.getElementById('viewsChart').getContext('2d');
        new Chart(viewsCtx, {
            type: 'line',
            data: viewsData,
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Create clicks chart
        const clicksCtx = document.getElementById('clicksChart').getContext('2d');
        new Chart(clicksCtx, {
            type: 'line',
            data: clicksData,
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>

<?php
// Include footer template
include_template('footer');
?>
