<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Page variables
$page_title = 'إضافة حلقة جديدة';
$active_page = 'circles';
$success = '';
$error = '';
$center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_GET['center_id']) ? (int)$_GET['center_id'] : null);

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Get teachers for dropdown
$teachers = [];
try {
    $query = "
        SELECT u.user_id, u.full_name, u.email
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE r.role_name = 'teacher' AND u.is_active = TRUE
    ";
    
    if ($center_id) {
        $query .= " AND u.center_id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$center_id]);
    } else {
        $stmt = $pdo->prepare($query);
        $stmt->execute();
    }
    
    $teachers = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المعلمين: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $circle_name = sanitize_input($_POST['circle_name']);
    $level = sanitize_input($_POST['level']);
    $selected_center_id = has_role('center_admin') ? $_SESSION['center_id'] : (int)$_POST['center_id'];
    $teacher_id = isset($_POST['teacher_id']) ? (int)$_POST['teacher_id'] : null;
    $schedule_details = sanitize_input($_POST['schedule_details']);
    $max_students = (int)$_POST['max_students'];
    $description = sanitize_input($_POST['description']);
    
    // Validate required fields
    if (empty($circle_name)) {
        $error = 'يرجى إدخال اسم الحلقة';
    } elseif (empty($level)) {
        $error = 'يرجى اختيار المستوى';
    } elseif (has_role('system_owner') && empty($selected_center_id)) {
        $error = 'يرجى اختيار المركز';
    } elseif (empty($teacher_id)) {
        $error = 'يرجى اختيار المعلم';
    } elseif (empty($schedule_details)) {
        $error = 'يرجى إدخال مواعيد الحلقة';
    } else {
        try {
            // Check if circle name already exists in the same center
            $stmt = $pdo->prepare("SELECT circle_id FROM circles WHERE circle_name = ? AND center_id = ?");
            $stmt->execute([$circle_name, $selected_center_id]);
            if ($stmt->rowCount() > 0) {
                $error = 'اسم الحلقة موجود بالفعل في هذا المركز، يرجى اختيار اسم آخر';
            } else {
                // Insert new circle
                $stmt = $pdo->prepare("
                    INSERT INTO circles (
                        circle_name, center_id, teacher_user_id, level, 
                        schedule_details, max_students, description, is_active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                ");
                $stmt->execute([
                    $circle_name, $selected_center_id, $teacher_id, $level, 
                    $schedule_details, $max_students, $description
                ]);
                
                $success = 'تم إضافة الحلقة بنجاح';
                
                // Redirect to circles list
                set_flash_message('success', $success);
                redirect('pages/circles.php');
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الحلقة: ' . $e->getMessage();
        }
    }
}

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'الحلقات', 'url' => 'circles.php'],
    ['title' => 'إضافة حلقة جديدة']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-circle'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-plus-circle me-2"></i> معلومات الحلقة الجديدة</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="circle_name" class="form-label">اسم الحلقة <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="circle_name" name="circle_name" required value="<?php echo isset($_POST['circle_name']) ? $_POST['circle_name'] : ''; ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="level" class="form-label">المستوى <span class="text-danger">*</span></label>
                    <select class="form-select" id="level" name="level" required>
                        <option value="">-- اختر المستوى --</option>
                        <option value="مبتدئ" <?php echo (isset($_POST['level']) && $_POST['level'] == 'مبتدئ') ? 'selected' : ''; ?>>مبتدئ</option>
                        <option value="متوسط" <?php echo (isset($_POST['level']) && $_POST['level'] == 'متوسط') ? 'selected' : ''; ?>>متوسط</option>
                        <option value="متقدم" <?php echo (isset($_POST['level']) && $_POST['level'] == 'متقدم') ? 'selected' : ''; ?>>متقدم</option>
                        <option value="حفظ كامل" <?php echo (isset($_POST['level']) && $_POST['level'] == 'حفظ كامل') ? 'selected' : ''; ?>>حفظ كامل</option>
                    </select>
                </div>
            </div>
            
            <div class="row">
                <?php if (has_role('system_owner')): ?>
                <div class="col-md-6 mb-3">
                    <label for="center_id" class="form-label">المركز <span class="text-danger">*</span></label>
                    <select class="form-select" id="center_id" name="center_id" required>
                        <option value="">-- اختر المركز --</option>
                        <?php foreach ($centers as $center): ?>
                            <option value="<?php echo $center['center_id']; ?>" <?php echo (isset($_POST['center_id']) && $_POST['center_id'] == $center['center_id']) || $center_id == $center['center_id'] ? 'selected' : ''; ?>>
                                <?php echo $center['center_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <div class="col-md-6 mb-3">
                    <label for="teacher_id" class="form-label">المعلم <span class="text-danger">*</span></label>
                    <select class="form-select" id="teacher_id" name="teacher_id" required>
                        <option value="">-- اختر المعلم --</option>
                        <?php foreach ($teachers as $teacher): ?>
                            <option value="<?php echo $teacher['user_id']; ?>" <?php echo (isset($_POST['teacher_id']) && $_POST['teacher_id'] == $teacher['user_id']) ? 'selected' : ''; ?>>
                                <?php echo $teacher['full_name']; ?> (<?php echo $teacher['email']; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="form-text">إذا لم يكن المعلم موجوداً، يمكنك <a href="add_teacher.php" target="_blank">إضافة معلم جديد</a></div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="schedule_details" class="form-label">مواعيد الحلقة <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="schedule_details" name="schedule_details" rows="3" required><?php echo isset($_POST['schedule_details']) ? $_POST['schedule_details'] : ''; ?></textarea>
                    <div class="form-text">مثال: الأحد والثلاثاء والخميس من الساعة 4 إلى 6 مساءً</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="max_students" class="form-label">الحد الأقصى لعدد الطلاب</label>
                    <input type="number" class="form-control" id="max_students" name="max_students" min="1" max="100" value="<?php echo isset($_POST['max_students']) ? $_POST['max_students'] : '20'; ?>">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">وصف الحلقة</label>
                <textarea class="form-control" id="description" name="description" rows="3"><?php echo isset($_POST['description']) ? $_POST['description'] : ''; ?></textarea>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="circles.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">إضافة الحلقة</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (has_role('system_owner')): ?>
    // Update teachers dropdown when center changes
    document.getElementById('center_id').addEventListener('change', function() {
        const centerId = this.value;
        const teacherSelect = document.getElementById('teacher_id');
        
        // Clear current options
        teacherSelect.innerHTML = '<option value="">-- اختر المعلم --</option>';
        
        if (centerId) {
            // Fetch teachers for selected center
            fetch('api/get_teachers.php?center_id=' + centerId)
                .then(response => response.json())
                .then(data => {
                    data.forEach(teacher => {
                        const option = document.createElement('option');
                        option.value = teacher.user_id;
                        option.textContent = `${teacher.full_name} (${teacher.email})`;
                        teacherSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error fetching teachers:', error));
        }
    });
    <?php endif; ?>
});
</script>

<?php
// Include footer template
include_template('footer');
?>
