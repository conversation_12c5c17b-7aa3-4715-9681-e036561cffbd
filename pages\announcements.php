<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/announcement_stats.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$center_id = isset($_SESSION['center_id']) ? $_SESSION['center_id'] : null;

// Handle announcement actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    // Handle mark as read action
    if ($_GET['action'] == 'mark_read') {
        $announcement_id = intval($_GET['id']);
        $user_id = $_SESSION['user_id'];

        try {
            // Create announcement_reads table if it doesn't exist
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS announcement_reads (
                    read_id INT AUTO_INCREMENT PRIMARY KEY,
                    announcement_id INT NOT NULL,
                    user_id INT NOT NULL,
                    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_announcement_user (announcement_id, user_id)
                )
            ");

            // Insert read record
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO announcement_reads (announcement_id, user_id)
                VALUES (?, ?)
            ");
            $stmt->execute([$announcement_id, $user_id]);

            if (isset($_GET['ajax'])) {
                // Return JSON response for AJAX requests
                header('Content-Type: application/json');
                echo json_encode(['success' => true]);
                exit;
            } else {
                // Redirect back to announcements page
                $success = 'تم تحديد الإعلان كمقروء';
            }
        } catch (PDOException $e) {
            if (isset($_GET['ajax'])) {
                // Return JSON error for AJAX requests
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
                exit;
            } else {
                // Set error message and continue
                $error = 'حدث خطأ: ' . $e->getMessage();
            }
        }
    }
    $action = $_GET['action'];
    $announcement_id = (int)$_GET['id'];

    if ($action === 'delete' && has_role('system_owner')) {
        try {
            // Start transaction
            $pdo->beginTransaction();

            // First delete all announcement reads
            $stmt = $pdo->prepare("DELETE FROM announcement_reads WHERE announcement_id = ?");
            $stmt->execute([$announcement_id]);

            // Then delete the announcement
            $stmt = $pdo->prepare("DELETE FROM announcements WHERE announcement_id = ?");
            $stmt->execute([$announcement_id]);

            // Commit transaction
            $pdo->commit();

            $success = 'تم حذف الإعلان بنجاح';
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $error = 'حدث خطأ أثناء حذف الإعلان: ' . $e->getMessage();
        }
    } elseif ($action === 'toggle' && (has_role('system_owner') || has_role('center_admin'))) {
        try {
            // Toggle announcement active status
            $stmt = $pdo->prepare("UPDATE announcements SET is_active = NOT is_active WHERE announcement_id = ?");
            $stmt->execute([$announcement_id]);
            $success = 'تم تغيير حالة الإعلان بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تغيير حالة الإعلان: ' . $e->getMessage();
        }
    } elseif ($action === 'mark_read') {
        try {
            // Mark announcement as read
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO announcement_reads (announcement_id, user_id)
                VALUES (?, ?)
            ");
            $stmt->execute([$announcement_id, $user_id]);
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تحديث حالة قراءة الإعلان: ' . $e->getMessage();
        }
    }
}

// Get announcements based on user role
try {
    // Check if columns exist in the announcements table
    $media_type_exists = column_exists($pdo, 'announcements', 'media_type');
    $media_url_exists = column_exists($pdo, 'announcements', 'media_url');
    $image_url_exists = column_exists($pdo, 'announcements', 'image_url');
    $background_color_exists = column_exists($pdo, 'announcements', 'background_color');
    $text_color_exists = column_exists($pdo, 'announcements', 'text_color');
    $start_date_exists = column_exists($pdo, 'announcements', 'start_date');
    $end_date_exists = column_exists($pdo, 'announcements', 'end_date');
    $is_public_exists = column_exists($pdo, 'announcements', 'is_public');
    $is_featured_exists = column_exists($pdo, 'announcements', 'is_featured');
    if (has_role('system_owner')) {
        // System owner sees all announcements
        $stmt = $pdo->prepare("
            SELECT a.announcement_id, a.title, a.content, a.sender_user_id, a.target_role, a.target_center_id,
                   a.is_active, a.created_at, a.updated_at,
                   " . ($media_type_exists ? "a.media_type" : "'none'") . " AS media_type,
                   " . ($media_url_exists ? "a.media_url" : "NULL") . " AS media_url,
                   " . ($image_url_exists ? "a.image_url" : "NULL") . " AS image_url,
                   " . ($background_color_exists ? "a.background_color" : "'#ffffff'") . " AS background_color,
                   " . ($text_color_exists ? "a.text_color" : "'#000000'") . " AS text_color,
                   " . ($start_date_exists ? "a.start_date" : "a.created_at") . " AS start_date,
                   " . ($end_date_exists ? "a.end_date" : "DATE_ADD(a.created_at, INTERVAL 30 DAY)") . " AS end_date,
                   " . ($is_public_exists ? "a.is_public" : "0") . " AS is_public,
                   " . ($is_featured_exists ? "a.is_featured" : "0") . " AS is_featured,
                   u.full_name AS sender_name, c.center_name,
                   (SELECT COUNT(*) FROM announcement_reads ar WHERE ar.announcement_id = a.announcement_id) AS read_count,
                   (SELECT COUNT(*) FROM users u JOIN roles r ON u.role_id = r.role_id
                                                     WHERE (r.role_name = a.target_role OR a.target_role = 'all' OR
                                                     (a.target_role = 'center_specific' AND u.center_id = a.target_center_id))) AS target_count,
                   (SELECT COUNT(*) FROM announcement_reads ar WHERE ar.announcement_id = a.announcement_id AND ar.user_id = ?) AS is_read
            FROM announcements a
            JOIN users u ON a.sender_user_id = u.user_id
            LEFT JOIN centers c ON a.target_center_id = c.center_id
            ORDER BY a.created_at DESC
        ");
        $stmt->execute([$user_id]);
    } elseif (has_role('center_admin')) {
        // Center admin sees announcements for all, center_admin role, or their specific center
        $stmt = $pdo->prepare("
            SELECT a.announcement_id, a.title, a.content, a.sender_user_id, a.target_role, a.target_center_id,
                   a.is_active, a.created_at, a.updated_at,
                   " . ($media_type_exists ? "a.media_type" : "'none'") . " AS media_type,
                   " . ($media_url_exists ? "a.media_url" : "NULL") . " AS media_url,
                   " . ($image_url_exists ? "a.image_url" : "NULL") . " AS image_url,
                   " . ($background_color_exists ? "a.background_color" : "'#ffffff'") . " AS background_color,
                   " . ($text_color_exists ? "a.text_color" : "'#000000'") . " AS text_color,
                   " . ($start_date_exists ? "a.start_date" : "a.created_at") . " AS start_date,
                   " . ($end_date_exists ? "a.end_date" : "DATE_ADD(a.created_at, INTERVAL 30 DAY)") . " AS end_date,
                   " . ($is_public_exists ? "a.is_public" : "0") . " AS is_public,
                   " . ($is_featured_exists ? "a.is_featured" : "0") . " AS is_featured,
                   u.full_name AS sender_name, c.center_name,
                   (SELECT COUNT(*) FROM announcement_reads ar WHERE ar.announcement_id = a.announcement_id) AS read_count,
                   (SELECT COUNT(*) FROM users u JOIN roles r ON u.role_id = r.role_id
                                                     WHERE (r.role_name = a.target_role OR a.target_role = 'all' OR
                                                     (a.target_role = 'center_specific' AND u.center_id = ?))) AS target_count,
                   (SELECT COUNT(*) FROM announcement_reads ar WHERE ar.announcement_id = a.announcement_id AND ar.user_id = ?) AS is_read
            FROM announcements a
            JOIN users u ON a.sender_user_id = u.user_id
            LEFT JOIN centers c ON a.target_center_id = c.center_id
            WHERE (a.target_role = 'all' AND (
                      -- Si el remitente es system_owner, mostrar a todos
                      EXISTS (SELECT 1 FROM users u3 JOIN roles r ON u3.role_id = r.role_id
                             WHERE u3.user_id = a.sender_user_id AND r.role_name = 'system_owner')
                      -- Si el remitente es center_admin, mostrar solo a usuarios del mismo centro
                      OR (EXISTS (SELECT 1 FROM users u3 JOIN roles r ON u3.role_id = r.role_id
                                WHERE u3.user_id = a.sender_user_id AND r.role_name = 'center_admin'
                                AND u3.center_id = ?))
                  ))
                  OR a.target_role = 'center_admin'
                  OR (a.target_role = 'center_specific' AND a.target_center_id = ?)
            ORDER BY a.created_at DESC
        ");
        $stmt->execute([$center_id, $center_id, $user_id, $center_id]);
    } else {
        // Regular users see announcements for all, their role, or their specific center
        $stmt = $pdo->prepare("
            SELECT a.announcement_id, a.title, a.content, a.sender_user_id, a.target_role, a.target_center_id,
                   a.is_active, a.created_at, a.updated_at,
                   " . ($media_type_exists ? "a.media_type" : "'none'") . " AS media_type,
                   " . ($media_url_exists ? "a.media_url" : "NULL") . " AS media_url,
                   " . ($image_url_exists ? "a.image_url" : "NULL") . " AS image_url,
                   " . ($background_color_exists ? "a.background_color" : "'#ffffff'") . " AS background_color,
                   " . ($text_color_exists ? "a.text_color" : "'#000000'") . " AS text_color,
                   " . ($start_date_exists ? "a.start_date" : "a.created_at") . " AS start_date,
                   " . ($end_date_exists ? "a.end_date" : "DATE_ADD(a.created_at, INTERVAL 30 DAY)") . " AS end_date,
                   " . ($is_public_exists ? "a.is_public" : "0") . " AS is_public,
                   " . ($is_featured_exists ? "a.is_featured" : "0") . " AS is_featured,
                   u.full_name AS sender_name, c.center_name,
                   (SELECT COUNT(*) FROM announcement_reads ar WHERE ar.announcement_id = a.announcement_id AND ar.user_id = ?) AS is_read
            FROM announcements a
            JOIN users u ON a.sender_user_id = u.user_id
            LEFT JOIN centers c ON a.target_center_id = c.center_id
            WHERE a.is_active = TRUE AND (
                  (a.target_role = 'all' AND (
                      -- Si el remitente es system_owner, mostrar a todos
                      EXISTS (SELECT 1 FROM users u3 JOIN roles r ON u3.role_id = r.role_id
                             WHERE u3.user_id = a.sender_user_id AND r.role_name = 'system_owner')
                      -- Si el remitente es center_admin, mostrar solo a usuarios del mismo centro
                      OR (EXISTS (SELECT 1 FROM users u3 JOIN roles r ON u3.role_id = r.role_id
                                WHERE u3.user_id = a.sender_user_id AND r.role_name = 'center_admin'
                                AND u3.center_id = ?))
                  ))
                  OR a.target_role = ?
                  OR (a.target_role = 'center_specific' AND a.target_center_id = ?))
            ORDER BY a.created_at DESC
        ");
        $stmt->execute([$user_id, $center_id, $role_name, $center_id]);
    }

    $announcements = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الإعلانات: ' . $e->getMessage();
}

// Role names in Arabic
$role_names = [
    'all' => 'الجميع',
    'system_owner' => 'مالك النظام',
    'center_admin' => 'مدراء المراكز',
    'teacher' => 'المعلمين',
    'student' => 'الطلاب',
    'parent' => 'أولياء الأمور',
    'center_specific' => 'مركز محدد'
];
?>

<?php
// Set page title
$page_title = 'الإعلانات';

// Include header
include_once '../includes/header.php';
?>
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-bullhorn me-2"></i> الإعلانات</h1>
            <div>
                <a href="search_announcements.php" class="btn btn-info me-2">
                    <i class="fas fa-search me-1"></i> بحث متقدم
                </a>
                <?php if (has_role('system_owner') || has_role('center_admin')): ?>
                    <a href="system_announcements.php?new=1" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إرسال إعلان جديد
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <?php if (empty($announcements)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد إعلانات متاحة حالياً.
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($announcements as $announcement): ?>
                    <div class="col-md-12 mb-4">
                        <div class="card <?php echo $announcement['is_read'] ? '' : 'border-primary'; ?>">
                            <div class="card-header <?php echo $announcement['is_read'] ? 'bg-light' : 'bg-primary text-white'; ?> d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <?php if (!$announcement['is_read']): ?>
                                        <span class="badge bg-danger me-2">جديد</span>
                                    <?php endif; ?>
                                    <?php echo $announcement['title']; ?>
                                </h5>
                                <div>
                                    <?php if (has_role('system_owner') || has_role('center_admin')): ?>
                                        <a href="system_announcements.php?id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-sm btn-outline-<?php echo $announcement['is_read'] ? 'secondary' : 'light'; ?> me-1">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if (has_role('system_owner')): ?>
                                            <a href="announcements.php?action=toggle&id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-sm btn-outline-<?php echo $announcement['is_read'] ? 'secondary' : 'light'; ?> me-1">
                                                <i class="fas <?php echo $announcement['is_active'] ? 'fa-eye-slash' : 'fa-eye'; ?>"></i>
                                            </a>
                                            <a href="announcements.php?action=delete&id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-sm btn-outline-<?php echo $announcement['is_read'] ? 'secondary' : 'light'; ?>" onclick="return confirm('هل أنت متأكد من حذف هذا الإعلان؟')">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-body" style="background-color: <?php echo isset($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>; color: <?php echo isset($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>;">
                                <?php if (isset($announcement['media_type']) && $announcement['media_type'] !== 'none' && !empty($announcement['media_url'])): ?>
                                    <div class="text-center mb-3">
                                        <?php if ($announcement['media_type'] === 'image'): ?>
                                            <img src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 300px;">
                                        <?php elseif ($announcement['media_type'] === 'video'): ?>
                                            <video controls class="img-fluid rounded" style="max-height: 300px;">
                                                <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="video/mp4">
                                                متصفحك لا يدعم تشغيل الفيديو.
                                            </video>
                                        <?php elseif ($announcement['media_type'] === 'audio'): ?>
                                            <audio controls class="w-100">
                                                <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="audio/mpeg">
                                                متصفحك لا يدعم تشغيل الصوت.
                                            </audio>
                                        <?php endif; ?>
                                    </div>
                                <?php elseif (isset($announcement['image_url']) && !empty($announcement['image_url'])): ?>
                                    <div class="text-center mb-3">
                                        <img src="<?php echo strpos($announcement['image_url'], 'http') === 0 ? $announcement['image_url'] : '../' . $announcement['image_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 300px;">
                                    </div>
                                <?php endif; ?>
                                <div class="mb-3">
                                    <?php echo $announcement['content']; ?>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="text-muted">
                                        <small>
                                            <i class="fas fa-user me-1"></i> <?php echo $announcement['sender_name']; ?> |
                                            <i class="fas fa-calendar-alt me-1"></i> <?php echo date('Y-m-d H:i', strtotime($announcement['created_at'])); ?> |
                                            <i class="fas fa-users me-1"></i>
                                            <?php
                                                echo $role_names[$announcement['target_role']];
                                                if ($announcement['target_role'] == 'center_specific') {
                                                    echo ' (' . $announcement['center_name'] . ')';
                                                }
                                            ?>
                                            <?php if (has_role('system_owner') || has_role('center_admin')): ?>
                                                | <i class="fas fa-eye me-1"></i>
                                                <?php echo isset($announcement['read_count']) ? $announcement['read_count'] : '0'; ?> /
                                                <?php echo isset($announcement['target_count']) ? $announcement['target_count'] : '0'; ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <?php if (!$announcement['is_read']): ?>
                                        <a href="announcements.php?action=mark_read&id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-check me-1"></i> تحديد كمقروء
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </main>

<?php
// Include footer
include_once '../includes/footer.php';
?>
