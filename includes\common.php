<?php
/**
 * Common functions and definitions for the application
 * This file should be included at the beginning of all PHP files
 */

// Define the root path of the application
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', realpath(dirname(__FILE__) . '/../') . '/');
}

// Include session configuration and start session
require_once ROOT_PATH . 'includes/session_config.php';
start_secure_session();

// Include configuration
require_once ROOT_PATH . 'includes/config.php';

// Include database connection if needed
if (!defined('NO_DB_CONNECTION')) {
    require_once ROOT_PATH . 'includes/db_connect.php';

    // Include settings helper
    require_once ROOT_PATH . 'includes/settings_helper.php';

    // Apply system settings
    apply_system_settings($pdo);

    // Include announcements helper
    require_once ROOT_PATH . 'includes/announcements_helper.php';

    // Include WhatsApp service
    require_once ROOT_PATH . 'includes/whatsapp_service.php';

    // Include system logger
    require_once ROOT_PATH . 'includes/system_logger.php';
}

/**
 * Get the root URL of the application
 *
 * @return string The root URL with trailing slash
 */
function get_root_url() {
    // For this specific application, we know the base path is always /qurann/
    // This is a more reliable approach than trying to determine it dynamically
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];

    // Hardcoded base path for reliability
    $base_path = '/qurann/';

    // Return the full URL
    return $protocol . $host . $base_path;
}

/**
 * Include a template file with variables
 *
 * @param string $template_name The name of the template file without .php extension
 * @param array $variables Variables to extract into the template scope
 */
function include_template($template_name, $variables = []) {
    // Extract variables to make them available in the template
    extract($variables);

    // Include the template file
    include ROOT_PATH . 'includes/templates/' . $template_name . '.php';
}

/**
 * Check if user has any of the specified roles
 *
 * @param array|string $roles Single role name or array of role names
 * @return bool True if user has any of the roles, false otherwise
 */
function has_any_role($roles) {
    if (!is_logged_in()) {
        return false;
    }

    if (!is_array($roles)) {
        $roles = [$roles];
    }

    foreach ($roles as $role) {
        if (has_role($role)) {
            return true;
        }
    }

    return false;
}

/**
 * Sanitize user input
 *
 * @param string $input The input to sanitize
 * @return string Sanitized input
 */
function sanitize_input($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

/**
 * Check if user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * Check if user has the specified role
 *
 * @param string $role_name The role name to check
 * @return bool True if user has the role, false otherwise
 */
function has_role($role_name) {
    if (!isset($_SESSION['role_name'])) {
        return false;
    }
    return $_SESSION['role_name'] === $role_name;
}

/**
 * Redirect to the specified path
 *
 * @param string $path The path to redirect to
 */
function redirect($path) {
    header("Location: " . get_root_url() . $path);
    exit;
}

/**
 * Set a flash message to be displayed on the next page
 *
 * @param string $type The type of message (success, danger, warning, info)
 * @param string $message The message text
 */
function set_flash_message($type, $message) {
    $_SESSION['flash_type'] = $type;
    $_SESSION['flash_message'] = $message;
}

/**
 * Check if a media file exists and return appropriate URL
 *
 * @param string $media_url The media URL to check
 * @param string $default_image Default image to use if media doesn't exist
 * @return string Valid media URL or default image
 */
function get_safe_media_url($media_url, $default_image = 'assets/images/default-announcement.svg') {
    if (empty($media_url)) {
        return $default_image;
    }

    // If it's an external URL, return as is
    if (strpos($media_url, 'http') === 0) {
        return $media_url;
    }

    // Check if local file exists
    $file_path = ROOT_PATH . $media_url;
    if (file_exists($file_path)) {
        return $media_url;
    }

    // Return default image if file doesn't exist
    return $default_image;
}

/**
 * Get safe image URL with error handling
 *
 * @param string $image_url The image URL
 * @param string $alt_text Alt text for the image
 * @param string $css_class CSS classes for the image
 * @param string $style Inline styles for the image
 * @return string HTML img tag with error handling
 */
function get_safe_image_tag($image_url, $alt_text = '', $css_class = '', $style = '') {
    $safe_url = get_safe_media_url($image_url);
    $class_attr = !empty($css_class) ? ' class="' . $css_class . '"' : '';
    $style_attr = !empty($style) ? ' style="' . $style . '"' : '';
    $alt_attr = !empty($alt_text) ? ' alt="' . htmlspecialchars($alt_text) . '"' : '';

    return '<img src="' . $safe_url . '"' . $class_attr . $style_attr . $alt_attr . ' onerror="this.src=\'assets/images/default-announcement.svg\'; this.onerror=null;">';
}
?>
