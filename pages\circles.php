<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin') || has_role('teacher'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Page variables
$page_title = 'إدارة الحلقات';
$active_page = 'circles';
$success = '';
$error = '';

// Get user role and ID
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_GET['center_id']) ? (int)$_GET['center_id'] : null);
$level_filter = isset($_GET['level']) ? $_GET['level'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Handle circle actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $circle_id = (int)$_GET['id'];
    
    if ($action === 'delete') {
        try {
            // Check if circle has enrollments
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM student_circle_enrollments WHERE circle_id = ?");
            $stmt->execute([$circle_id]);
            $enrollment_count = $stmt->fetchColumn();
            
            if ($enrollment_count > 0) {
                $error = 'لا يمكن حذف الحلقة لأنها تحتوي على طلاب مسجلين. قم بإلغاء تسجيل الطلاب أولاً.';
            } else {
                // Delete circle
                $stmt = $pdo->prepare("DELETE FROM circles WHERE circle_id = ?");
                $stmt->execute([$circle_id]);
                $success = 'تم حذف الحلقة بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء حذف الحلقة: ' . $e->getMessage();
        }
    } elseif ($action === 'toggle') {
        try {
            // Toggle circle active status
            $stmt = $pdo->prepare("UPDATE circles SET is_active = NOT is_active WHERE circle_id = ?");
            $stmt->execute([$circle_id]);
            $success = 'تم تغيير حالة الحلقة بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تغيير حالة الحلقة: ' . $e->getMessage();
        }
    }
}

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Get circles based on role and filters
try {
    $query = "
        SELECT c.*, ce.center_name, u.full_name AS teacher_name,
               (SELECT COUNT(*) FROM student_circle_enrollments sce WHERE sce.circle_id = c.circle_id) AS student_count
        FROM circles c
        JOIN centers ce ON c.center_id = ce.center_id
        LEFT JOIN users u ON c.teacher_user_id = u.user_id
        WHERE 1=1
    ";
    
    $params = [];
    
    if (has_role('teacher')) {
        $query .= " AND c.teacher_user_id = ?";
        $params[] = $user_id;
    } elseif (has_role('center_admin')) {
        $query .= " AND c.center_id = ?";
        $params[] = $_SESSION['center_id'];
    } elseif (has_role('system_owner') && $center_id) {
        $query .= " AND c.center_id = ?";
        $params[] = $center_id;
    }
    
    if (!empty($level_filter)) {
        $query .= " AND c.level = ?";
        $params[] = $level_filter;
    }
    
    if ($status_filter === 'active') {
        $query .= " AND c.is_active = 1";
    } elseif ($status_filter === 'inactive') {
        $query .= " AND c.is_active = 0";
    }
    
    $query .= " ORDER BY ce.center_name, c.circle_name";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get levels for filter
$levels = ['مبتدئ', 'متوسط', 'متقدم', 'حفظ كامل'];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true
]);

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : (has_role('center_admin') ? 'center_admin_dashboard.php' : 'teacher_dashboard.php')],
    ['title' => 'الحلقات']
];

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-circle',
    'action_button' => has_role('system_owner') || has_role('center_admin') ? [
        'url' => 'add_circle.php',
        'text' => 'إضافة حلقة جديدة',
        'icon' => 'fas fa-plus-circle'
    ] : null
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i> تصفية الحلقات</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <?php if (has_role('system_owner')): ?>
                <div class="col-md-3">
                    <label for="center_id" class="form-label">المركز</label>
                    <select class="form-select" id="center_id" name="center_id">
                        <option value="">جميع المراكز</option>
                        <?php foreach ($centers as $center): ?>
                            <option value="<?php echo $center['center_id']; ?>" <?php echo $center_id == $center['center_id'] ? 'selected' : ''; ?>>
                                <?php echo $center['center_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>
            
            <div class="col-md-3">
                <label for="level" class="form-label">المستوى</label>
                <select class="form-select" id="level" name="level">
                    <option value="">جميع المستويات</option>
                    <?php foreach ($levels as $level): ?>
                        <option value="<?php echo $level; ?>" <?php echo $level_filter === $level ? 'selected' : ''; ?>>
                            <?php echo $level; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">الكل</option>
                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                    <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                </select>
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-1"></i> تصفية
                </button>
                <a href="circles.php" class="btn btn-secondary">
                    <i class="fas fa-redo me-1"></i> إعادة ضبط
                </a>
            </div>
        </form>
    </div>
</div>

<?php if (empty($circles)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> لا يوجد حلقات مطابقة لمعايير البحث.
    </div>
<?php else: ?>
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table id="circlesTable" class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>اسم الحلقة</th>
                            <?php if (has_role('system_owner')): ?>
                                <th>المركز</th>
                            <?php endif; ?>
                            <th>المعلم</th>
                            <th>المستوى</th>
                            <th>الطلاب</th>
                            <th>مواعيد الحلقة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($circles as $circle): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $circle['circle_name']; ?></strong>
                                </td>
                                <?php if (has_role('system_owner')): ?>
                                    <td><?php echo $circle['center_name']; ?></td>
                                <?php endif; ?>
                                <td><?php echo $circle['teacher_name']; ?></td>
                                <td><?php echo $circle['level']; ?></td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?php echo $circle['student_count']; ?> / <?php echo $circle['max_students']; ?>
                                    </span>
                                </td>
                                <td><?php echo $circle['schedule_details']; ?></td>
                                <td>
                                    <?php if ($circle['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="circle_details.php?id=<?php echo $circle['circle_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (has_role('system_owner') || has_role('center_admin')): ?>
                                            <a href="edit_circle.php?id=<?php echo $circle['circle_id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="circles.php?action=toggle&id=<?php echo $circle['circle_id']; ?><?php echo $center_id ? '&center_id=' . $center_id : ''; ?><?php echo $level_filter ? '&level=' . urlencode($level_filter) : ''; ?><?php echo $status_filter ? '&status=' . $status_filter : ''; ?>" class="btn btn-sm btn-secondary" title="<?php echo $circle['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                                <i class="fas <?php echo $circle['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                            </a>
                                            <a href="circles.php?action=delete&id=<?php echo $circle['circle_id']; ?><?php echo $center_id ? '&center_id=' . $center_id : ''; ?><?php echo $level_filter ? '&level=' . urlencode($level_filter) : ''; ?><?php echo $status_filter ? '&status=' . $status_filter : ''; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذه الحلقة؟')">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (has_role('system_owner')): ?>
    // Update form when center changes
    document.getElementById('center_id').addEventListener('change', function() {
        const form = this.form;
        form.submit();
    });
    <?php endif; ?>
});
</script>

<?php
// Include footer template
include_template('footer');
?>
