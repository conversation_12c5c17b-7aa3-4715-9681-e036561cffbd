<?php
// Este script crea la tabla student_assignments directamente

// Include common functions and definitions
require_once 'includes/common.php';

// Verificar si el usuario está autenticado
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('auth/login.php');
    exit;
}

$success = false;
$error = '';

try {
    // Iniciar transacción
    $pdo->beginTransaction();
    
    // Verificar si la tabla ya existe
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'student_assignments'
    ");
    $stmt->execute();
    $table_exists = (bool)$stmt->fetchColumn();
    
    if ($table_exists) {
        $error = 'جدول واجبات الطلاب (student_assignments) موجود بالفعل.';
    } else {
        // Crear la tabla student_assignments
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `student_assignments` (
              `student_assignment_id` INT AUTO_INCREMENT PRIMARY KEY,
              `assignment_id` INT NOT NULL,
              `student_user_id` INT NOT NULL,
              `status` ENUM('pending', 'submitted', 'graded', 'late', 'missed') DEFAULT 'pending',
              `submission_text` TEXT,
              `submission_date` DATETIME DEFAULT NULL,
              `grade` DECIMAL(5,2) DEFAULT NULL,
              `feedback` TEXT,
              `graded_by_user_id` INT DEFAULT NULL,
              `graded_at` DATETIME DEFAULT NULL,
              `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ");
        
        // Crear índices para mejorar el rendimiento
        $pdo->exec("CREATE INDEX idx_student_assignments_assignment_id ON student_assignments(assignment_id);");
        $pdo->exec("CREATE INDEX idx_student_assignments_student_user_id ON student_assignments(student_user_id);");
        $pdo->exec("CREATE INDEX idx_student_assignments_status ON student_assignments(status);");
        
        // Agregar claves foráneas si las tablas referenciadas existen
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'assignments'
        ");
        $stmt->execute();
        $assignments_table_exists = (bool)$stmt->fetchColumn();
        
        if ($assignments_table_exists) {
            $pdo->exec("
                ALTER TABLE `student_assignments`
                ADD CONSTRAINT `fk_student_assignments_assignment_id`
                FOREIGN KEY (`assignment_id`) REFERENCES `assignments`(`assignment_id`) ON DELETE CASCADE
            ");
        }
        
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'users'
        ");
        $stmt->execute();
        $users_table_exists = (bool)$stmt->fetchColumn();
        
        if ($users_table_exists) {
            $pdo->exec("
                ALTER TABLE `student_assignments`
                ADD CONSTRAINT `fk_student_assignments_student_user_id`
                FOREIGN KEY (`student_user_id`) REFERENCES `users`(`user_id`) ON DELETE CASCADE
            ");
            
            $pdo->exec("
                ALTER TABLE `student_assignments`
                ADD CONSTRAINT `fk_student_assignments_graded_by_user_id`
                FOREIGN KEY (`graded_by_user_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
            ");
        }
        
        $success = true;
    }
    
    // Confirmar transacción
    $pdo->commit();
    
} catch (PDOException $e) {
    // Revertir transacción si está activa
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $error = 'حدث خطأ أثناء إنشاء الجدول: ' . $e->getMessage();
}

// Establecer mensaje flash y redirigir
if ($success) {
    set_flash_message('success', 'تم إنشاء جدول واجبات الطلاب (student_assignments) بنجاح.');
} else {
    if (empty($error)) {
        set_flash_message('info', 'لم يتم إنشاء الجدول. قد يكون موجودًا بالفعل.');
    } else {
        set_flash_message('danger', $error);
    }
}

// Redirigir a la página anterior o a la página de asignaciones
$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : get_root_url() . 'pages/assignments.php';
header("Location: $referer");
exit;
?>
