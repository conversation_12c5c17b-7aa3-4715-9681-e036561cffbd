// Custom JavaScript for Quran Circle Management System

// Document ready function
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        $('.alert-dismissible').alert('close');
    }, 5000);

    // Confirm delete actions
    $('.confirm-delete').on('click', function(e) {
        if (!confirm('هل أنت متأكد من رغبتك في الحذف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            e.preventDefault();
        }
    });

    // Toggle password visibility
    $('.toggle-password').on('click', function() {
        var input = $($(this).attr('toggle'));
        if (input.attr('type') == 'password') {
            input.attr('type', 'text');
            $(this).html('<i class="fas fa-eye-slash"></i>');
        } else {
            input.attr('type', 'password');
            $(this).html('<i class="fas fa-eye"></i>');
        }
    });

    // Attendance form handling
    $('.attendance-form').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);

        $.ajax({
            type: form.attr('method'),
            url: form.attr('action'),
            data: form.serialize(),
            success: function(response) {
                var data = JSON.parse(response);
                if (data.success) {
                    // Show success message
                    $('#attendance-message').html('<div class="alert alert-success">' + data.message + '</div>');
                    // Update UI to reflect the change
                    updateAttendanceUI(data.student_id, data.status);
                } else {
                    // Show error message
                    $('#attendance-message').html('<div class="alert alert-danger">' + data.message + '</div>');
                }

                // Auto-hide the message after 3 seconds
                setTimeout(function() {
                    $('#attendance-message').html('');
                }, 3000);
            },
            error: function() {
                $('#attendance-message').html('<div class="alert alert-danger">حدث خطأ أثناء معالجة الطلب.</div>');
            }
        });
    });

    // Function to update attendance UI
    function updateAttendanceUI(studentId, status) {
        var statusCell = $('#status-' + studentId);
        statusCell.removeClass('attendance-present attendance-absent attendance-excused attendance-late');

        switch(status) {
            case 'present':
                statusCell.html('<i class="fas fa-check-circle"></i> حاضر');
                statusCell.addClass('attendance-present');
                break;
            case 'absent_unexcused':
                statusCell.html('<i class="fas fa-times-circle"></i> غائب');
                statusCell.addClass('attendance-absent');
                break;
            case 'absent_excused':
                statusCell.html('<i class="fas fa-exclamation-circle"></i> غائب بعذر');
                statusCell.addClass('attendance-excused');
                break;
            case 'late':
                statusCell.html('<i class="fas fa-clock"></i> متأخر');
                statusCell.addClass('attendance-late');
                break;
        }
    }

    // Memorization progress form handling
    $('#memorization-form').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);

        $.ajax({
            type: form.attr('method'),
            url: form.attr('action'),
            data: form.serialize(),
            success: function(response) {
                var data = JSON.parse(response);
                if (data.success) {
                    // Show success message
                    $('#memorization-message').html('<div class="alert alert-success">' + data.message + '</div>');
                    // Clear form or redirect as needed
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        form[0].reset();
                    }
                } else {
                    // Show error message
                    $('#memorization-message').html('<div class="alert alert-danger">' + data.message + '</div>');
                }
            },
            error: function() {
                $('#memorization-message').html('<div class="alert alert-danger">حدث خطأ أثناء معالجة الطلب.</div>');
            }
        });
    });

    // Dynamic form fields for Surah selection
    $('#surah-select').on('change', function() {
        var surahId = $(this).val();
        if (surahId) {
            $.ajax({
                url: 'includes/get_surah_info.php',
                type: 'GET',
                data: {surah_id: surahId},
                success: function(response) {
                    var data = JSON.parse(response);
                    $('#total-ayat').text(data.total_ayat);
                    $('#ayah-from').attr('max', data.total_ayat);
                    $('#ayah-to').attr('max', data.total_ayat);
                }
            });
        }
    });

    // Fix assignment links to prevent incorrect redirects
    $('a[href*="assignments"]').on('click', function(e) {
        var href = $(this).attr('href');

        // If the link is pointing to assignments.php, redirect to student_assignments.php for students
        if (href.includes('assignments.php') && !href.includes('student_assignments.php')) {
            e.preventDefault();
            var newHref = href.replace('assignments.php', 'student_assignments.php');
            window.location.href = newHref;
        }
    });
});
