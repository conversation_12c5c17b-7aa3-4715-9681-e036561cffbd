-- Parent Student Relations Table
-- This table allows for multiple parents per student with different relationship types

USE quran_circle_management;

-- Create the Parent_Student_Relations table
CREATE TABLE IF NOT EXISTS Parent_Student_Relations (
    relation_id INT AUTO_INCREMENT PRIMARY KEY,
    parent_user_id INT NOT NULL,
    student_user_id INT NOT NULL,
    relation_type VARCHAR(50) DEFAULT 'أب', -- Can be 'أب' (father), 'أم' (mother), 'وصي' (guardian), etc.
    is_primary BOOLEAN DEFAULT TRUE, -- Whether this is the primary parent for contact
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_user_id) REFERENCES users(user_id),
    FOREIGN KEY (student_user_id) REFERENCES users(user_id),
    UNIQUE KEY (parent_user_id, student_user_id)
);

-- Migrate existing parent-student relationships from student_circle_enrollments
INSERT IGNORE INTO Parent_Student_Relations (parent_user_id, student_user_id, relation_type, is_primary)
SELECT DISTINCT parent_user_id, student_user_id, 'أب', TRUE
FROM student_circle_enrollments
WHERE parent_user_id IS NOT NULL;

-- Add index for faster queries
CREATE INDEX idx_parent_student ON Parent_Student_Relations(parent_user_id, student_user_id);
CREATE INDEX idx_student_parent ON Parent_Student_Relations(student_user_id, parent_user_id);
