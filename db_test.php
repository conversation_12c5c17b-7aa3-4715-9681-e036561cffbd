<?php
// Include common functions and definitions
require_once 'includes/common.php';

// Set the page title
$page_title = "اختبار قاعدة البيانات";

// Function to display table structure
function display_table_structure($pdo, $table_name) {
    echo "<h3>هيكل جدول $table_name</h3>";
    
    try {
        $stmt = $pdo->prepare("DESCRIBE $table_name");
        $stmt->execute();
        $columns = $stmt->fetchAll();
        
        echo "<table class='table table-bordered table-striped'>";
        echo "<thead><tr><th>الحقل</th><th>النوع</th><th>Null</th><th>المفتاح</th><th>القيمة الافتراضية</th><th>إضافي</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>خطأ في عرض هيكل الجدول: " . $e->getMessage() . "</div>";
    }
}

// Function to display table data
function display_table_data($pdo, $table_name, $limit = 10) {
    echo "<h3>بيانات جدول $table_name (أول $limit سجلات)</h3>";
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM $table_name LIMIT $limit");
        $stmt->execute();
        $rows = $stmt->fetchAll();
        
        if (count($rows) > 0) {
            echo "<table class='table table-bordered table-striped'>";
            echo "<thead><tr>";
            
            // Display column headers
            foreach (array_keys($rows[0]) as $column) {
                echo "<th>$column</th>";
            }
            
            echo "</tr></thead>";
            echo "<tbody>";
            
            // Display data rows
            foreach ($rows as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
                }
                echo "</tr>";
            }
            
            echo "</tbody></table>";
        } else {
            echo "<div class='alert alert-info'>لا توجد بيانات في الجدول.</div>";
        }
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>خطأ في عرض بيانات الجدول: " . $e->getMessage() . "</div>";
    }
}

// Function to count records in a table
function count_table_records($pdo, $table_name) {
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM $table_name");
        $stmt->execute();
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return "خطأ: " . $e->getMessage();
    }
}

// Get all tables in the database
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    $error = "خطأ في استرجاع قائمة الجداول: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">الرئيسية</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    
    <main class="container py-4">
        <h1><?php echo $page_title; ?></h1>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php else: ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i> تم الاتصال بقاعدة البيانات بنجاح!
            </div>
            
            <h2>إحصائيات قاعدة البيانات</h2>
            <div class="row mb-4">
                <?php foreach ($tables as $table): ?>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $table; ?></h5>
                                <p class="card-text">
                                    <strong>عدد السجلات:</strong> <?php echo count_table_records($pdo, $table); ?>
                                </p>
                                <a href="#<?php echo $table; ?>_structure" class="btn btn-sm btn-primary me-2" data-bs-toggle="collapse" role="button">
                                    عرض الهيكل
                                </a>
                                <a href="#<?php echo $table; ?>_data" class="btn btn-sm btn-info" data-bs-toggle="collapse" role="button">
                                    عرض البيانات
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <?php foreach ($tables as $table): ?>
                <div class="collapse mb-4" id="<?php echo $table; ?>_structure">
                    <div class="card card-body">
                        <?php display_table_structure($pdo, $table); ?>
                    </div>
                </div>
                
                <div class="collapse mb-4" id="<?php echo $table; ?>_data">
                    <div class="card card-body">
                        <?php display_table_data($pdo, $table); ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </main>
    
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">الرئيسية</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
