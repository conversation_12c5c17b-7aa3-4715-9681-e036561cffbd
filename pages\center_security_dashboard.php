<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/center_security.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$page_title = 'لوحة أمان المراكز';
include_once '../includes/header_inner.php';

// Get security statistics
try {
    // Get all centers
    $stmt = $pdo->query("
        SELECT c.*, 
               (SELECT COUNT(*) FROM users u WHERE u.center_id = c.center_id AND u.is_active = 1) as user_count,
               (SELECT COUNT(*) FROM circles ci WHERE ci.center_id = c.center_id AND ci.is_active = 1) as circle_count
        FROM centers c 
        ORDER BY c.center_name
    ");
    $centers = $stmt->fetchAll();

    // Get users without centers
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM users u 
        WHERE u.center_id IS NULL AND u.is_active = 1 AND u.role_id != (SELECT role_id FROM roles WHERE role_name = 'system_owner')
    ");
    $users_without_center = $stmt->fetchColumn();

    // Get recent unauthorized access attempts
    $stmt = $pdo->query("
        SELECT al.*, u.full_name, u.username 
        FROM activity_logs al 
        LEFT JOIN users u ON al.user_id = u.user_id 
        WHERE al.category = 'SECURITY' AND al.action = 'unauthorized_access' 
        ORDER BY al.created_at DESC 
        LIMIT 10
    ");
    $unauthorized_attempts = $stmt->fetchAll();

    // Get center access statistics
    $stmt = $pdo->query("
        SELECT 
            c.center_name,
            COUNT(DISTINCT u.user_id) as total_users,
            COUNT(DISTINCT CASE WHEN al.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN u.user_id END) as active_users_week,
            COUNT(DISTINCT CASE WHEN al.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN u.user_id END) as active_users_month
        FROM centers c
        LEFT JOIN users u ON c.center_id = u.center_id
        LEFT JOIN activity_logs al ON u.user_id = al.user_id
        WHERE c.is_active = 1
        GROUP BY c.center_id, c.center_name
        ORDER BY total_users DESC
    ");
    $center_stats = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = 'خطأ في استرجاع إحصائيات الأمان: ' . $e->getMessage();
}
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-shield-alt me-2"></i> لوحة أمان المراكز</h1>
        <div>
            <a href="secure_user_activity.php" class="btn btn-primary me-2">
                <i class="fas fa-history me-1"></i> الأنشطة الآمنة
            </a>
            <a href="system_owner_dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <!-- Security Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-building fa-2x text-primary mb-2"></i>
                    <h3 class="text-primary"><?php echo count($centers); ?></h3>
                    <p class="mb-0">إجمالي المراكز</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h3 class="text-success"><?php echo array_sum(array_column($centers, 'user_count')); ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <h3 class="text-warning"><?php echo $users_without_center; ?></h3>
                    <p class="mb-0">مستخدمين بدون مركز</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-ban fa-2x text-danger mb-2"></i>
                    <h3 class="text-danger"><?php echo count($unauthorized_attempts); ?></h3>
                    <p class="mb-0">محاولات وصول غير مصرح</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Centers Security Status -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-building me-2"></i>حالة أمان المراكز</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم المركز</th>
                            <th>الحالة</th>
                            <th>عدد المستخدمين</th>
                            <th>عدد الحلقات</th>
                            <th>مستوى الأمان</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($centers as $center): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($center['center_name']); ?></strong>
                                    <br><small class="text-muted">ID: <?php echo $center['center_id']; ?></small>
                                </td>
                                <td>
                                    <span class="badge <?php echo $center['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo $center['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $center['user_count']; ?></span>
                                    مستخدم
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $center['circle_count']; ?></span>
                                    حلقة
                                </td>
                                <td>
                                    <?php
                                    $security_level = 'عالي';
                                    $security_color = 'success';
                                    
                                    if ($center['user_count'] == 0) {
                                        $security_level = 'غير محدد';
                                        $security_color = 'secondary';
                                    } elseif ($center['user_count'] > 50) {
                                        $security_level = 'متوسط';
                                        $security_color = 'warning';
                                    }
                                    ?>
                                    <span class="badge bg-<?php echo $security_color; ?>"><?php echo $security_level; ?></span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="secure_user_activity.php?center=<?php echo $center['center_id']; ?>" class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i> عرض الأنشطة
                                        </a>
                                        <a href="user_operations_tracker.php?center=<?php echo $center['center_id']; ?>" class="btn btn-outline-info">
                                            <i class="fas fa-users"></i> المستخدمين
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Center Activity Statistics -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات نشاط المراكز</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>المركز</th>
                            <th>إجمالي المستخدمين</th>
                            <th>نشطين (آخر أسبوع)</th>
                            <th>نشطين (آخر شهر)</th>
                            <th>معدل النشاط</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($center_stats as $stat): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($stat['center_name']); ?></td>
                                <td><span class="badge bg-primary"><?php echo $stat['total_users']; ?></span></td>
                                <td><span class="badge bg-success"><?php echo $stat['active_users_week']; ?></span></td>
                                <td><span class="badge bg-info"><?php echo $stat['active_users_month']; ?></span></td>
                                <td>
                                    <?php
                                    $activity_rate = $stat['total_users'] > 0 ? round(($stat['active_users_week'] / $stat['total_users']) * 100, 1) : 0;
                                    $rate_color = $activity_rate >= 70 ? 'success' : ($activity_rate >= 40 ? 'warning' : 'danger');
                                    ?>
                                    <span class="badge bg-<?php echo $rate_color; ?>"><?php echo $activity_rate; ?>%</span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent Unauthorized Access Attempts -->
    <?php if (!empty($unauthorized_attempts)): ?>
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>محاولات الوصول غير المصرح بها الأخيرة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>الوصف</th>
                                <th>عنوان IP</th>
                                <th>التاريخ</th>
                                <th>التفاصيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($unauthorized_attempts as $attempt): ?>
                                <tr>
                                    <td>
                                        <?php if ($attempt['full_name']): ?>
                                            <strong><?php echo htmlspecialchars($attempt['full_name']); ?></strong>
                                            <br><small class="text-muted">@<?php echo htmlspecialchars($attempt['username']); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">مستخدم غير معروف</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($attempt['description']); ?></td>
                                    <td>
                                        <code><?php echo htmlspecialchars($attempt['ip_address'] ?? 'غير محدد'); ?></code>
                                    </td>
                                    <td>
                                        <small><?php echo $attempt['created_at']; ?></small>
                                    </td>
                                    <td>
                                        <?php if ($attempt['additional_data']): ?>
                                            <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#details-<?php echo $attempt['log_id']; ?>">
                                                <i class="fas fa-info-circle"></i> تفاصيل
                                            </button>
                                            <div class="collapse mt-2" id="details-<?php echo $attempt['log_id']; ?>">
                                                <pre class="bg-light p-2 small"><?php echo htmlspecialchars($attempt['additional_data']); ?></pre>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Security Recommendations -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-lightbulb me-2"></i>توصيات الأمان</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-check-circle text-success me-2"></i>الممارسات الجيدة</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-shield-alt text-success me-2"></i> عزل بيانات المراكز مفعل</li>
                        <li><i class="fas fa-user-lock text-success me-2"></i> تسجيل محاولات الوصول غير المصرح</li>
                        <li><i class="fas fa-eye text-success me-2"></i> مراقبة أنشطة المستخدمين</li>
                        <li><i class="fas fa-database text-success me-2"></i> حماية قاعدة البيانات</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>نقاط تحتاج انتباه</h6>
                    <ul class="list-unstyled">
                        <?php if ($users_without_center > 0): ?>
                            <li><i class="fas fa-users text-warning me-2"></i> يوجد <?php echo $users_without_center; ?> مستخدم بدون مركز</li>
                        <?php endif; ?>
                        <?php if (count($unauthorized_attempts) > 5): ?>
                            <li><i class="fas fa-ban text-danger me-2"></i> عدد كبير من محاولات الوصول غير المصرح</li>
                        <?php endif; ?>
                        <li><i class="fas fa-clock text-info me-2"></i> مراجعة دورية لصلاحيات المستخدمين</li>
                        <li><i class="fas fa-key text-info me-2"></i> تحديث كلمات المرور بانتظام</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
