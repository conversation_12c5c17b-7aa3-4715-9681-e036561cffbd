<?php
// Simple test file to check if Apache is working correctly
echo "<h1>Apache Test</h1>";
echo "<p>If you can see this, Apache is working correctly!</p>";
echo "<h2>Server Information:</h2>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Server Name: " . $_SERVER['SERVER_NAME'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";

// Display all loaded PHP modules
echo "<h2>Loaded PHP Modules:</h2>";
echo "<pre>";
print_r(get_loaded_extensions());
echo "</pre>";

// Test database connection
echo "<h2>Database Connection Test:</h2>";
try {
    $dsn = "mysql:host=localhost;dbname=quran_circle_management";
    $username = "root";
    $password = "";
    
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color:green'>Database connection successful!</p>";
    
    // Test query
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>Tables in database:</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>" . $table . "</li>";
    }
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Database connection failed: " . $e->getMessage() . "</p>";
}
?>
