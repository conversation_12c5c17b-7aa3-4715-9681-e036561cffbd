<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Page variables
$page_title = 'إضافة معلم جديد';
$active_page = 'teachers';
$success = '';
$error = '';
$center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_GET['center_id']) ? (int)$_GET['center_id'] : null);

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $username = sanitize_input($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $specialization = sanitize_input($_POST['specialization']);
    $qualifications = sanitize_input($_POST['qualifications']);
    $selected_center_id = has_role('center_admin') ? $_SESSION['center_id'] : (int)$_POST['center_id'];

    // Validate required fields
    if (empty($username)) {
        $error = 'يرجى إدخال اسم المستخدم';
    } elseif (empty($password)) {
        $error = 'يرجى إدخال كلمة المرور';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيدها غير متطابقين';
    } elseif (empty($full_name)) {
        $error = 'يرجى إدخال الاسم الكامل';
    } elseif (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (has_role('system_owner') && empty($selected_center_id)) {
        $error = 'يرجى اختيار المركز';
    } else {
        try {
            // Check if username already exists
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->rowCount() > 0) {
                $error = 'اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر';
            } else {
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->rowCount() > 0) {
                    $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد آخر';
                } else {
                    // Get teacher role ID
                    $stmt = $pdo->prepare("SELECT role_id FROM roles WHERE role_name = 'teacher'");
                    $stmt->execute();
                    $role = $stmt->fetch();
                    $role_id = $role['role_id'];

                    // Hash password
                    $password_hash = password_hash($password, PASSWORD_DEFAULT);

                    // Begin transaction
                    $pdo->beginTransaction();

                    // Check if specialization and qualifications columns exist
                    $stmt = $pdo->prepare("
                        SELECT COUNT(*)
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'users'
                        AND COLUMN_NAME = 'specialization'
                    ");
                    $stmt->execute();
                    $specialization_exists = (bool)$stmt->fetchColumn();

                    $stmt = $pdo->prepare("
                        SELECT COUNT(*)
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'users'
                        AND COLUMN_NAME = 'qualifications'
                    ");
                    $stmt->execute();
                    $qualifications_exists = (bool)$stmt->fetchColumn();

                    // Insert new teacher user based on existing columns
                    if ($specialization_exists && $qualifications_exists) {
                        $stmt = $pdo->prepare("
                            INSERT INTO users (
                                username, password_hash, full_name, email, phone_number,
                                specialization, qualifications, role_id, center_id, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                        ");
                        $stmt->execute([
                            $username, $password_hash, $full_name, $email, $phone_number,
                            $specialization, $qualifications, $role_id, $selected_center_id
                        ]);
                    } else {
                        $stmt = $pdo->prepare("
                            INSERT INTO users (
                                username, password_hash, full_name, email, phone_number,
                                role_id, center_id, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                        ");
                        $stmt->execute([
                            $username, $password_hash, $full_name, $email, $phone_number,
                            $role_id, $selected_center_id
                        ]);
                    }

                    $teacher_id = $pdo->lastInsertId();

                    // Handle profile picture upload if provided
                    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                        $upload_dir = '../assets/images/profiles/';

                        // Create directory if it doesn't exist
                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0777, true);
                        }

                        $file_extension = pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION);
                        $new_filename = 'user_' . $teacher_id . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;

                        if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                            // Update user with profile picture URL
                            $profile_picture_url = 'assets/images/profiles/' . $new_filename;
                            $stmt = $pdo->prepare("UPDATE users SET profile_picture_url = ? WHERE user_id = ?");
                            $stmt->execute([$profile_picture_url, $teacher_id]);
                        }
                    }

                    // Commit transaction
                    $pdo->commit();

                    $success = 'تم إضافة المعلم بنجاح';

                    // Redirect to teachers list
                    set_flash_message('success', $success);
                    redirect('pages/teachers.php');
                }
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء إضافة المعلم: ' . $e->getMessage();
        }
    }
}

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'المعلمين', 'url' => 'teachers.php'],
    ['title' => 'إضافة معلم جديد']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-chalkboard-teacher'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-user-plus me-2"></i> معلومات المعلم الجديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" required value="<?php echo isset($_POST['username']) ? $_POST['username'] : ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required value="<?php echo isset($_POST['full_name']) ? $_POST['full_name'] : ''; ?>">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" required value="<?php echo isset($_POST['email']) ? $_POST['email'] : ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="phone_number" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone_number" name="phone_number" value="<?php echo isset($_POST['phone_number']) ? $_POST['phone_number'] : ''; ?>">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="specialization" class="form-label">التخصص</label>
                    <input type="text" class="form-control" id="specialization" name="specialization" value="<?php echo isset($_POST['specialization']) ? $_POST['specialization'] : ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                    <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                </div>
            </div>

            <?php if (has_role('system_owner')): ?>
            <div class="mb-3">
                <label for="center_id" class="form-label">المركز <span class="text-danger">*</span></label>
                <select class="form-select" id="center_id" name="center_id" required>
                    <option value="">-- اختر المركز --</option>
                    <?php foreach ($centers as $center): ?>
                        <option value="<?php echo $center['center_id']; ?>" <?php echo (isset($_POST['center_id']) && $_POST['center_id'] == $center['center_id']) || $center_id == $center['center_id'] ? 'selected' : ''; ?>>
                            <?php echo $center['center_name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php endif; ?>

            <div class="mb-3">
                <label for="qualifications" class="form-label">المؤهلات والخبرات</label>
                <textarea class="form-control" id="qualifications" name="qualifications" rows="3"><?php echo isset($_POST['qualifications']) ? $_POST['qualifications'] : ''; ?></textarea>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="teachers.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">إضافة المعلم</button>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer template
include_template('footer');
?>
