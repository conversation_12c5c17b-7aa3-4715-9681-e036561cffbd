<?php
// Include common functions and definitions
require_once 'includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
    exit;
}

$success = '';
$error = '';

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Check if announcements table exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'announcements'
    ");
    $stmt->execute();
    $table_exists = (bool)$stmt->fetchColumn();
    
    if (!$table_exists) {
        $error = 'جدول الإعلانات غير موجود';
    } else {
        // Check if view_count column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'view_count'
        ");
        $stmt->execute();
        $view_count_exists = (bool)$stmt->fetchColumn();
        
        // Check if click_count column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'click_count'
        ");
        $stmt->execute();
        $click_count_exists = (bool)$stmt->fetchColumn();
        
        // Check if media_type column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'media_type'
        ");
        $stmt->execute();
        $media_type_exists = (bool)$stmt->fetchColumn();
        
        // Check if media_url column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'media_url'
        ");
        $stmt->execute();
        $media_url_exists = (bool)$stmt->fetchColumn();
        
        // Add view_count column if it doesn't exist
        if (!$view_count_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN view_count INT NOT NULL DEFAULT 0");
            $success .= 'تم إضافة عمود view_count بنجاح. ';
        }
        
        // Add click_count column if it doesn't exist
        if (!$click_count_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN click_count INT NOT NULL DEFAULT 0");
            $success .= 'تم إضافة عمود click_count بنجاح. ';
        }
        
        // Add media_type column if it doesn't exist
        if (!$media_type_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN media_type ENUM('image', 'video', 'audio', 'none') NOT NULL DEFAULT 'none'");
            $success .= 'تم إضافة عمود media_type بنجاح. ';
        }
        
        // Add media_url column if it doesn't exist
        if (!$media_url_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN media_url VARCHAR(255) NULL");
            $success .= 'تم إضافة عمود media_url بنجاح. ';
        }
        
        // Update existing announcements with image_url to use media_type and media_url
        if ($media_type_exists && $media_url_exists) {
            $stmt = $pdo->prepare("
                SELECT announcement_id, image_url 
                FROM announcements 
                WHERE image_url IS NOT NULL AND image_url != ''
            ");
            $stmt->execute();
            $announcements_with_images = $stmt->fetchAll();
            
            foreach ($announcements_with_images as $announcement) {
                $stmt = $pdo->prepare("
                    UPDATE announcements 
                    SET media_type = 'image', media_url = ? 
                    WHERE announcement_id = ?
                ");
                $stmt->execute([$announcement['image_url'], $announcement['announcement_id']]);
            }
            
            $success .= 'تم تحديث الإعلانات الحالية لاستخدام أعمدة الوسائط الجديدة. ';
        }
        
        // Create announcement_clicks table if it doesn't exist
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'announcement_clicks'
        ");
        $stmt->execute();
        $clicks_table_exists = (bool)$stmt->fetchColumn();
        
        if (!$clicks_table_exists) {
            $pdo->exec("
                CREATE TABLE announcement_clicks (
                    click_id INT AUTO_INCREMENT PRIMARY KEY,
                    announcement_id INT NOT NULL,
                    user_id INT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent VARCHAR(255) NULL,
                    clicked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
                )
            ");
            
            $success .= 'تم إنشاء جدول نقرات الإعلانات بنجاح. ';
        }
        
        // Create announcement_views table if it doesn't exist
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'announcement_views'
        ");
        $stmt->execute();
        $views_table_exists = (bool)$stmt->fetchColumn();
        
        if (!$views_table_exists) {
            $pdo->exec("
                CREATE TABLE announcement_views (
                    view_id INT AUTO_INCREMENT PRIMARY KEY,
                    announcement_id INT NOT NULL,
                    user_id INT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent VARCHAR(255) NULL,
                    viewed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
                )
            ");
            
            $success .= 'تم إنشاء جدول مشاهدات الإعلانات بنجاح. ';
        }
        
        if (empty($success)) {
            $success = 'جدول الإعلانات بالفعل يحتوي على الأعمدة المطلوبة.';
        }
    }
    
    // Commit transaction
    $pdo->commit();
    
    // Set flash message
    if (!empty($success)) {
        set_flash_message('success', $success);
    }
    
} catch (PDOException $e) {
    // Rollback transaction
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $error = 'حدث خطأ أثناء تحديث جدول الإعلانات: ' . $e->getMessage();
    set_flash_message('danger', $error);
}

// Redirect to announcements page
redirect('pages/system_announcements.php');
?>
