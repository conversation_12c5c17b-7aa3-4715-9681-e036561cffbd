<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has parent role
if (!is_logged_in() || !has_role('parent')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('../auth/login.php');
}

// Get parent information
$parent_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Process adding a new child
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_child') {
    $student_id = isset($_POST['student_id']) ? (int)$_POST['student_id'] : 0;
    $relation_type = isset($_POST['relation_type']) ? sanitize_input($_POST['relation_type']) : 'أب';
    $is_primary = isset($_POST['is_primary']) ? 1 : 0;
    
    if (empty($student_id)) {
        $error = 'يرجى اختيار الطالب';
    } else {
        try {
            // Check if the student exists and is not already linked to this parent
            $stmt = $pdo->prepare("
                SELECT u.user_id, u.full_name 
                FROM users u
                LEFT JOIN Parent_Student_Relations psr ON u.user_id = psr.student_user_id AND psr.parent_user_id = ?
                WHERE u.user_id = ? AND u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
                AND psr.relation_id IS NULL
            ");
            $stmt->execute([$parent_id, $student_id]);
            $student = $stmt->fetch();
            
            if ($student) {
                // If this is set as primary, update all other relations to non-primary
                if ($is_primary) {
                    $stmt = $pdo->prepare("
                        UPDATE Parent_Student_Relations 
                        SET is_primary = FALSE 
                        WHERE parent_user_id = ?
                    ");
                    $stmt->execute([$parent_id]);
                }
                
                // Add the new relation
                $stmt = $pdo->prepare("
                    INSERT INTO Parent_Student_Relations (parent_user_id, student_user_id, relation_type, is_primary)
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$parent_id, $student_id, $relation_type, $is_primary]);
                
                $success = 'تم إضافة الطالب ' . $student['full_name'] . ' بنجاح';
            } else {
                $error = 'الطالب غير موجود أو مرتبط بالفعل بك';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الطالب: ' . $e->getMessage();
        }
    }
}

// Process removing a child
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'remove_child') {
    $relation_id = isset($_POST['relation_id']) ? (int)$_POST['relation_id'] : 0;
    
    if (empty($relation_id)) {
        $error = 'معرف العلاقة غير صالح';
    } else {
        try {
            // Check if the relation exists and belongs to this parent
            $stmt = $pdo->prepare("
                SELECT psr.relation_id, u.full_name 
                FROM Parent_Student_Relations psr
                JOIN users u ON psr.student_user_id = u.user_id
                WHERE psr.relation_id = ? AND psr.parent_user_id = ?
            ");
            $stmt->execute([$relation_id, $parent_id]);
            $relation = $stmt->fetch();
            
            if ($relation) {
                // Remove the relation
                $stmt = $pdo->prepare("
                    DELETE FROM Parent_Student_Relations 
                    WHERE relation_id = ?
                ");
                $stmt->execute([$relation_id]);
                
                $success = 'تم إزالة الطالب ' . $relation['full_name'] . ' بنجاح';
            } else {
                $error = 'العلاقة غير موجودة أو لا تنتمي إليك';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إزالة الطالب: ' . $e->getMessage();
        }
    }
}

// Get children information using the new Parent_Student_Relations table
try {
    $stmt = $pdo->prepare("
        SELECT psr.relation_id, psr.relation_type, psr.is_primary,
               u.user_id, u.full_name, u.profile_picture_url, 
               c.circle_id, c.circle_name, c.level, 
               t.full_name AS teacher_name,
               ce.center_name
        FROM Parent_Student_Relations psr
        JOIN users u ON psr.student_user_id = u.user_id
        LEFT JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id AND sce.status = 'approved'
        LEFT JOIN circles c ON sce.circle_id = c.circle_id
        LEFT JOIN users t ON c.teacher_user_id = t.user_id
        LEFT JOIN centers ce ON c.center_id = ce.center_id
        WHERE psr.parent_user_id = ?
        ORDER BY psr.is_primary DESC, u.full_name
    ");
    $stmt->execute([$parent_id]);
    $children = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الأبناء: ' . $e->getMessage();
}

// Get available students to add (students not already linked to this parent)
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name
        FROM users u
        WHERE u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
        AND u.user_id NOT IN (
            SELECT student_user_id FROM Parent_Student_Relations WHERE parent_user_id = ?
        )
        ORDER BY u.full_name
    ");
    $stmt->execute([$parent_id]);
    $available_students = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب المتاحين: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أبنائي - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="parent_dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="my_children.php">أبنائي</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="messages.php">الرسائل</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <main class="container py-4">
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>أبنائي</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addChildModal">
                <i class="fas fa-plus me-1"></i> إضافة ابن
            </button>
        </div>
        
        <!-- Children List -->
        <div class="row">
            <?php if (empty($children)): ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا يوجد أبناء مسجلين حالياً. يمكنك إضافة ابن من خلال الزر أعلاه.
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($children as $child): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 <?php echo $child['is_primary'] ? 'border-primary' : ''; ?>">
                            <?php if ($child['is_primary']): ?>
                                <div class="card-header bg-primary text-white">
                                    <span><i class="fas fa-star me-1"></i> الابن الرئيسي</span>
                                </div>
                            <?php endif; ?>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <img src="<?php echo !empty($child['profile_picture_url']) ? '../' . $child['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>" 
                                         class="rounded-circle" width="100" height="100" alt="صورة الطالب">
                                    <h5 class="card-title mt-2"><?php echo $child['full_name']; ?></h5>
                                    <span class="badge bg-info"><?php echo $child['relation_type']; ?></span>
                                </div>
                                
                                <div class="mb-3">
                                    <?php if (!empty($child['circle_name'])): ?>
                                        <p class="mb-1"><strong>الحلقة:</strong> <?php echo $child['circle_name']; ?></p>
                                        <p class="mb-1"><strong>المستوى:</strong> <?php echo $child['level']; ?></p>
                                        <p class="mb-1"><strong>المعلم:</strong> <?php echo $child['teacher_name']; ?></p>
                                        <p class="mb-1"><strong>المركز:</strong> <?php echo $child['center_name']; ?></p>
                                    <?php else: ?>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i> هذا الطالب غير مسجل في أي حلقة حالياً.
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="student_details.php?id=<?php echo $child['user_id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-eye me-1"></i> عرض التفاصيل
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#removeChildModal" 
                                            data-relation-id="<?php echo $child['relation_id']; ?>"
                                            data-student-name="<?php echo $child['full_name']; ?>">
                                        <i class="fas fa-trash-alt me-1"></i> إزالة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>
    
    <!-- Add Child Modal -->
    <div class="modal fade" id="addChildModal" tabindex="-1" aria-labelledby="addChildModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addChildModalLabel">إضافة ابن</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_child">
                        
                        <div class="mb-3">
                            <label for="student_id" class="form-label">اختر الطالب</label>
                            <select class="form-select" id="student_id" name="student_id" required>
                                <option value="">-- اختر الطالب --</option>
                                <?php foreach ($available_students as $student): ?>
                                    <option value="<?php echo $student['user_id']; ?>"><?php echo $student['full_name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="relation_type" class="form-label">نوع العلاقة</label>
                            <select class="form-select" id="relation_type" name="relation_type" required>
                                <option value="أب">أب</option>
                                <option value="أم">أم</option>
                                <option value="وصي">وصي</option>
                                <option value="أخ">أخ</option>
                                <option value="أخت">أخت</option>
                                <option value="جد">جد</option>
                                <option value="جدة">جدة</option>
                                <option value="عم">عم</option>
                                <option value="عمة">عمة</option>
                                <option value="خال">خال</option>
                                <option value="خالة">خالة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_primary" name="is_primary" checked>
                            <label class="form-check-label" for="is_primary">
                                تعيين كولي أمر رئيسي
                            </label>
                            <div class="form-text">سيتم استخدام ولي الأمر الرئيسي للتواصل والإشعارات</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Remove Child Modal -->
    <div class="modal fade" id="removeChildModal" tabindex="-1" aria-labelledby="removeChildModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="removeChildModalLabel">إزالة ابن</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="remove_child">
                        <input type="hidden" name="relation_id" id="remove_relation_id">
                        
                        <p>هل أنت متأكد من رغبتك في إزالة <span id="remove_student_name" class="fw-bold"></span> من قائمة أبنائك؟</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i> هذا الإجراء لا يمكن التراجع عنه.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">إزالة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>
    
    <script>
        // Set relation ID and student name in remove modal
        document.addEventListener('DOMContentLoaded', function() {
            const removeChildModal = document.getElementById('removeChildModal');
            if (removeChildModal) {
                removeChildModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const relationId = button.getAttribute('data-relation-id');
                    const studentName = button.getAttribute('data-student-name');
                    
                    document.getElementById('remove_relation_id').value = relationId;
                    document.getElementById('remove_student_name').textContent = studentName;
                });
            }
        });
    </script>
</body>
</html>
