<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Check if teacher ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف المعلم غير صحيح');
    redirect('pages/teachers.php');
}

$teacher_id = (int)$_GET['id'];
$error = '';
$success = '';

// Get teacher information
try {
    $stmt = $pdo->prepare("
        SELECT u.*, c.center_name, r.role_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ? AND r.role_name = 'teacher'
    ");
    $stmt->execute([$teacher_id]);
    $teacher = $stmt->fetch();
    
    if (!$teacher) {
        set_flash_message('danger', 'المعلم غير موجود');
        redirect('pages/teachers.php');
    }
    
    // Check if current user has access to this teacher
    if (has_role('center_admin') && $teacher['center_id'] != $_SESSION['center_id']) {
        set_flash_message('danger', 'غير مصرح لك بالوصول إلى بيانات هذا المعلم');
        redirect('pages/teachers.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المعلم: ' . $e->getMessage();
}

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Check if specialization and qualifications columns exist
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'specialization'
    ");
    $stmt->execute();
    $specialization_exists = (bool)$stmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'qualifications'
    ");
    $stmt->execute();
    $qualifications_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من أعمدة الجدول: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $specialization = $specialization_exists ? sanitize_input($_POST['specialization']) : '';
    $qualifications = $qualifications_exists ? sanitize_input($_POST['qualifications']) : '';
    $selected_center_id = has_role('center_admin') ? $_SESSION['center_id'] : (int)$_POST['center_id'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // New password (optional)
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validate required fields
    if (empty($full_name)) {
        $error = 'يرجى إدخال الاسم الكامل';
    } elseif (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (has_role('system_owner') && empty($selected_center_id)) {
        $error = 'يرجى اختيار المركز';
    } elseif (!empty($new_password) && $new_password !== $confirm_password) {
        $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
    } else {
        try {
            // Check if email already exists for another user
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
            $stmt->execute([$email, $teacher_id]);
            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد آخر';
            } else {
                // Begin transaction
                $pdo->beginTransaction();
                
                // Update teacher information
                if ($specialization_exists && $qualifications_exists) {
                    $stmt = $pdo->prepare("
                        UPDATE users
                        SET full_name = ?, email = ?, phone_number = ?, 
                            specialization = ?, qualifications = ?, 
                            center_id = ?, is_active = ?
                        WHERE user_id = ?
                    ");
                    $stmt->execute([
                        $full_name, $email, $phone_number,
                        $specialization, $qualifications,
                        $selected_center_id, $is_active,
                        $teacher_id
                    ]);
                } else {
                    $stmt = $pdo->prepare("
                        UPDATE users
                        SET full_name = ?, email = ?, phone_number = ?, 
                            center_id = ?, is_active = ?
                        WHERE user_id = ?
                    ");
                    $stmt->execute([
                        $full_name, $email, $phone_number,
                        $selected_center_id, $is_active,
                        $teacher_id
                    ]);
                }
                
                // Update password if provided
                if (!empty($new_password)) {
                    $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE user_id = ?");
                    $stmt->execute([$password_hash, $teacher_id]);
                }
                
                // Handle profile picture upload if provided
                if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/images/profiles/';
                    
                    // Create directory if it doesn't exist
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }
                    
                    $file_extension = pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION);
                    $new_filename = 'user_' . $teacher_id . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_filename;
                    
                    if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                        // Update user with profile picture URL
                        $profile_picture_url = 'assets/images/profiles/' . $new_filename;
                        $stmt = $pdo->prepare("UPDATE users SET profile_picture_url = ? WHERE user_id = ?");
                        $stmt->execute([$profile_picture_url, $teacher_id]);
                    }
                }
                
                // Commit transaction
                $pdo->commit();
                
                $success = 'تم تحديث بيانات المعلم بنجاح';
                
                // Refresh teacher data
                $stmt = $pdo->prepare("
                    SELECT u.*, c.center_name, r.role_name
                    FROM users u
                    JOIN roles r ON u.role_id = r.role_id
                    LEFT JOIN centers c ON u.center_id = c.center_id
                    WHERE u.user_id = ? AND r.role_name = 'teacher'
                ");
                $stmt->execute([$teacher_id]);
                $teacher = $stmt->fetch();
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء تحديث بيانات المعلم: ' . $e->getMessage();
        }
    }
}

// Page variables
$page_title = 'تعديل بيانات المعلم: ' . $teacher['full_name'];
$active_page = 'teachers';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'المعلمين', 'url' => 'teachers.php'],
    ['title' => 'تعديل بيانات المعلم']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-chalkboard-teacher'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-edit me-2"></i> تعديل بيانات المعلم</h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" value="<?php echo $teacher['username']; ?>" readonly disabled>
                    <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required value="<?php echo $teacher['full_name']; ?>">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="new_password" name="new_password">
                    <div class="form-text">اتركها فارغة إذا كنت لا ترغب في تغيير كلمة المرور</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" required value="<?php echo $teacher['email']; ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="phone_number" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone_number" name="phone_number" value="<?php echo $teacher['phone_number']; ?>">
                </div>
            </div>
            
            <?php if ($specialization_exists): ?>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="specialization" class="form-label">التخصص</label>
                    <input type="text" class="form-control" id="specialization" name="specialization" value="<?php echo $teacher['specialization'] ?? ''; ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                    <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                </div>
            </div>
            <?php else: ?>
            <div class="mb-3">
                <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
            </div>
            <?php endif; ?>
            
            <?php if (has_role('system_owner')): ?>
            <div class="mb-3">
                <label for="center_id" class="form-label">المركز <span class="text-danger">*</span></label>
                <select class="form-select" id="center_id" name="center_id" required>
                    <option value="">-- اختر المركز --</option>
                    <?php foreach ($centers as $center): ?>
                        <option value="<?php echo $center['center_id']; ?>" <?php echo $teacher['center_id'] == $center['center_id'] ? 'selected' : ''; ?>>
                            <?php echo $center['center_name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php endif; ?>
            
            <?php if ($qualifications_exists): ?>
            <div class="mb-3">
                <label for="qualifications" class="form-label">المؤهلات والخبرات</label>
                <textarea class="form-control" id="qualifications" name="qualifications" rows="3"><?php echo $teacher['qualifications'] ?? ''; ?></textarea>
            </div>
            <?php endif; ?>
            
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $teacher['is_active'] ? 'checked' : ''; ?>>
                <label class="form-check-label" for="is_active">حساب نشط</label>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="teacher_details.php?id=<?php echo $teacher_id; ?>" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>

<?php if (!empty($teacher['profile_picture_url'])): ?>
<div class="card shadow mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="card-title mb-0"><i class="fas fa-image me-2"></i> الصورة الحالية</h5>
    </div>
    <div class="card-body text-center">
        <img src="<?php echo get_root_url() . $teacher['profile_picture_url']; ?>" alt="صورة المعلم" class="img-fluid rounded" style="max-height: 300px;">
    </div>
</div>
<?php endif; ?>

<?php
// Include footer template
include_template('footer');
?>
