<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';

// Generate test logs if requested
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_logs'])) {
    try {
        // Create some test logs
        $test_logs = [
            ['AUTH', 'تسجيل دخول ناجح للمستخدم: admin', 'INFO'],
            ['AUTH', 'محاولة تسجيل دخول فاشلة للمستخدم: unknown_user', 'WARNING'],
            ['USER', 'إنشاء مستخدم جديد: أحمد محمد', 'INFO'],
            ['USER', 'تحديث بيانات المستخدم: فاطمة علي', 'INFO'],
            ['CENTER', 'إنشاء مركز جديد: مركز النور لتحفيظ القرآن', 'INFO'],
            ['CIRCLE', 'إنشاء حلقة جديدة: حلقة المبتدئين', 'INFO'],
            ['ATTENDANCE', 'تسجيل حضور: محمد أحمد - حلقة المتقدمين - حاضر', 'INFO'],
            ['ATTENDANCE', 'تسجيل حضور: سارة محمد - حلقة المبتدئين - غائب بدون عذر', 'WARNING'],
            ['WHATSAPP', 'إرسال رسالة واتساب نجح: إشعار غياب إلى ولي أمر', 'INFO'],
            ['WHATSAPP', 'إرسال رسالة واتساب فشل: رقم هاتف غير صحيح', 'ERROR'],
            ['CONFIG', 'تغيير إعداد النظام: site_name', 'INFO'],
            ['DATABASE', 'نسخ احتياطي للقاعدة البيانات', 'INFO'],
            ['SECURITY', 'حدث أمني: محاولة وصول غير مصرح بها', 'WARNING'],
            ['SYSTEM', 'خطأ في النظام: فشل في الاتصال بقاعدة البيانات', 'ERROR'],
            ['SYSTEM', 'بدء تشغيل النظام بنجاح', 'INFO'],
        ];
        
        $count = 0;
        foreach ($test_logs as $log) {
            // Add some random additional data for some logs
            $additional_data = null;
            if (rand(1, 3) === 1) {
                $additional_data = [
                    'test_data' => true,
                    'random_value' => rand(1, 100),
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            }
            
            if (log_system_activity($log[0], $log[1], $log[2], $_SESSION['user_id'], $additional_data)) {
                $count++;
            }
            
            // Add some delay to create different timestamps
            usleep(100000); // 0.1 second
        }
        
        $success = "تم إنشاء {$count} سجل تجريبي بنجاح";
        
        // Log this action
        log_system_activity('SYSTEM', "تم إنشاء {$count} سجل تجريبي للاختبار", 'INFO', $_SESSION['user_id']);
        
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء إنشاء السجلات التجريبية: ' . $e->getMessage();
    }
}

// Clear test logs if requested
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['clear_test_logs'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM system_logs WHERE additional_data LIKE '%\"test_data\":true%'");
        $stmt->execute();
        $deleted_count = $stmt->rowCount();
        
        $success = "تم حذف {$deleted_count} سجل تجريبي";
        
        // Log this action
        log_system_activity('SYSTEM', "تم حذف {$deleted_count} سجل تجريبي", 'INFO', $_SESSION['user_id']);
        
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء حذف السجلات التجريبية: ' . $e->getMessage();
    }
}

// Get current log statistics
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_logs");
    $stmt->execute();
    $total_logs = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_logs WHERE additional_data LIKE '%\"test_data\":true%'");
    $stmt->execute();
    $test_logs_count = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT log_level, COUNT(*) as count 
        FROM system_logs 
        GROUP BY log_level 
        ORDER BY count DESC
    ");
    $stmt->execute();
    $level_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $total_logs = 0;
    $test_logs_count = 0;
    $level_stats = [];
}

// Page title
$page_title = 'اختبار نظام السجلات';

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-vial me-2"></i> اختبار نظام السجلات</h1>
        <div>
            <a href="system_logs.php" class="btn btn-primary me-2">
                <i class="fas fa-list me-1"></i> عرض السجلات
            </a>
            <a href="system_owner_dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo number_format($total_logs); ?></h4>
                            <p class="mb-0">إجمالي السجلات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo number_format($test_logs_count); ?></h4>
                            <p class="mb-0">السجلات التجريبية</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-flask fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo number_format($total_logs - $test_logs_count); ?></h4>
                            <p class="mb-0">السجلات الحقيقية</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-plus me-2"></i>إنشاء سجلات تجريبية</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        سيتم إنشاء مجموعة من السجلات التجريبية لاختبار نظام السجلات. 
                        هذه السجلات ستحتوي على أمثلة مختلفة من الأنشطة والأحداث.
                    </p>
                    <form method="POST">
                        <button type="submit" name="generate_logs" class="btn btn-primary">
                            <i class="fas fa-magic me-1"></i>إنشاء سجلات تجريبية
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-trash me-2"></i>حذف السجلات التجريبية</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        حذف جميع السجلات التجريبية من قاعدة البيانات. 
                        هذا الإجراء لن يؤثر على السجلات الحقيقية للنظام.
                    </p>
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من حذف جميع السجلات التجريبية؟')">
                        <button type="submit" name="clear_test_logs" class="btn btn-warning">
                            <i class="fas fa-trash me-1"></i>حذف السجلات التجريبية
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Level Statistics -->
    <?php if (!empty($level_stats)): ?>
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات مستويات السجلات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($level_stats as $stat): ?>
                                <?php
                                $bg_class = '';
                                $icon = '';
                                switch ($stat['log_level']) {
                                    case 'CRITICAL':
                                        $bg_class = 'bg-danger';
                                        $icon = 'fas fa-skull';
                                        break;
                                    case 'ERROR':
                                        $bg_class = 'bg-danger';
                                        $icon = 'fas fa-times-circle';
                                        break;
                                    case 'WARNING':
                                        $bg_class = 'bg-warning';
                                        $icon = 'fas fa-exclamation-triangle';
                                        break;
                                    case 'INFO':
                                        $bg_class = 'bg-info';
                                        $icon = 'fas fa-info-circle';
                                        break;
                                    case 'DEBUG':
                                        $bg_class = 'bg-secondary';
                                        $icon = 'fas fa-bug';
                                        break;
                                    default:
                                        $bg_class = 'bg-primary';
                                        $icon = 'fas fa-circle';
                                }
                                ?>
                                <div class="col-md-2 mb-3">
                                    <div class="card <?php echo $bg_class; ?> text-white text-center">
                                        <div class="card-body py-3">
                                            <i class="<?php echo $icon; ?> fa-2x mb-2"></i>
                                            <h5><?php echo number_format($stat['count']); ?></h5>
                                            <small><?php echo $stat['log_level']; ?></small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Test Functions -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-code me-2"></i>اختبار دوال التسجيل</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">يمكنك اختبار دوال التسجيل المختلفة:</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>دوال المصادقة:</h6>
                            <ul class="list-unstyled">
                                <li><code>log_auth_event()</code> - تسجيل أحداث المصادقة</li>
                                <li><code>log_security_event()</code> - تسجيل الأحداث الأمنية</li>
                            </ul>
                            
                            <h6>دوال إدارة المستخدمين:</h6>
                            <ul class="list-unstyled">
                                <li><code>log_user_event()</code> - تسجيل أحداث المستخدمين</li>
                                <li><code>log_center_event()</code> - تسجيل أحداث المراكز</li>
                                <li><code>log_circle_event()</code> - تسجيل أحداث الحلقات</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>دوال النشاط:</h6>
                            <ul class="list-unstyled">
                                <li><code>log_attendance_event()</code> - تسجيل أحداث الحضور</li>
                                <li><code>log_whatsapp_event()</code> - تسجيل أحداث الواتساب</li>
                            </ul>
                            
                            <h6>دوال النظام:</h6>
                            <ul class="list-unstyled">
                                <li><code>log_config_event()</code> - تسجيل تغييرات الإعدادات</li>
                                <li><code>log_database_event()</code> - تسجيل أحداث قاعدة البيانات</li>
                                <li><code>log_system_error()</code> - تسجيل أخطاء النظام</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
