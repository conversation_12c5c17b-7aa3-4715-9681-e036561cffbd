<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';

// Define settings table if not exists
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_settings (
            setting_key VARCHAR(100) PRIMARY KEY,
            setting_value TEXT,
            setting_description TEXT,
            setting_group VARCHAR(50),
            is_public BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء إنشاء جدول الإعدادات: ' . $e->getMessage();
}

// Default settings
$default_settings = [
    // Appearance settings
    ['primary_color', '#0d6efd', 'اللون الرئيسي', 'appearance', true],
    ['logo_url', 'assets/images/logo.png', 'رابط الشعار', 'appearance', true],
    ['footer_text', 'جميع الحقوق محفوظة © نظام إدارة حلقات تحفيظ القرآن', 'نص التذييل', 'appearance', true],
    ['home_image_url', 'assets/images/quran-study.jpg', 'رابط الصورة الرئيسية', 'appearance', true],
];

// Insert default settings if they don't exist
try {
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO system_settings
        (setting_key, setting_value, setting_description, setting_group, is_public)
        VALUES (?, ?, ?, ?, ?)
    ");

    foreach ($default_settings as $setting) {
        $stmt->execute($setting);
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء إدخال الإعدادات الافتراضية: ' . $e->getMessage();
}

// Get current settings
try {
    $stmt = $pdo->query("SELECT * FROM system_settings ORDER BY setting_group, setting_key");
    $settings = $stmt->fetchAll();

    // Group settings by category
    $grouped_settings = [];
    foreach ($settings as $setting) {
        $group = $setting['setting_group'];
        if (!isset($grouped_settings[$group])) {
            $grouped_settings[$group] = [];
        }
        $grouped_settings[$group][] = $setting;
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الإعدادات: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    try {
        $pdo->beginTransaction();

        // Update logo URL if provided
        if (isset($_POST['logo_url']) && !empty($_POST['logo_url'])) {
            $logo_url = sanitize_input($_POST['logo_url']);
            $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'logo_url'");
            $stmt->execute([$logo_url]);
        }

        // Update primary color if provided
        if (isset($_POST['primary_color']) && !empty($_POST['primary_color'])) {
            $primary_color = sanitize_input($_POST['primary_color']);
            $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'primary_color'");
            $stmt->execute([$primary_color]);
        }

        $pdo->commit();
        $success = 'تم حفظ الإعدادات بنجاح';

        // Refresh settings after update
        $stmt = $pdo->query("SELECT * FROM system_settings ORDER BY setting_group, setting_key");
        $settings = $stmt->fetchAll();

        // Group settings by category
        $grouped_settings = [];
        foreach ($settings as $setting) {
            $group = $setting['setting_group'];
            if (!isset($grouped_settings[$group])) {
                $grouped_settings[$group] = [];
            }
            $grouped_settings[$group][] = $setting;
        }
    } catch (PDOException $e) {
        $pdo->rollBack();
        $error = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// Handle logo upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
    try {
        $upload_dir = '../assets/images/';

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $file_extension = pathinfo($_FILES['logo_file']['name'], PATHINFO_EXTENSION);
        $new_filename = 'new_logo.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;
        $logo_url = 'assets/images/' . $new_filename;

        if (move_uploaded_file($_FILES['logo_file']['tmp_name'], $upload_path)) {
            // Update setting in database
            $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'logo_url'");
            $stmt->execute([$logo_url]);

            $success = 'تم تحميل الشعار وحفظ الإعدادات بنجاح';

            // Refresh settings
            $stmt = $pdo->query("SELECT * FROM system_settings WHERE setting_key = 'logo_url'");
            $logo_setting = $stmt->fetch();

            if ($logo_setting) {
                foreach ($grouped_settings['appearance'] as &$setting) {
                    if ($setting['setting_key'] === 'logo_url') {
                        $setting['setting_value'] = $logo_setting['setting_value'];
                        break;
                    }
                }
            }
        } else {
            $error = 'حدث خطأ أثناء تحميل الشعار';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء تحديث إعدادات الشعار: ' . $e->getMessage();
    }
}

// Get logo URL and primary color
$logo_url = isset($grouped_settings['appearance']) ?
    array_values(array_filter($grouped_settings['appearance'], function($setting) {
        return $setting['setting_key'] === 'logo_url';
    }))[0]['setting_value'] ?? 'assets/images/logo.png' : 'assets/images/logo.png';

$primary_color = isset($grouped_settings['appearance']) ?
    array_values(array_filter($grouped_settings['appearance'], function($setting) {
        return $setting['setting_key'] === 'primary_color';
    }))[0]['setting_value'] ?? '#0d6efd' : '#0d6efd';

// Page title
$page_title = 'إعدادات النظام';

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-cogs me-2"></i> إعدادات النظام</h1>
        <a href="system_owner_dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
        </a>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-cogs me-2"></i> إعدادات النظام</h5>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="appearance-tab" data-bs-toggle="tab"
                                data-bs-target="#appearance" type="button" role="tab"
                                aria-controls="appearance" aria-selected="true">
                                <i class="fas fa-palette me-1"></i> إعدادات المظهر
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="general-tab" data-bs-toggle="tab"
                                data-bs-target="#general" type="button" role="tab"
                                aria-controls="general" aria-selected="false">
                                <i class="fas fa-cog me-1"></i> إعدادات عامة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="memorization-tab" data-bs-toggle="tab"
                                data-bs-target="#memorization" type="button" role="tab"
                                aria-controls="memorization" aria-selected="false">
                                <i class="fas fa-book-open me-1"></i> إعدادات الحفظ
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="registration-tab" data-bs-toggle="tab"
                                data-bs-target="#registration" type="button" role="tab"
                                aria-controls="registration" aria-selected="false">
                                <i class="fas fa-user-plus me-1"></i> إعدادات التسجيل
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="settingsTabsContent">
                        <!-- Appearance Tab -->
                        <div class="tab-pane fade show active" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label fw-bold">معاينة الشعار</label>
                                            <div class="card p-3 text-center">
                                                <img src="<?php echo '../' . $logo_url; ?>" alt="شعار الموقع" class="img-fluid mb-2" style="max-height: 100px;" id="logo_preview">
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('logo_file').click()">
                                                        <i class="fas fa-upload me-1"></i> تحميل شعار جديد
                                                    </button>
                                                    <input type="file" id="logo_file" name="logo_file" style="display: none;" accept="image/*" onchange="previewLogo(this)">
                                                </div>
                                            </div>
                                            <input type="hidden" name="logo_url" id="logo_url" value="<?php echo $logo_url; ?>">
                                        </div>

                                        <div class="mb-4">
                                            <label class="form-label fw-bold">لون النظام الرئيسي</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" value="<?php echo $primary_color; ?>">
                                                <input type="text" class="form-control" id="color_text" value="<?php echo $primary_color; ?>" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label class="form-label fw-bold">معاينة اللون</label>
                                            <div class="card p-3">
                                                <div class="p-3 rounded text-white mb-3" id="color_preview" style="background-color: <?php echo $primary_color; ?>">
                                                    عنوان بلون النظام الرئيسي
                                                </div>
                                                <button type="button" class="btn" id="btn_preview" style="background-color: <?php echo $primary_color; ?>; color: white;">
                                                    زر بلون النظام الرئيسي
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-3">
                                    <button type="submit" name="save_settings" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> حفظ الإعدادات
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Other tabs can be added here -->
                        <div class="tab-pane fade" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> سيتم إضافة إعدادات عامة هنا في التحديثات القادمة.
                            </div>
                        </div>

                        <div class="tab-pane fade" id="memorization" role="tabpanel" aria-labelledby="memorization-tab">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> سيتم إضافة إعدادات الحفظ هنا في التحديثات القادمة.
                            </div>
                        </div>

                        <div class="tab-pane fade" id="registration" role="tabpanel" aria-labelledby="registration-tab">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> سيتم إضافة إعدادات التسجيل هنا في التحديثات القادمة.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Preview logo before upload
    function previewLogo(input) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();

            reader.onload = function(e) {
                document.getElementById('logo_preview').src = e.target.result;
            }

            reader.readAsDataURL(input.files[0]);
        }
    }

    // Update color preview when color picker changes
    document.addEventListener('DOMContentLoaded', function() {
        const colorPicker = document.getElementById('primary_color');
        const colorText = document.getElementById('color_text');
        const colorPreview = document.getElementById('color_preview');
        const btnPreview = document.getElementById('btn_preview');

        colorPicker.addEventListener('input', function() {
            const colorValue = this.value;
            colorText.value = colorValue;
            colorPreview.style.backgroundColor = colorValue;
            btnPreview.style.backgroundColor = colorValue;
        });
    });
</script>

<?php
// Include footer
include_once '../includes/footer_inner.php';
?>
