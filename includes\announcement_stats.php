<?php
/**
 * Announcement Statistics Helper Functions
 *
 * This file contains functions for tracking and managing announcement statistics
 * such as views and clicks.
 */

/**
 * Record a view for an announcement
 *
 * @param int $announcement_id The ID of the announcement
 * @return bool True if the view was recorded successfully, false otherwise
 */
function record_announcement_view($announcement_id) {
    global $pdo;

    try {
        // Get user information
        $user_id = is_logged_in() ? $_SESSION['user_id'] : null;
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null;

        // Check if this view has already been recorded in the last hour
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM announcement_views
            WHERE announcement_id = ?
            AND (user_id = ? OR (user_id IS NULL AND ip_address = ?))
            AND viewed_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$announcement_id, $user_id, $ip_address]);
        $already_viewed = (bool)$stmt->fetchColumn();

        if (!$already_viewed) {
            // Record the view in the announcement_views table
            $stmt = $pdo->prepare("
                INSERT INTO announcement_views (announcement_id, user_id, ip_address, user_agent)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$announcement_id, $user_id, $ip_address, $user_agent]);

            // Increment the view_count in the announcements table
            $stmt = $pdo->prepare("
                UPDATE announcements
                SET view_count = view_count + 1
                WHERE announcement_id = ?
            ");
            $stmt->execute([$announcement_id]);

            return true;
        }

        return false;
    } catch (PDOException $e) {
        error_log('Error recording announcement view: ' . $e->getMessage());
        return false;
    }
}

/**
 * Record a click for an announcement
 *
 * @param int $announcement_id The ID of the announcement
 * @return bool True if the click was recorded successfully, false otherwise
 */
function record_announcement_click($announcement_id) {
    global $pdo;

    try {
        // Get user information
        $user_id = is_logged_in() ? $_SESSION['user_id'] : null;
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : null;

        // Check if this click has already been recorded in the last hour
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM announcement_clicks
            WHERE announcement_id = ?
            AND (user_id = ? OR (user_id IS NULL AND ip_address = ?))
            AND clicked_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$announcement_id, $user_id, $ip_address]);
        $already_clicked = (bool)$stmt->fetchColumn();

        if (!$already_clicked) {
            // Record the click in the announcement_clicks table
            $stmt = $pdo->prepare("
                INSERT INTO announcement_clicks (announcement_id, user_id, ip_address, user_agent)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$announcement_id, $user_id, $ip_address, $user_agent]);

            // Increment the click_count in the announcements table
            $stmt = $pdo->prepare("
                UPDATE announcements
                SET click_count = click_count + 1
                WHERE announcement_id = ?
            ");
            $stmt->execute([$announcement_id]);

            return true;
        }

        return false;
    } catch (PDOException $e) {
        error_log('Error recording announcement click: ' . $e->getMessage());
        return false;
    }
}

/**
 * Function to check if a column exists in a table
 *
 * @param PDO $pdo The PDO connection
 * @param string $table The table name
 * @param string $column The column name
 * @return bool True if the column exists, false otherwise
 */
function column_exists($pdo, $table, $column) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = ?
        AND COLUMN_NAME = ?
    ");
    $stmt->execute([$table, $column]);
    return (bool)$stmt->fetchColumn();
}

/**
 * Function to check if a table exists
 *
 * @param PDO $pdo The PDO connection
 * @param string $table The table name
 * @return bool True if the table exists, false otherwise
 */
function table_exists($pdo, $table) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.TABLES
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = ?
    ");
    $stmt->execute([$table]);
    return (bool)$stmt->fetchColumn();
}

/**
 * Get statistics for an announcement
 *
 * @param int $announcement_id The ID of the announcement
 * @return array An array containing the statistics
 */
function get_announcement_stats($announcement_id) {
    global $pdo;

    try {
        // Check if columns and tables exist
        $view_count_exists = column_exists($pdo, 'announcements', 'view_count');
        $click_count_exists = column_exists($pdo, 'announcements', 'click_count');
        $views_table_exists = table_exists($pdo, 'announcement_views');
        $clicks_table_exists = table_exists($pdo, 'announcement_clicks');

        // Default values
        $view_count = 0;
        $click_count = 0;
        $view_stats_by_date = [];
        $click_stats_by_date = [];

        // Get basic stats from the announcements table if columns exist
        if ($view_count_exists || $click_count_exists) {
            $select_fields = [];
            if ($view_count_exists) {
                $select_fields[] = "view_count";
            } else {
                $select_fields[] = "0 AS view_count";
            }

            if ($click_count_exists) {
                $select_fields[] = "click_count";
            } else {
                $select_fields[] = "0 AS click_count";
            }

            $stmt = $pdo->prepare("
                SELECT " . implode(", ", $select_fields) . "
                FROM announcements
                WHERE announcement_id = ?
            ");
            $stmt->execute([$announcement_id]);
            $basic_stats = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($basic_stats) {
                $view_count = isset($basic_stats['view_count']) ? $basic_stats['view_count'] : 0;
                $click_count = isset($basic_stats['click_count']) ? $basic_stats['click_count'] : 0;
            }
        }

        // Get view stats by date if table exists
        if ($views_table_exists) {
            $stmt = $pdo->prepare("
                SELECT DATE(viewed_at) AS date, COUNT(*) AS count
                FROM announcement_views
                WHERE announcement_id = ?
                GROUP BY DATE(viewed_at)
                ORDER BY date DESC
                LIMIT 30
            ");
            $stmt->execute([$announcement_id]);
            $view_stats_by_date = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        // Get click stats by date if table exists
        if ($clicks_table_exists) {
            $stmt = $pdo->prepare("
                SELECT DATE(clicked_at) AS date, COUNT(*) AS count
                FROM announcement_clicks
                WHERE announcement_id = ?
                GROUP BY DATE(clicked_at)
                ORDER BY date DESC
                LIMIT 30
            ");
            $stmt->execute([$announcement_id]);
            $click_stats_by_date = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        // Calculate click-through rate (CTR)
        $ctr = ($view_count > 0) ? round(($click_count / $view_count) * 100, 2) : 0;

        return [
            'view_count' => $view_count,
            'click_count' => $click_count,
            'ctr' => $ctr,
            'view_stats_by_date' => $view_stats_by_date,
            'click_stats_by_date' => $click_stats_by_date
        ];
    } catch (PDOException $e) {
        error_log('Error getting announcement stats: ' . $e->getMessage());
        return [
            'view_count' => 0,
            'click_count' => 0,
            'ctr' => 0,
            'view_stats_by_date' => [],
            'click_stats_by_date' => []
        ];
    }
}

/**
 * Get the most viewed announcements
 *
 * @param int $limit The maximum number of announcements to return
 * @return array An array of announcements
 */
function get_most_viewed_announcements($limit = 5) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT a.*, u.full_name AS sender_name
            FROM announcements a
            JOIN users u ON a.sender_user_id = u.user_id
            WHERE a.is_active = TRUE
            AND CURRENT_DATE BETWEEN a.start_date AND a.end_date
            ORDER BY a.view_count DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log('Error getting most viewed announcements: ' . $e->getMessage());
        return [];
    }
}

/**
 * Get the most clicked announcements
 *
 * @param int $limit The maximum number of announcements to return
 * @return array An array of announcements
 */
function get_most_clicked_announcements($limit = 5) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT a.*, u.full_name AS sender_name
            FROM announcements a
            JOIN users u ON a.sender_user_id = u.user_id
            WHERE a.is_active = TRUE
            AND CURRENT_DATE BETWEEN a.start_date AND a.end_date
            ORDER BY a.click_count DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log('Error getting most clicked announcements: ' . $e->getMessage());
        return [];
    }
}
?>
