<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has teacher role
if (!is_logged_in() || !has_role('teacher')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$teacher_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Get teacher's circles
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name, c.description, c.level, c.schedule_details,
               c.max_students, c.start_date, c.end_date, c.is_active,
               COUNT(sce.enrollment_id) AS student_count
        FROM circles c
        LEFT JOIN student_circle_enrollments sce ON c.circle_id = sce.circle_id AND sce.status = 'approved'
        WHERE c.teacher_user_id = ?
        GROUP BY c.circle_id
        ORDER BY c.is_active DESC, c.circle_name
    ");
    $stmt->execute([$teacher_id]);
    $circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get center information
try {
    $stmt = $pdo->prepare("
        SELECT center_id, center_name
        FROM centers
        WHERE center_id = ?
    ");
    $stmt->execute([$_SESSION['center_id']]);
    $center = $stmt->fetch();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المركز: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حلقاتي - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <?php include_once '../includes/header.php'; ?>

    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>حلقاتي</h1>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <?php if (isset($center)): ?>
            <div class="alert alert-info">
                <i class="fas fa-building me-2"></i> أنت تعمل في مركز: <strong><?php echo $center['center_name']; ?></strong>
            </div>
        <?php endif; ?>

        <?php if (empty($circles)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i> لا توجد حلقات مسجلة لك حالياً.
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($circles as $circle): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 <?php echo $circle['is_active'] ? '' : 'bg-light'; ?>">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0"><?php echo $circle['circle_name']; ?></h5>
                                <?php if ($circle['is_active']): ?>
                                    <span class="badge bg-success">نشطة</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشطة</span>
                                <?php endif; ?>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($circle['description'])): ?>
                                    <p class="card-text"><?php echo $circle['description']; ?></p>
                                <?php endif; ?>

                                <div class="mb-3">
                                    <strong>المستوى:</strong> <?php echo $circle['level']; ?>
                                </div>

                                <div class="mb-3">
                                    <strong>المواعيد:</strong> <?php echo $circle['schedule_details']; ?>
                                </div>

                                <div class="mb-3">
                                    <strong>الطلاب:</strong>
                                    <span class="badge bg-info"><?php echo $circle['student_count']; ?></span>
                                    <?php if (!empty($circle['max_students'])): ?>
                                        / <?php echo $circle['max_students']; ?>
                                    <?php endif; ?>
                                </div>

                                <?php if (!empty($circle['start_date'])): ?>
                                    <div class="mb-3">
                                        <strong>تاريخ البداية:</strong> <?php echo date('Y-m-d', strtotime($circle['start_date'])); ?>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($circle['end_date'])): ?>
                                    <div class="mb-3">
                                        <strong>تاريخ النهاية:</strong> <?php echo date('Y-m-d', strtotime($circle['end_date'])); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="circle_details.php?id=<?php echo $circle['circle_id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-info-circle me-1"></i> التفاصيل
                                    </a>
                                    <div>
                                        <a href="attendance.php?circle_id=<?php echo $circle['circle_id']; ?>" class="btn btn-info">
                                            <i class="fas fa-calendar-check me-1"></i> الحضور
                                        </a>
                                        <a href="memorization.php?circle_id=<?php echo $circle['circle_id']; ?>" class="btn btn-success">
                                            <i class="fas fa-book-reader me-1"></i> التسميع
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <?php include_once '../includes/footer.php'; ?>
</body>
</html>
