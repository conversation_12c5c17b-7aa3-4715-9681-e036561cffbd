<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has admin role
if (!is_logged_in() || !has_any_role(['system_owner', 'center_admin'])) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_whatsapp'])) {
        // Test WhatsApp configuration
        $test_phone = $_POST['test_phone'] ?? '';
        $test_message = $_POST['test_message'] ?? 'رسالة تجريبية من نظام إدارة حلقات تحفيظ القرآن الكريم';
        
        if (empty($test_phone)) {
            $error = 'يرجى إدخال رقم الهاتف للاختبار';
        } else {
            $result = WhatsAppService::sendMessage($test_phone, $test_message);
            
            if ($result['success']) {
                $success = 'تم إرسال الرسالة التجريبية بنجاح!';
            } else {
                $error = 'فشل في إرسال الرسالة: ' . $result['message'];
            }
        }
    }
}

// Check WhatsApp configuration status
$config_errors = validate_whatsapp_config();
$is_configured = empty($config_errors);

// Page variables
$page_title = 'إعدادات الواتساب';
$active_page = 'whatsapp_settings';

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fab fa-whatsapp'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <!-- Configuration Status -->
        <div class="card mb-4">
            <div class="card-header <?php echo $is_configured ? 'bg-success' : 'bg-warning'; ?> text-white">
                <h5 class="card-title mb-0">
                    <i class="fab fa-whatsapp me-2"></i>
                    حالة إعدادات الواتساب
                </h5>
            </div>
            <div class="card-body">
                <?php if ($is_configured): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تكوين الواتساب بنجاح! الخدمة جاهزة للاستخدام.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <strong>الخدمة المستخدمة:</strong> <?php echo strtoupper(WHATSAPP_SERVICE); ?>
                        </div>
                        <div class="col-md-6">
                            <strong>حالة الإشعارات:</strong> 
                            <?php if (WHATSAPP_NOTIFICATIONS_ENABLED): ?>
                                <span class="badge bg-success">مفعلة</span>
                            <?php else: ?>
                                <span class="badge bg-danger">معطلة</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <strong>إشعارات الحضور:</strong>
                            <?php if (WHATSAPP_ATTENDANCE_NOTIFICATIONS): ?>
                                <span class="badge bg-success">مفعلة</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">معطلة</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <strong>إشعارات الواجبات:</strong>
                            <?php if (WHATSAPP_ASSIGNMENT_NOTIFICATIONS): ?>
                                <span class="badge bg-success">مفعلة</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">معطلة</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <strong>إشعارات الإعلانات:</strong>
                            <?php if (WHATSAPP_ANNOUNCEMENT_NOTIFICATIONS): ?>
                                <span class="badge bg-success">مفعلة</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">معطلة</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        يجب تكوين إعدادات الواتساب أولاً قبل استخدام الخدمة.
                    </div>
                    
                    <h6>الأخطاء الموجودة:</h6>
                    <ul>
                        <?php foreach ($config_errors as $config_error): ?>
                            <li><?php echo $config_error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        </div>

        <!-- Test WhatsApp -->
        <?php if ($is_configured): ?>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-paper-plane me-2"></i>
                    اختبار إرسال رسالة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="test_phone" class="form-label">رقم الهاتف للاختبار</label>
                        <input type="text" class="form-control" id="test_phone" name="test_phone" 
                               placeholder="مثال: +966501234567" required>
                        <div class="form-text">يرجى إدخال رقم الهاتف بالصيغة الدولية</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="test_message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="test_message" name="test_message" rows="3">رسالة تجريبية من نظام إدارة حلقات تحفيظ القرآن الكريم</textarea>
                    </div>
                    
                    <button type="submit" name="test_whatsapp" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>
                        إرسال رسالة تجريبية
                    </button>
                </form>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="col-md-4">
        <!-- Setup Instructions -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تعليمات الإعداد
                </h5>
            </div>
            <div class="card-body">
                <h6>خطوات تفعيل الواتساب:</h6>
                <ol>
                    <li>اختر إحدى الخدمات المتاحة</li>
                    <li>احصل على مفاتيح API من الخدمة</li>
                    <li>قم بتحديث ملف <code>whatsapp_config.php</code></li>
                    <li>اختبر الإعدادات من هذه الصفحة</li>
                </ol>
                
                <hr>
                
                <h6>الخدمات المتاحة:</h6>
                <ul class="list-unstyled">
                    <li><strong>Twilio:</strong> خدمة موثوقة ومدفوعة</li>
                    <li><strong>Ultramsg:</strong> خدمة عربية بسيطة</li>
                    <li><strong>WATI:</strong> خدمة متخصصة</li>
                    <li><strong>360Dialog:</strong> خدمة أوروبية</li>
                </ul>
            </div>
        </div>

        <!-- Configuration File -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    ملف الإعدادات
                </h5>
            </div>
            <div class="card-body">
                <p>لتحديث إعدادات الواتساب، قم بتعديل الملف:</p>
                <code>includes/whatsapp_config.php</code>
                
                <hr>
                
                <p><strong>الإعدادات الحالية:</strong></p>
                <ul class="list-unstyled">
                    <li><strong>الخدمة:</strong> <?php echo WHATSAPP_SERVICE; ?></li>
                    <li><strong>الحالة:</strong> 
                        <?php if (WHATSAPP_NOTIFICATIONS_ENABLED): ?>
                            <span class="text-success">مفعلة</span>
                        <?php else: ?>
                            <span class="text-danger">معطلة</span>
                        <?php endif; ?>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Recent Logs -->
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    سجل الأنشطة الأخيرة
                </h5>
            </div>
            <div class="card-body">
                <?php if (WHATSAPP_LOG_ENABLED && file_exists(WHATSAPP_LOG_FILE)): ?>
                    <?php
                    $log_lines = file(WHATSAPP_LOG_FILE);
                    $recent_logs = array_slice(array_reverse($log_lines), 0, 10);
                    ?>
                    
                    <?php if (!empty($recent_logs)): ?>
                        <div style="max-height: 300px; overflow-y: auto;">
                            <?php foreach ($recent_logs as $log_line): ?>
                                <div class="small mb-1">
                                    <?php
                                    $log_line = trim($log_line);
                                    if (strpos($log_line, '[ERROR]') !== false) {
                                        echo '<span class="text-danger">' . htmlspecialchars($log_line) . '</span>';
                                    } elseif (strpos($log_line, '[WARNING]') !== false) {
                                        echo '<span class="text-warning">' . htmlspecialchars($log_line) . '</span>';
                                    } else {
                                        echo '<span class="text-muted">' . htmlspecialchars($log_line) . '</span>';
                                    }
                                    ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">لا توجد أنشطة مسجلة</p>
                    <?php endif; ?>
                <?php else: ?>
                    <p class="text-muted">سجل الأنشطة غير مفعل أو غير موجود</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer template
include_template('footer');
?>
