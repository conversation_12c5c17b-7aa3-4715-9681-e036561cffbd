<?php
/**
 * Validation and sanitization helper functions
 */

/**
 * Sanitize input data to prevent XSS attacks
 *
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate email address
 *
 * @param string $email Email address to validate
 * @return bool True if valid, false otherwise
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (basic validation)
 *
 * @param string $phone Phone number to validate
 * @return bool True if valid, false otherwise
 */
function validate_phone($phone) {
    // Remove spaces, dashes, and parentheses
    $phone = preg_replace('/[\s\-\(\)]/', '', $phone);
    
    // Check if it's a valid phone number format
    return preg_match('/^\+?[0-9]{10,15}$/', $phone);
}

/**
 * Validate username (alphanumeric, underscore, dash)
 *
 * @param string $username Username to validate
 * @return bool True if valid, false otherwise
 */
function validate_username($username) {
    return preg_match('/^[a-zA-Z0-9_\-]{3,20}$/', $username);
}

/**
 * Validate password strength
 *
 * @param string $password Password to validate
 * @param int $min_length Minimum length (default: 6)
 * @return bool True if valid, false otherwise
 */
function validate_password_strength($password, $min_length = 6) {
    // Check minimum length
    if (strlen($password) < $min_length) {
        return false;
    }
    
    // Check for at least one number
    if (!preg_match('/[0-9]/', $password)) {
        return false;
    }
    
    // Check for at least one uppercase letter
    if (!preg_match('/[A-Z]/', $password)) {
        return false;
    }
    
    return true;
}

/**
 * Validate date format (YYYY-MM-DD)
 *
 * @param string $date Date to validate
 * @return bool True if valid, false otherwise
 */
function validate_date($date) {
    $format = 'Y-m-d';
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * Sanitize and validate integer
 *
 * @param mixed $value Value to sanitize
 * @param int $min Minimum allowed value (default: null)
 * @param int $max Maximum allowed value (default: null)
 * @return int|null Sanitized integer or null if invalid
 */
function sanitize_int($value, $min = null, $max = null) {
    $value = filter_var($value, FILTER_SANITIZE_NUMBER_INT);
    $value = filter_var($value, FILTER_VALIDATE_INT);
    
    if ($value === false) {
        return null;
    }
    
    if ($min !== null && $value < $min) {
        return null;
    }
    
    if ($max !== null && $value > $max) {
        return null;
    }
    
    return $value;
}

/**
 * Sanitize and validate float
 *
 * @param mixed $value Value to sanitize
 * @param float $min Minimum allowed value (default: null)
 * @param float $max Maximum allowed value (default: null)
 * @return float|null Sanitized float or null if invalid
 */
function sanitize_float($value, $min = null, $max = null) {
    $value = filter_var($value, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
    $value = filter_var($value, FILTER_VALIDATE_FLOAT);
    
    if ($value === false) {
        return null;
    }
    
    if ($min !== null && $value < $min) {
        return null;
    }
    
    if ($max !== null && $value > $max) {
        return null;
    }
    
    return $value;
}

/**
 * Sanitize and validate URL
 *
 * @param string $url URL to sanitize
 * @return string|null Sanitized URL or null if invalid
 */
function sanitize_url($url) {
    $url = filter_var($url, FILTER_SANITIZE_URL);
    $url = filter_var($url, FILTER_VALIDATE_URL);
    
    return $url !== false ? $url : null;
}

/**
 * Generate a random token
 *
 * @param int $length Length of the token (default: 32)
 * @return string Random token
 */
function generate_token($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Check if a string contains Arabic characters
 *
 * @param string $text Text to check
 * @return bool True if contains Arabic, false otherwise
 */
function contains_arabic($text) {
    return preg_match('/[\x{0600}-\x{06FF}\x{0750}-\x{077F}]/u', $text);
}
