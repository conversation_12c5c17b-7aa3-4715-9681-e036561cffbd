<?php
/**
 * Center Security System - نظام أمان المراكز
 * يضمن عزل بيانات كل مركز عن الآخر
 */

/**
 * التحقق من صلاحية الوصول للمركز
 */
function check_center_access($pdo, $user_id, $target_center_id = null) {
    try {
        // الحصول على معلومات المستخدم
        $stmt = $pdo->prepare("
            SELECT u.center_id, u.role_id, r.role_name 
            FROM users u 
            JOIN roles r ON u.role_id = r.role_id 
            WHERE u.user_id = ? AND u.is_active = 1
        ");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return false;
        }
        
        // مدير النظام له صلاحية الوصول لجميع المراكز
        if ($user['role_name'] === 'system_owner') {
            return true;
        }
        
        // إذا لم يتم تحديد مركز مستهدف، التحقق من وجود مركز للمستخدم
        if ($target_center_id === null) {
            return $user['center_id'] !== null;
        }
        
        // التحقق من أن المستخدم ينتمي للمركز المستهدف
        return $user['center_id'] == $target_center_id;
        
    } catch (PDOException $e) {
        error_log("Center access check failed: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على معرف مركز المستخدم
 */
function get_user_center_id($pdo, $user_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT u.center_id, r.role_name 
            FROM users u 
            JOIN roles r ON u.role_id = r.role_id 
            WHERE u.user_id = ? AND u.is_active = 1
        ");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return null;
        }
        
        // مدير النظام يمكنه الوصول لجميع المراكز
        if ($user['role_name'] === 'system_owner') {
            return 'all';
        }
        
        return $user['center_id'];
        
    } catch (PDOException $e) {
        error_log("Get user center failed: " . $e->getMessage());
        return null;
    }
}

/**
 * إضافة شرط المركز للاستعلام
 */
function add_center_condition($base_query, $table_alias, $user_center_id, $center_column = 'center_id') {
    if ($user_center_id === 'all') {
        return $base_query; // مدير النظام - لا حاجة لشرط
    }
    
    if ($user_center_id === null) {
        return $base_query . " AND 1=0"; // منع الوصول
    }
    
    return $base_query . " AND {$table_alias}.{$center_column} = " . intval($user_center_id);
}

/**
 * التحقق من صلاحية الوصول لمستخدم معين
 */
function can_access_user($pdo, $current_user_id, $target_user_id) {
    try {
        $current_center = get_user_center_id($pdo, $current_user_id);
        
        if ($current_center === 'all') {
            return true; // مدير النظام
        }
        
        if ($current_center === null) {
            return false;
        }
        
        $target_center = get_user_center_id($pdo, $target_user_id);
        
        return $current_center == $target_center;
        
    } catch (PDOException $e) {
        error_log("User access check failed: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من صلاحية الوصول لحلقة معينة
 */
function can_access_circle($pdo, $user_id, $circle_id) {
    try {
        $user_center = get_user_center_id($pdo, $user_id);
        
        if ($user_center === 'all') {
            return true; // مدير النظام
        }
        
        if ($user_center === null) {
            return false;
        }
        
        $stmt = $pdo->prepare("SELECT center_id FROM circles WHERE circle_id = ?");
        $stmt->execute([$circle_id]);
        $circle = $stmt->fetch();
        
        if (!$circle) {
            return false;
        }
        
        return $user_center == $circle['center_id'];
        
    } catch (PDOException $e) {
        error_log("Circle access check failed: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من صلاحية الوصول لتسجيل في حلقة
 */
function can_access_enrollment($pdo, $user_id, $enrollment_id) {
    try {
        $user_center = get_user_center_id($pdo, $user_id);
        
        if ($user_center === 'all') {
            return true; // مدير النظام
        }
        
        if ($user_center === null) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            SELECT c.center_id 
            FROM student_circle_enrollments sce
            JOIN circles c ON sce.circle_id = c.circle_id
            WHERE sce.enrollment_id = ?
        ");
        $stmt->execute([$enrollment_id]);
        $enrollment = $stmt->fetch();
        
        if (!$enrollment) {
            return false;
        }
        
        return $user_center == $enrollment['center_id'];
        
    } catch (PDOException $e) {
        error_log("Enrollment access check failed: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على قائمة المراكز المسموح للمستخدم الوصول إليها
 */
function get_accessible_centers($pdo, $user_id) {
    try {
        $user_center = get_user_center_id($pdo, $user_id);
        
        if ($user_center === 'all') {
            // مدير النظام - جميع المراكز
            $stmt = $pdo->query("SELECT * FROM centers WHERE is_active = 1 ORDER BY center_name");
            return $stmt->fetchAll();
        }
        
        if ($user_center === null) {
            return [];
        }
        
        // مركز واحد فقط
        $stmt = $pdo->prepare("SELECT * FROM centers WHERE center_id = ? AND is_active = 1");
        $stmt->execute([$user_center]);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Get accessible centers failed: " . $e->getMessage());
        return [];
    }
}

/**
 * تسجيل محاولة وصول غير مصرح بها
 */
function log_unauthorized_access($pdo, $user_id, $attempted_resource, $attempted_action) {
    try {
        require_once 'activity_logger.php';
        
        $description = "محاولة وصول غير مصرح بها: {$attempted_action} على {$attempted_resource}";
        
        log_activity($pdo, 'SECURITY', 'unauthorized_access', $description, $user_id, [
            'attempted_resource' => $attempted_resource,
            'attempted_action' => $attempted_action,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        error_log("Failed to log unauthorized access: " . $e->getMessage());
    }
}

/**
 * إنشاء استعلام آمن للمستخدمين
 */
function create_secure_users_query($pdo, $user_id, $additional_conditions = "") {
    $user_center = get_user_center_id($pdo, $user_id);
    
    $base_query = "
        SELECT u.*, r.role_name, c.center_name 
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.role_id 
        LEFT JOIN centers c ON u.center_id = c.center_id 
        WHERE 1=1
    ";
    
    if ($user_center !== 'all') {
        if ($user_center === null) {
            $base_query .= " AND 1=0"; // منع الوصول
        } else {
            $base_query .= " AND u.center_id = " . intval($user_center);
        }
    }
    
    if ($additional_conditions) {
        $base_query .= " " . $additional_conditions;
    }
    
    return $base_query;
}

/**
 * إنشاء استعلام آمن للحلقات
 */
function create_secure_circles_query($pdo, $user_id, $additional_conditions = "") {
    $user_center = get_user_center_id($pdo, $user_id);
    
    $base_query = "
        SELECT c.*, u.full_name as teacher_name, cent.center_name 
        FROM circles c 
        LEFT JOIN users u ON c.teacher_user_id = u.user_id 
        LEFT JOIN centers cent ON c.center_id = cent.center_id 
        WHERE 1=1
    ";
    
    if ($user_center !== 'all') {
        if ($user_center === null) {
            $base_query .= " AND 1=0"; // منع الوصول
        } else {
            $base_query .= " AND c.center_id = " . intval($user_center);
        }
    }
    
    if ($additional_conditions) {
        $base_query .= " " . $additional_conditions;
    }
    
    return $base_query;
}

/**
 * إنشاء استعلام آمن للتسجيلات
 */
function create_secure_enrollments_query($pdo, $user_id, $additional_conditions = "") {
    $user_center = get_user_center_id($pdo, $user_id);
    
    $base_query = "
        SELECT sce.*, s.full_name as student_name, c.circle_name, p.full_name as parent_name,
               cent.center_name
        FROM student_circle_enrollments sce 
        JOIN users s ON sce.student_user_id = s.user_id 
        JOIN circles c ON sce.circle_id = c.circle_id 
        LEFT JOIN users p ON sce.parent_user_id = p.user_id 
        LEFT JOIN centers cent ON c.center_id = cent.center_id
        WHERE 1=1
    ";
    
    if ($user_center !== 'all') {
        if ($user_center === null) {
            $base_query .= " AND 1=0"; // منع الوصول
        } else {
            $base_query .= " AND c.center_id = " . intval($user_center);
        }
    }
    
    if ($additional_conditions) {
        $base_query .= " " . $additional_conditions;
    }
    
    return $base_query;
}

/**
 * إنشاء استعلام آمن لسجلات الحضور
 */
function create_secure_attendance_query($pdo, $user_id, $additional_conditions = "") {
    $user_center = get_user_center_id($pdo, $user_id);
    
    $base_query = "
        SELECT ar.*, s.full_name as student_name, c.circle_name, t.full_name as teacher_name,
               cent.center_name
        FROM attendance_records ar
        JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
        JOIN users s ON sce.student_user_id = s.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        LEFT JOIN users t ON ar.recorded_by_user_id = t.user_id
        LEFT JOIN centers cent ON c.center_id = cent.center_id
        WHERE 1=1
    ";
    
    if ($user_center !== 'all') {
        if ($user_center === null) {
            $base_query .= " AND 1=0"; // منع الوصول
        } else {
            $base_query .= " AND c.center_id = " . intval($user_center);
        }
    }
    
    if ($additional_conditions) {
        $base_query .= " " . $additional_conditions;
    }
    
    return $base_query;
}

/**
 * إنشاء استعلام آمن لتقدم الحفظ
 */
function create_secure_memorization_query($pdo, $user_id, $additional_conditions = "") {
    $user_center = get_user_center_id($pdo, $user_id);
    
    $base_query = "
        SELECT mp.*, s.full_name as student_name, c.circle_name, t.full_name as teacher_name,
               cent.center_name
        FROM memorization_progress mp
        JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
        JOIN users s ON sce.student_user_id = s.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        LEFT JOIN users t ON mp.recorded_by_user_id = t.user_id
        LEFT JOIN centers cent ON c.center_id = cent.center_id
        WHERE 1=1
    ";
    
    if ($user_center !== 'all') {
        if ($user_center === null) {
            $base_query .= " AND 1=0"; // منع الوصول
        } else {
            $base_query .= " AND c.center_id = " . intval($user_center);
        }
    }
    
    if ($additional_conditions) {
        $base_query .= " " . $additional_conditions;
    }
    
    return $base_query;
}

/**
 * التحقق من صلاحية تعديل البيانات
 */
function can_modify_data($pdo, $user_id, $data_type, $data_id) {
    try {
        $user_center = get_user_center_id($pdo, $user_id);
        
        if ($user_center === 'all') {
            return true; // مدير النظام
        }
        
        if ($user_center === null) {
            return false;
        }
        
        switch ($data_type) {
            case 'user':
                return can_access_user($pdo, $user_id, $data_id);
                
            case 'circle':
                return can_access_circle($pdo, $user_id, $data_id);
                
            case 'enrollment':
                return can_access_enrollment($pdo, $user_id, $data_id);
                
            default:
                return false;
        }
        
    } catch (PDOException $e) {
        error_log("Modify data check failed: " . $e->getMessage());
        return false;
    }
}

/**
 * تطبيق أمان المركز على جلسة المستخدم
 */
function apply_center_security_to_session($pdo, $user_id) {
    try {
        $user_center = get_user_center_id($pdo, $user_id);
        $_SESSION['user_center_id'] = $user_center;
        $_SESSION['is_system_owner'] = ($user_center === 'all');
        
        // الحصول على معلومات المركز
        if ($user_center !== 'all' && $user_center !== null) {
            $stmt = $pdo->prepare("SELECT center_name FROM centers WHERE center_id = ?");
            $stmt->execute([$user_center]);
            $center = $stmt->fetch();
            $_SESSION['center_name'] = $center['center_name'] ?? 'غير محدد';
        } else {
            $_SESSION['center_name'] = $user_center === 'all' ? 'جميع المراكز' : 'غير محدد';
        }
        
    } catch (PDOException $e) {
        error_log("Apply center security failed: " . $e->getMessage());
    }
}
?>
