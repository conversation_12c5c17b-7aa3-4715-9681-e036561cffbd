<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$error = '';
$success = '';

// Create system_logs table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            log_level ENUM('INFO', 'WARNING', 'ERROR', 'DEBUG', 'CRITICAL') NOT NULL DEFAULT 'INFO',
            log_category VARCHAR(50) NOT NULL DEFAULT 'GENERAL',
            message TEXT NOT NULL,
            user_id INT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            additional_data JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
            INDEX idx_log_level (log_level),
            INDEX idx_log_category (log_category),
            INDEX idx_created_at (created_at),
            INDEX idx_user_id (user_id)
        )
    ");
} catch (PDOException $e) {
    $error = 'حدث خطأ في إنشاء جدول السجلات: ' . $e->getMessage();
}

// Handle log cleanup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'clear_logs') {
        $days = (int)$_POST['days'];
        if ($days > 0) {
            try {
                $stmt = $pdo->prepare("DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
                $stmt->execute([$days]);
                $deleted_count = $stmt->rowCount();
                $success = "تم حذف {$deleted_count} سجل أقدم من {$days} يوم";

                // Log this action
                log_system_activity('SYSTEM', "تم حذف السجلات الأقدم من {$days} يوم", 'INFO', $_SESSION['user_id']);
            } catch (PDOException $e) {
                $error = 'حدث خطأ أثناء حذف السجلات: ' . $e->getMessage();
            }
        }
    } elseif ($_POST['action'] === 'export_logs') {
        $level = $_POST['level'] ?? '';
        $category = $_POST['category'] ?? '';
        $days = (int)($_POST['export_days'] ?? 30);

        try {
            $query = "
                SELECT sl.*, u.full_name as user_name
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.user_id
                WHERE sl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ";
            $params = [$days];

            if (!empty($level)) {
                $query .= " AND sl.log_level = ?";
                $params[] = $level;
            }

            if (!empty($category)) {
                $query .= " AND sl.log_category = ?";
                $params[] = $category;
            }

            $query .= " ORDER BY sl.created_at DESC";

            $stmt = $pdo->prepare($query);
            $stmt->execute($params);
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Generate CSV
            $filename = 'system_logs_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename=' . $filename);

            $output = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

            // CSV headers
            fputcsv($output, ['التاريخ', 'المستوى', 'الفئة', 'الرسالة', 'المستخدم', 'عنوان IP']);

            foreach ($logs as $log) {
                fputcsv($output, [
                    $log['created_at'],
                    $log['log_level'],
                    $log['log_category'],
                    $log['message'],
                    $log['user_name'] ?? 'غير محدد',
                    $log['ip_address'] ?? 'غير محدد'
                ]);
            }

            fclose($output);
            exit;
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تصدير السجلات: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$level_filter = $_GET['level'] ?? '';
$category_filter = $_GET['category'] ?? '';
$days_filter = (int)($_GET['days'] ?? 7);
$page = (int)($_GET['page'] ?? 1);
$per_page = 50;
$offset = ($page - 1) * $per_page;

// Build query with filters
$where_conditions = ["sl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)"];
$params = [$days_filter];

if (!empty($level_filter)) {
    $where_conditions[] = "sl.log_level = ?";
    $params[] = $level_filter;
}

if (!empty($category_filter)) {
    $where_conditions[] = "sl.log_category = ?";
    $params[] = $category_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
try {
    $count_query = "
        SELECT COUNT(*)
        FROM system_logs sl
        WHERE {$where_clause}
    ";
    $stmt = $pdo->prepare($count_query);
    $stmt->execute($params);
    $total_logs = $stmt->fetchColumn();

    $total_pages = ceil($total_logs / $per_page);
} catch (PDOException $e) {
    $total_logs = 0;
    $total_pages = 0;
}

// Get logs with pagination
try {
    $query = "
        SELECT sl.*, u.full_name as user_name
        FROM system_logs sl
        LEFT JOIN users u ON sl.user_id = u.user_id
        WHERE {$where_clause}
        ORDER BY sl.created_at DESC
        LIMIT {$per_page} OFFSET {$offset}
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $logs = [];
    $error = 'حدث خطأ أثناء استرجاع السجلات: ' . $e->getMessage();
}

// Get available categories and levels
try {
    $stmt = $pdo->prepare("SELECT DISTINCT log_category FROM system_logs ORDER BY log_category");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $stmt = $pdo->prepare("SELECT DISTINCT log_level FROM system_logs ORDER BY log_level");
    $stmt->execute();
    $levels = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    $categories = [];
    $levels = [];
}

// Get statistics
try {
    $stmt = $pdo->prepare("
        SELECT
            log_level,
            COUNT(*) as count
        FROM system_logs
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY log_level
        ORDER BY count DESC
    ");
    $stmt->execute([$days_filter]);
    $level_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $stmt = $pdo->prepare("
        SELECT
            log_category,
            COUNT(*) as count
        FROM system_logs
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY log_category
        ORDER BY count DESC
        LIMIT 10
    ");
    $stmt->execute([$days_filter]);
    $category_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $level_stats = [];
    $category_stats = [];
}

// Function to log system activity (if not already defined)
if (!function_exists('log_system_activity')) {
    function log_system_activity($category, $message, $level = 'INFO', $user_id = null, $additional_data = null) {
        global $pdo;

        try {
            $stmt = $pdo->prepare("
                INSERT INTO system_logs (log_category, message, log_level, user_id, ip_address, user_agent, additional_data)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            $additional_data_json = $additional_data ? json_encode($additional_data) : null;

            $stmt->execute([
                $category,
                $message,
                $level,
                $user_id,
                $ip_address,
                $user_agent,
                $additional_data_json
            ]);
        } catch (PDOException $e) {
            // Silently fail to avoid infinite loops
            error_log('Failed to log system activity: ' . $e->getMessage());
        }
    }
}

// Page title
$page_title = 'سجلات النظام';

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-file-alt me-2"></i> سجلات النظام</h1>
        <a href="system_owner_dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
        </a>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo number_format($total_logs); ?></h4>
                            <p class="mb-0">إجمالي السجلات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php foreach ($level_stats as $stat): ?>
            <?php
            $bg_class = '';
            switch ($stat['log_level']) {
                case 'ERROR':
                case 'CRITICAL':
                    $bg_class = 'bg-danger';
                    break;
                case 'WARNING':
                    $bg_class = 'bg-warning';
                    break;
                case 'INFO':
                    $bg_class = 'bg-info';
                    break;
                case 'DEBUG':
                    $bg_class = 'bg-secondary';
                    break;
                default:
                    $bg_class = 'bg-primary';
            }
            ?>
            <div class="col-md-3">
                <div class="card <?php echo $bg_class; ?> text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo number_format($stat['count']); ?></h4>
                                <p class="mb-0"><?php echo $stat['log_level']; ?></p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Filters and Actions -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>تصفية السجلات</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="level" class="form-label">مستوى السجل</label>
                            <select class="form-select" id="level" name="level">
                                <option value="">جميع المستويات</option>
                                <?php foreach ($levels as $level): ?>
                                    <option value="<?php echo $level; ?>" <?php echo $level_filter === $level ? 'selected' : ''; ?>>
                                        <?php echo $level; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">فئة السجل</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category; ?>" <?php echo $category_filter === $category ? 'selected' : ''; ?>>
                                        <?php echo $category; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="days" class="form-label">آخر (أيام)</label>
                            <select class="form-select" id="days" name="days">
                                <option value="1" <?php echo $days_filter === 1 ? 'selected' : ''; ?>>يوم واحد</option>
                                <option value="7" <?php echo $days_filter === 7 ? 'selected' : ''; ?>>7 أيام</option>
                                <option value="30" <?php echo $days_filter === 30 ? 'selected' : ''; ?>>30 يوم</option>
                                <option value="90" <?php echo $days_filter === 90 ? 'selected' : ''; ?>>90 يوم</option>
                                <option value="365" <?php echo $days_filter === 365 ? 'selected' : ''; ?>>سنة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>تصفية
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-tools me-2"></i>إجراءات</h5>
                </div>
                <div class="card-body">
                    <!-- Export Logs -->
                    <form method="POST" class="mb-3">
                        <input type="hidden" name="action" value="export_logs">
                        <input type="hidden" name="level" value="<?php echo $level_filter; ?>">
                        <input type="hidden" name="category" value="<?php echo $category_filter; ?>">
                        <input type="hidden" name="export_days" value="<?php echo $days_filter; ?>">
                        <button type="submit" class="btn btn-success btn-sm w-100 mb-2">
                            <i class="fas fa-download me-1"></i>تصدير السجلات (CSV)
                        </button>
                    </form>

                    <!-- Clear Old Logs -->
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من حذف السجلات القديمة؟')">
                        <input type="hidden" name="action" value="clear_logs">
                        <div class="input-group input-group-sm mb-2">
                            <input type="number" class="form-control" name="days" value="90" min="1" max="365" required>
                            <span class="input-group-text">يوم</span>
                        </div>
                        <button type="submit" class="btn btn-warning btn-sm w-100">
                            <i class="fas fa-trash me-1"></i>حذف السجلات الأقدم من
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>السجلات
                <span class="badge bg-secondary"><?php echo number_format($total_logs); ?></span>
            </h5>
            <div>
                عرض <?php echo number_format($offset + 1); ?> - <?php echo number_format(min($offset + $per_page, $total_logs)); ?>
                من <?php echo number_format($total_logs); ?>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($logs)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد سجلات</h5>
                    <p class="text-muted">لم يتم العثور على سجلات تطابق المعايير المحددة</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>المستوى</th>
                                <th>الفئة</th>
                                <th>الرسالة</th>
                                <th>المستخدم</th>
                                <th>عنوان IP</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($logs as $log): ?>
                                <?php
                                $level_class = '';
                                $level_icon = '';
                                switch ($log['log_level']) {
                                    case 'CRITICAL':
                                        $level_class = 'text-danger fw-bold';
                                        $level_icon = 'fas fa-skull';
                                        break;
                                    case 'ERROR':
                                        $level_class = 'text-danger';
                                        $level_icon = 'fas fa-times-circle';
                                        break;
                                    case 'WARNING':
                                        $level_class = 'text-warning';
                                        $level_icon = 'fas fa-exclamation-triangle';
                                        break;
                                    case 'INFO':
                                        $level_class = 'text-info';
                                        $level_icon = 'fas fa-info-circle';
                                        break;
                                    case 'DEBUG':
                                        $level_class = 'text-secondary';
                                        $level_icon = 'fas fa-bug';
                                        break;
                                    default:
                                        $level_class = 'text-primary';
                                        $level_icon = 'fas fa-circle';
                                }
                                ?>
                                <tr>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="<?php echo $level_class; ?>">
                                            <i class="<?php echo $level_icon; ?> me-1"></i>
                                            <?php echo $log['log_level']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            <?php echo $log['log_category']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="log-message" style="max-width: 400px;">
                                            <?php echo htmlspecialchars($log['message']); ?>
                                            <?php if (!empty($log['additional_data'])): ?>
                                                <button class="btn btn-sm btn-outline-secondary ms-2"
                                                        type="button"
                                                        data-bs-toggle="collapse"
                                                        data-bs-target="#details-<?php echo $log['log_id']; ?>"
                                                        aria-expanded="false">
                                                    <i class="fas fa-info"></i>
                                                </button>
                                                <div class="collapse mt-2" id="details-<?php echo $log['log_id']; ?>">
                                                    <div class="card card-body bg-light">
                                                        <small>
                                                            <pre><?php echo htmlspecialchars(json_encode(json_decode($log['additional_data']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                                                        </small>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($log['user_name']): ?>
                                            <span class="badge bg-primary">
                                                <?php echo htmlspecialchars($log['user_name']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">النظام</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted font-monospace">
                                            <?php echo $log['ip_address'] ?? 'غير محدد'; ?>
                                        </small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($total_pages > 1): ?>
            <div class="card-footer">
                <nav aria-label="تصفح السجلات">
                    <ul class="pagination pagination-sm justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                    السابق
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);
                        ?>

                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                    التالي
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>

    <!-- Category Statistics -->
    <?php if (!empty($category_stats)): ?>
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>إحصائيات الفئات</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($category_stats as $stat): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-secondary"><?php echo $stat['log_category']; ?></span>
                                <span class="fw-bold"><?php echo number_format($stat['count']); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>معلومات مفيدة</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-circle text-danger me-2"></i>
                                <strong>CRITICAL/ERROR:</strong> أخطاء تتطلب تدخل فوري
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-circle text-warning me-2"></i>
                                <strong>WARNING:</strong> تحذيرات قد تحتاج متابعة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-circle text-info me-2"></i>
                                <strong>INFO:</strong> معلومات عامة عن النشاط
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-circle text-secondary me-2"></i>
                                <strong>DEBUG:</strong> معلومات تقنية للمطورين
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.log-message {
    word-break: break-word;
    line-height: 1.4;
}

.table td {
    vertical-align: middle;
}

.font-monospace {
    font-family: 'Courier New', monospace;
}

pre {
    font-size: 0.8rem;
    max-height: 200px;
    overflow-y: auto;
}
</style>

<?php
// Include footer
require_once '../includes/footer.php';
?>
