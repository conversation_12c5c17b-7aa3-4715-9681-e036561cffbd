<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has teacher role
if (!is_logged_in() || !has_role('teacher')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$teacher_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Get filter parameters
$circle_id = isset($_GET['circle_id']) ? (int)$_GET['circle_id'] : 0;

// Get teacher's circles for filter dropdown
try {
    $stmt = $pdo->prepare("
        SELECT circle_id, circle_name
        FROM circles
        WHERE teacher_user_id = ? AND is_active = 1
        ORDER BY circle_name
    ");
    $stmt->execute([$teacher_id]);
    $teacher_circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get teacher's students
try {
    $query = "
        SELECT DISTINCT u.user_id, u.full_name, u.username, u.email, u.phone_number,
               u.birth_date, u.gender, u.is_active, u.created_at,
               c.circle_name, c.circle_id, sce.enrollment_date, sce.status
        FROM users u
        JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        WHERE c.teacher_user_id = ?
    ";
    
    $params = [$teacher_id];
    
    if ($circle_id) {
        $query .= " AND c.circle_id = ?";
        $params[] = $circle_id;
    }
    
    $query .= " ORDER BY u.full_name";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $students = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب: ' . $e->getMessage();
}

// Page variables
$page_title = 'طلابي';
$active_page = 'students';

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true
]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-user-graduate'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h6 class="card-title mb-0"><i class="fas fa-filter me-2"></i> تصفية النتائج</h6>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="circle_id" class="form-label">الحلقة</label>
                <select name="circle_id" id="circle_id" class="form-select">
                    <option value="">جميع الحلقات</option>
                    <?php foreach ($teacher_circles as $circle): ?>
                        <option value="<?php echo $circle['circle_id']; ?>" <?php echo $circle_id == $circle['circle_id'] ? 'selected' : ''; ?>>
                            <?php echo $circle['circle_name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i> تصفية
                </button>
                <a href="teacher_students.php" class="btn btn-secondary">
                    <i class="fas fa-undo me-1"></i> إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<?php if (empty($students)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> لا يوجد طلاب مسجلين في حلقاتك حالياً.
    </div>
<?php else: ?>
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table id="studentsTable" class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>معلومات الاتصال</th>
                            <th>الحلقة</th>
                            <th>الجنس</th>
                            <th>تاريخ الميلاد</th>
                            <th>تاريخ التسجيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($students as $student): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $student['full_name']; ?></strong><br>
                                    <small class="text-muted">@<?php echo $student['username']; ?></small>
                                </td>
                                <td>
                                    <i class="fas fa-envelope me-1"></i> <?php echo $student['email']; ?><br>
                                    <?php if (!empty($student['phone_number'])): ?>
                                        <i class="fas fa-phone me-1"></i> <?php echo $student['phone_number']; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo $student['circle_name']; ?></span>
                                </td>
                                <td>
                                    <?php if (!empty($student['gender'])): ?>
                                        <?php echo $student['gender'] == 'male' ? 'ذكر' : 'أنثى'; ?>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($student['birth_date'])): ?>
                                        <?php echo date('Y-m-d', strtotime($student['birth_date'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($student['enrollment_date'])); ?></td>
                                <td>
                                    <?php if ($student['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                    
                                    <?php if ($student['status'] == 'approved'): ?>
                                        <span class="badge bg-success">مقبول</span>
                                    <?php elseif ($student['status'] == 'pending'): ?>
                                        <span class="badge bg-warning">في الانتظار</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">مرفوض</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="student_details.php?id=<?php echo $student['user_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="attendance.php?circle_id=<?php echo $student['circle_id']; ?>&student_id=<?php echo $student['user_id']; ?>" class="btn btn-sm btn-primary" title="الحضور">
                                            <i class="fas fa-calendar-check"></i>
                                        </a>
                                        <a href="memorization.php?circle_id=<?php echo $student['circle_id']; ?>&student_id=<?php echo $student['user_id']; ?>" class="btn btn-sm btn-success" title="التسميع">
                                            <i class="fas fa-book-reader"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Include footer template
include_template('footer');
?>
