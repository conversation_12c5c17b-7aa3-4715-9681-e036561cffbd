<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Create some initial logs for testing
try {
    // Create the table first
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_logs (
            log_id INT AUTO_INCREMENT PRIMARY KEY,
            log_level ENUM('INFO', 'WARNING', 'ERROR', 'DEBUG', 'CRITICAL') NOT NULL DEFAULT 'INFO',
            log_category VARCHAR(50) NOT NULL DEFAULT 'GENERAL',
            message TEXT NOT NULL,
            user_id INT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            additional_data JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIG<PERSON> KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
            INDEX idx_log_level (log_level),
            INDEX idx_log_category (log_category),
            INDEX idx_created_at (created_at),
            INDEX idx_user_id (user_id)
        )
    ");
    
    // Add some sample logs
    $sample_logs = [
        ['SYSTEM', 'تم بدء تشغيل النظام بنجاح', 'INFO'],
        ['AUTH', 'تسجيل دخول ناجح للمستخدم: admin', 'INFO'],
        ['USER', 'إنشاء مستخدم جديد: أحمد محمد', 'INFO'],
        ['CENTER', 'إنشاء مركز جديد: مركز النور لتحفيظ القرآن', 'INFO'],
        ['CIRCLE', 'إنشاء حلقة جديدة: حلقة المبتدئين', 'INFO'],
        ['ATTENDANCE', 'تسجيل حضور: محمد أحمد - حلقة المتقدمين - حاضر', 'INFO'],
        ['WHATSAPP', 'إرسال رسالة واتساب نجح: إشعار غياب إلى ولي أمر', 'INFO'],
        ['CONFIG', 'تغيير إعداد النظام: site_name', 'INFO'],
        ['DATABASE', 'نسخ احتياطي للقاعدة البيانات', 'INFO'],
        ['SYSTEM', 'تم إنشاء نظام السجلات بنجاح', 'INFO'],
        ['AUTH', 'محاولة تسجيل دخول فاشلة للمستخدم: unknown_user', 'WARNING'],
        ['ATTENDANCE', 'تسجيل حضور: سارة محمد - حلقة المبتدئين - غائب بدون عذر', 'WARNING'],
        ['WHATSAPP', 'إرسال رسالة واتساب فشل: رقم هاتف غير صحيح', 'ERROR'],
        ['SECURITY', 'حدث أمني: محاولة وصول غير مصرح بها', 'WARNING'],
        ['SYSTEM', 'خطأ في النظام: فشل في الاتصال بقاعدة البيانات', 'ERROR'],
    ];
    
    $count = 0;
    foreach ($sample_logs as $log) {
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (log_category, message, log_level, user_id, ip_address, user_agent, additional_data) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $additional_data = json_encode([
            'sample_data' => true,
            'created_by' => 'init_script',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        if ($stmt->execute([
            $log[0],
            $log[1],
            $log[2],
            $_SESSION['user_id'],
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            $_SERVER['HTTP_USER_AGENT'] ?? 'System Init Script',
            $additional_data
        ])) {
            $count++;
        }
        
        // Add some delay to create different timestamps
        usleep(50000); // 0.05 second
    }
    
    $success_message = "تم إنشاء {$count} سجل تجريبي بنجاح!";
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ: ' . $e->getMessage();
}

// Redirect back to system logs
if (isset($success_message)) {
    set_flash_message('success', $success_message);
} else {
    set_flash_message('danger', $error_message ?? 'حدث خطأ غير معروف');
}

redirect('system_logs.php');
?>
