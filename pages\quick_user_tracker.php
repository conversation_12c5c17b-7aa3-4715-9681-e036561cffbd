<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$page_title = 'الوصول السريع لمتتبع المستخدمين';
include_once '../includes/header_inner.php';

// Get users with recent activity
try {
    $stmt = $pdo->query("
        SELECT u.user_id, u.username, u.full_name, r.role_name, c.center_name, u.is_active,
               (SELECT COUNT(*) FROM activity_logs al WHERE al.user_id = u.user_id AND al.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as recent_activities,
               (SELECT MAX(al.created_at) FROM activity_logs al WHERE al.user_id = u.user_id) as last_activity
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.role_id 
        LEFT JOIN centers c ON u.center_id = c.center_id 
        WHERE u.is_active = 1
        ORDER BY recent_activities DESC, last_activity DESC
        LIMIT 20
    ");
    $active_users = $stmt->fetchAll();
} catch (PDOException $e) {
    $active_users = [];
}

// Get all users for search
try {
    $stmt = $pdo->query("
        SELECT u.user_id, u.username, u.full_name, r.role_name
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.role_id 
        ORDER BY u.full_name
    ");
    $all_users = $stmt->fetchAll();
} catch (PDOException $e) {
    $all_users = [];
}
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-search me-2"></i> الوصول السريع لمتتبع المستخدمين</h1>
        <div>
            <a href="user_operations_tracker.php" class="btn btn-primary me-2">
                <i class="fas fa-user-cog me-1"></i> المتتبع الكامل
            </a>
            <a href="user_activity.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة
            </a>
        </div>
    </div>

    <!-- Quick Search -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-search me-2"></i>البحث السريع عن مستخدم</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <input type="text" id="userSearch" class="form-control" placeholder="ابحث عن مستخدم بالاسم أو اسم المستخدم...">
                </div>
                <div class="col-md-4">
                    <select id="roleFilter" class="form-select">
                        <option value="">جميع الأدوار</option>
                        <option value="system_owner">مدير النظام</option>
                        <option value="center_admin">مدير المركز</option>
                        <option value="teacher">معلم</option>
                        <option value="student">طالب</option>
                        <option value="parent">ولي أمر</option>
                    </select>
                </div>
            </div>
            <div id="searchResults" class="mt-3"></div>
        </div>
    </div>

    <!-- Most Active Users -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-fire me-2"></i>المستخدمين الأكثر نشاطاً (آخر 7 أيام)</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($active_users)): ?>
                <div class="row">
                    <?php foreach (array_slice($active_users, 0, 12) as $user): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100 user-card" style="cursor: pointer;" onclick="viewUserOperations(<?php echo $user['user_id']; ?>)">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="card-title mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h6>
                                            <p class="card-text">
                                                <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small><br>
                                                <span class="badge bg-primary"><?php echo htmlspecialchars($user['role_name']); ?></span>
                                            </p>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-success"><?php echo $user['recent_activities']; ?></span>
                                            <small class="d-block text-muted">عملية</small>
                                        </div>
                                    </div>
                                    <?php if ($user['last_activity']): ?>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            آخر نشاط: <?php echo date('Y-m-d H:i', strtotime($user['last_activity'])); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أنشطة حديثة</h5>
                    <p class="text-muted">لم يتم العثور على أنشطة للمستخدمين في آخر 7 أيام</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- All Users List -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-users me-2"></i>جميع المستخدمين</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="usersTable">
                    <thead>
                        <tr>
                            <th>الاسم الكامل</th>
                            <th>اسم المستخدم</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($all_users as $user): ?>
                            <tr class="user-row" data-role="<?php echo $user['role_name']; ?>">
                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                <td><code><?php echo htmlspecialchars($user['username']); ?></code></td>
                                <td><span class="badge bg-info"><?php echo htmlspecialchars($user['role_name']); ?></span></td>
                                <td>
                                    <?php 
                                    $is_active = true; // Default to active since we're showing all users
                                    foreach ($active_users as $active_user) {
                                        if ($active_user['user_id'] == $user['user_id']) {
                                            $is_active = $active_user['is_active'];
                                            break;
                                        }
                                    }
                                    ?>
                                    <span class="badge <?php echo $is_active ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo $is_active ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="viewUserOperations(<?php echo $user['user_id']; ?>)">
                                        <i class="fas fa-eye me-1"></i> عرض العمليات
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function viewUserOperations(userId) {
    window.location.href = 'user_operations_tracker.php?user_id=' + userId;
}

// Search functionality
document.getElementById('userSearch').addEventListener('input', function() {
    filterUsers();
});

document.getElementById('roleFilter').addEventListener('change', function() {
    filterUsers();
});

function filterUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const rows = document.querySelectorAll('.user-row');
    
    rows.forEach(row => {
        const name = row.cells[0].textContent.toLowerCase();
        const username = row.cells[1].textContent.toLowerCase();
        const role = row.getAttribute('data-role');
        
        const matchesSearch = name.includes(searchTerm) || username.includes(searchTerm);
        const matchesRole = !roleFilter || role === roleFilter;
        
        if (matchesSearch && matchesRole) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Live search results
document.getElementById('userSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const resultsDiv = document.getElementById('searchResults');
    
    if (searchTerm.length < 2) {
        resultsDiv.innerHTML = '';
        return;
    }
    
    const users = <?php echo json_encode($all_users); ?>;
    const matches = users.filter(user => 
        user.full_name.toLowerCase().includes(searchTerm) || 
        user.username.toLowerCase().includes(searchTerm)
    ).slice(0, 5);
    
    if (matches.length > 0) {
        let html = '<div class="list-group">';
        matches.forEach(user => {
            html += `
                <a href="user_operations_tracker.php?user_id=${user.user_id}" class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${user.full_name}</h6>
                        <small class="text-muted">${user.role_name}</small>
                    </div>
                    <small class="text-muted">@${user.username}</small>
                </a>
            `;
        });
        html += '</div>';
        resultsDiv.innerHTML = html;
    } else {
        resultsDiv.innerHTML = '<div class="alert alert-info">لا توجد نتائج مطابقة</div>';
    }
});
</script>

<style>
.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

#searchResults {
    max-height: 300px;
    overflow-y: auto;
}
</style>

<?php include_once '../includes/footer.php'; ?>
