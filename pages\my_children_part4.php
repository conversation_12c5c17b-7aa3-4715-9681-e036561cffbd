    <!-- Add Child Modal -->
    <div class="modal fade" id="addChildModal" tabindex="-1" aria-labelledby="addChildModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addChildModalLabel">إضافة ابن</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_child">
                        
                        <div class="mb-3">
                            <label for="student_id" class="form-label">اختر الطالب</label>
                            <select class="form-select" id="student_id" name="student_id" required>
                                <option value="">-- اختر الطالب --</option>
                                <?php foreach ($available_students as $student): ?>
                                    <option value="<?php echo $student['user_id']; ?>"><?php echo $student['full_name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="relation_type" class="form-label">نوع العلاقة</label>
                            <select class="form-select" id="relation_type" name="relation_type" required>
                                <option value="أب">أب</option>
                                <option value="أم">أم</option>
                                <option value="وصي">وصي</option>
                                <option value="أخ">أخ</option>
                                <option value="أخت">أخت</option>
                                <option value="جد">جد</option>
                                <option value="جدة">جدة</option>
                                <option value="عم">عم</option>
                                <option value="عمة">عمة</option>
                                <option value="خال">خال</option>
                                <option value="خالة">خالة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_primary" name="is_primary" checked>
                            <label class="form-check-label" for="is_primary">
                                تعيين كولي أمر رئيسي
                            </label>
                            <div class="form-text">سيتم استخدام ولي الأمر الرئيسي للتواصل والإشعارات</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Remove Child Modal -->
    <div class="modal fade" id="removeChildModal" tabindex="-1" aria-labelledby="removeChildModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="removeChildModalLabel">إزالة ابن</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="remove_child">
                        <input type="hidden" name="relation_id" id="remove_relation_id">
                        
                        <p>هل أنت متأكد من رغبتك في إزالة <span id="remove_student_name" class="fw-bold"></span> من قائمة أبنائك؟</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i> هذا الإجراء لا يمكن التراجع عنه.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">إزالة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>
    
    <script>
        // Set relation ID and student name in remove modal
        document.addEventListener('DOMContentLoaded', function() {
            const removeChildModal = document.getElementById('removeChildModal');
            if (removeChildModal) {
                removeChildModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const relationId = button.getAttribute('data-relation-id');
                    const studentName = button.getAttribute('data-student-name');
                    
                    document.getElementById('remove_relation_id').value = relationId;
                    document.getElementById('remove_student_name').textContent = studentName;
                });
            }
        });
    </script>
</body>
</html>
