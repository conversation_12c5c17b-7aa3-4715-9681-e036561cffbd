<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Check if parent ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف ولي الأمر غير صحيح');
    redirect('pages/parents.php');
}

$parent_id = (int)$_GET['id'];
$error = '';
$success = '';

// Get parent information
try {
    $stmt = $pdo->prepare("
        SELECT u.*, c.center_name, r.role_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ? AND r.role_name = 'parent'
    ");
    $stmt->execute([$parent_id]);
    $parent = $stmt->fetch();

    if (!$parent) {
        set_flash_message('danger', 'ولي الأمر غير موجود');
        redirect('pages/parents.php');
    }

    // Check if current user has access to this parent
    if (has_role('center_admin') && $parent['center_id'] != $_SESSION['center_id']) {
        set_flash_message('danger', 'غير مصرح لك بالوصول إلى بيانات ولي الأمر هذا');
        redirect('pages/parents.php');
    }

    // Get children relations
    $stmt = $pdo->prepare("
        SELECT psr.relation_id, psr.student_user_id, psr.relation_type, psr.is_primary,
               s.full_name AS student_name, s.email AS student_email,
               c.circle_name, t.full_name AS teacher_name
        FROM parent_student_relations psr
        JOIN users s ON psr.student_user_id = s.user_id
        LEFT JOIN student_circle_enrollments sce ON s.user_id = sce.student_user_id AND sce.status = 'approved'
        LEFT JOIN circles c ON sce.circle_id = c.circle_id
        LEFT JOIN users t ON c.teacher_user_id = t.user_id
        WHERE psr.parent_user_id = ?
        ORDER BY psr.is_primary DESC, s.full_name
    ");
    $stmt->execute([$parent_id]);
    $children_relations = $stmt->fetchAll();

    // Check if address column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'address'
    ");
    $stmt->execute();
    $address_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات ولي الأمر: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $selected_center_id = has_role('center_admin') ? $_SESSION['center_id'] : (int)$_POST['center_id'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Children relations
    $student_ids = isset($_POST['student_ids']) ? $_POST['student_ids'] : [];
    $relation_types = isset($_POST['relation_types']) ? $_POST['relation_types'] : [];
    $primary_student_ids = isset($_POST['primary_student_ids']) ? $_POST['primary_student_ids'] : [];
    $new_student_id = isset($_POST['new_student_id']) ? (int)$_POST['new_student_id'] : null;
    $new_relation_type = isset($_POST['new_relation_type']) ? sanitize_input($_POST['new_relation_type']) : 'أب';

    // Optional fields based on schema
    $address = $address_exists ? sanitize_input($_POST['address']) : null;

    // New password (optional)
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate required fields
    if (empty($full_name)) {
        $error = 'يرجى إدخال الاسم الكامل';
    } elseif (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (has_role('system_owner') && empty($selected_center_id)) {
        $error = 'يرجى اختيار المركز';
    } elseif (!empty($new_password) && $new_password !== $confirm_password) {
        $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
    } else {
        try {
            // Check if email already exists for another user
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
            $stmt->execute([$email, $parent_id]);
            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد آخر';
            } else {
                // Begin transaction
                $pdo->beginTransaction();

                // Build update query based on existing columns
                $query = "UPDATE users SET full_name = ?, email = ?, phone_number = ?, center_id = ?, is_active = ?";
                $params = [$full_name, $email, $phone_number, $selected_center_id, $is_active];

                if ($address_exists) {
                    $query .= ", address = ?";
                    $params[] = $address;
                }

                $query .= " WHERE user_id = ?";
                $params[] = $parent_id;

                // Update parent information
                $stmt = $pdo->prepare($query);
                $stmt->execute($params);

                // Update password if provided
                if (!empty($new_password)) {
                    $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE user_id = ?");
                    $stmt->execute([$password_hash, $parent_id]);
                }

                // Handle profile picture upload if provided
                if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/images/profiles/';

                    // Create directory if it doesn't exist
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }

                    $file_extension = pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION);
                    $new_filename = 'user_' . $parent_id . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_filename;

                    if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                        // Update user with profile picture URL
                        $profile_picture_url = 'assets/images/profiles/' . $new_filename;
                        $stmt = $pdo->prepare("UPDATE users SET profile_picture_url = ? WHERE user_id = ?");
                        $stmt->execute([$profile_picture_url, $parent_id]);
                    }
                }

                // Handle children relations
                if (!empty($student_ids)) {
                    // Update existing relations
                    foreach ($student_ids as $index => $student_id) {
                        $is_primary = in_array($student_id, $primary_student_ids) ? 1 : 0;
                        $relation_type = $relation_types[$index] ?? 'أب';

                        $stmt = $pdo->prepare("
                            UPDATE parent_student_relations
                            SET relation_type = ?, is_primary = ?
                            WHERE parent_user_id = ? AND student_user_id = ?
                        ");
                        $stmt->execute([$relation_type, $is_primary, $parent_id, $student_id]);
                    }
                }

                // Add new student relation if selected
                if ($new_student_id) {
                    // Check if relation already exists
                    $stmt = $pdo->prepare("
                        SELECT relation_id FROM parent_student_relations
                        WHERE parent_user_id = ? AND student_user_id = ?
                    ");
                    $stmt->execute([$parent_id, $new_student_id]);

                    if (!$stmt->fetch()) {
                        // Set is_primary to 1 if this is the first student
                        $stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM parent_student_relations
                            WHERE parent_user_id = ?
                        ");
                        $stmt->execute([$parent_id]);
                        $is_primary = ($stmt->fetchColumn() == 0) ? 1 : 0;

                        // Create new relation
                        $stmt = $pdo->prepare("
                            INSERT INTO parent_student_relations
                            (parent_user_id, student_user_id, relation_type, is_primary)
                            VALUES (?, ?, ?, ?)
                        ");
                        $stmt->execute([$parent_id, $new_student_id, $new_relation_type, $is_primary]);
                    }
                }

                // Commit transaction
                $pdo->commit();

                $success = 'تم تحديث بيانات ولي الأمر بنجاح';

                // Refresh parent data
                $stmt = $pdo->prepare("
                    SELECT u.*, c.center_name, r.role_name
                    FROM users u
                    JOIN roles r ON u.role_id = r.role_id
                    LEFT JOIN centers c ON u.center_id = c.center_id
                    WHERE u.user_id = ? AND r.role_name = 'parent'
                ");
                $stmt->execute([$parent_id]);
                $parent = $stmt->fetch();

                // Refresh children relations
                $stmt = $pdo->prepare("
                    SELECT psr.relation_id, psr.student_user_id, psr.relation_type, psr.is_primary,
                           s.full_name AS student_name, s.email AS student_email,
                           c.circle_name, t.full_name AS teacher_name
                    FROM parent_student_relations psr
                    JOIN users s ON psr.student_user_id = s.user_id
                    LEFT JOIN student_circle_enrollments sce ON s.user_id = sce.student_user_id AND sce.status = 'approved'
                    LEFT JOIN circles c ON sce.circle_id = c.circle_id
                    LEFT JOIN users t ON c.teacher_user_id = t.user_id
                    WHERE psr.parent_user_id = ?
                    ORDER BY psr.is_primary DESC, s.full_name
                ");
                $stmt->execute([$parent_id]);
                $children_relations = $stmt->fetchAll();
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء تحديث بيانات ولي الأمر: ' . $e->getMessage();
        }
    }
}

// Get available students for the parent's center
$center_id_for_students = $parent['center_id'];
$students_stmt = $pdo->prepare("
    SELECT u.user_id, u.full_name, u.email,
           c.circle_name, t.full_name AS teacher_name
    FROM users u
    JOIN roles r ON u.role_id = r.role_id
    LEFT JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id AND sce.status = 'approved'
    LEFT JOIN circles c ON sce.circle_id = c.circle_id
    LEFT JOIN users t ON c.teacher_user_id = t.user_id
    WHERE r.role_name = 'student' AND u.center_id = ? AND u.is_active = TRUE
    ORDER BY u.full_name
");
$students_stmt->execute([$center_id_for_students]);
$available_students = $students_stmt->fetchAll();

// Page variables
$page_title = 'تعديل بيانات ولي الأمر: ' . $parent['full_name'];
$active_page = 'parents';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'أولياء الأمور', 'url' => 'parents.php'],
    ['title' => 'تعديل بيانات ولي الأمر']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-user-friends'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-edit me-2"></i> تعديل بيانات ولي الأمر</h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" value="<?php echo $parent['username']; ?>" readonly disabled>
                    <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required value="<?php echo $parent['full_name']; ?>">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" required value="<?php echo $parent['email']; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="phone_number" class="form-label">رقم الهاتف</label>
                    <input type="text" class="form-control" id="phone_number" name="phone_number" value="<?php echo $parent['phone_number']; ?>">
                </div>
            </div>

            <?php if ($address_exists): ?>
            <div class="row">
                <div class="col-md-12 mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo $parent['address']; ?></textarea>
                </div>
            </div>
            <?php endif; ?>

            <?php if (has_role('system_owner')): ?>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="center_id" class="form-label">المركز <span class="text-danger">*</span></label>
                    <select class="form-select" id="center_id" name="center_id" required>
                        <option value="">-- اختر المركز --</option>
                        <?php
                        // Get centers
                        $centers_stmt = $pdo->query("SELECT center_id, center_name FROM centers ORDER BY center_name");
                        $centers = $centers_stmt->fetchAll();

                        foreach ($centers as $center):
                        ?>
                            <option value="<?php echo $center['center_id']; ?>" <?php echo $parent['center_id'] == $center['center_id'] ? 'selected' : ''; ?>>
                                <?php echo $center['center_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="new_password" name="new_password">
                    <div class="form-text">اتركها فارغة إذا كنت لا ترغب في تغيير كلمة المرور</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                </div>
            </div>

            <!-- Children Relations Section -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-child me-2"></i> الأبناء المرتبطين</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($children_relations)): ?>
                        <div class="alert alert-warning">
                            <strong>تنبيه:</strong> ولي الأمر ليس لديه أبناء مرتبطين به.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive mb-3">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>الطالب</th>
                                        <th>نوع العلاقة</th>
                                        <th>ابن رئيسي</th>
                                        <th>الحلقة</th>
                                        <th>المعلم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($children_relations as $index => $relation): ?>
                                        <tr>
                                            <td>
                                                <input type="hidden" name="student_ids[]" value="<?php echo $relation['student_user_id']; ?>">
                                                <?php echo $relation['student_name']; ?>
                                            </td>
                                            <td>
                                                <select class="form-select" name="relation_types[]">
                                                    <option value="أب" <?php echo $relation['relation_type'] == 'أب' ? 'selected' : ''; ?>>أب</option>
                                                    <option value="أم" <?php echo $relation['relation_type'] == 'أم' ? 'selected' : ''; ?>>أم</option>
                                                    <option value="جد" <?php echo $relation['relation_type'] == 'جد' ? 'selected' : ''; ?>>جد</option>
                                                    <option value="جدة" <?php echo $relation['relation_type'] == 'جدة' ? 'selected' : ''; ?>>جدة</option>
                                                    <option value="أخ" <?php echo $relation['relation_type'] == 'أخ' ? 'selected' : ''; ?>>أخ</option>
                                                    <option value="أخت" <?php echo $relation['relation_type'] == 'أخت' ? 'selected' : ''; ?>>أخت</option>
                                                    <option value="عم" <?php echo $relation['relation_type'] == 'عم' ? 'selected' : ''; ?>>عم</option>
                                                    <option value="عمة" <?php echo $relation['relation_type'] == 'عمة' ? 'selected' : ''; ?>>عمة</option>
                                                    <option value="خال" <?php echo $relation['relation_type'] == 'خال' ? 'selected' : ''; ?>>خال</option>
                                                    <option value="خالة" <?php echo $relation['relation_type'] == 'خالة' ? 'selected' : ''; ?>>خالة</option>
                                                    <option value="وصي" <?php echo $relation['relation_type'] == 'وصي' ? 'selected' : ''; ?>>وصي</option>
                                                    <option value="أخرى" <?php echo $relation['relation_type'] == 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                                                </select>
                                            </td>
                                            <td class="text-center">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="primary_student_ids[]" value="<?php echo $relation['student_user_id']; ?>" <?php echo $relation['is_primary'] ? 'checked' : ''; ?>>
                                                </div>
                                            </td>
                                            <td>
                                                <?php echo $relation['circle_name'] ? $relation['circle_name'] : '<span class="text-muted">غير مسجل في حلقة</span>'; ?>
                                            </td>
                                            <td>
                                                <?php echo $relation['teacher_name'] ? $relation['teacher_name'] : '<span class="text-muted">-</span>'; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="new_student_id" class="form-label">إضافة طالب جديد</label>
                            <select class="form-select" id="new_student_id" name="new_student_id">
                                <option value="">-- اختر طالب --</option>
                                <?php
                                // Filter out students that are already related to this parent
                                $existing_student_ids = array_column($children_relations, 'student_user_id');

                                foreach ($available_students as $student):
                                    if (!in_array($student['user_id'], $existing_student_ids)):
                                ?>
                                    <option value="<?php echo $student['user_id']; ?>">
                                        <?php echo $student['full_name']; ?> -
                                        <?php echo $student['email']; ?>
                                        <?php echo $student['circle_name'] ? ' - الحلقة: ' . $student['circle_name'] : ''; ?>
                                    </option>
                                <?php
                                    endif;
                                endforeach;
                                ?>
                            </select>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="new_relation_type" class="form-label">نوع العلاقة</label>
                            <select class="form-select" id="new_relation_type" name="new_relation_type">
                                <option value="أب">أب</option>
                                <option value="أم">أم</option>
                                <option value="جد">جد</option>
                                <option value="جدة">جدة</option>
                                <option value="أخ">أخ</option>
                                <option value="أخت">أخت</option>
                                <option value="عم">عم</option>
                                <option value="عمة">عمة</option>
                                <option value="خال">خال</option>
                                <option value="خالة">خالة</option>
                                <option value="وصي">وصي</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                    <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                </div>

                <div class="col-md-6 mb-3">
                    <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $parent['is_active'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">
                            حساب نشط
                        </label>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="parents.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>

<?php if (!empty($parent['profile_picture_url'])): ?>
<div class="card shadow mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="card-title mb-0"><i class="fas fa-image me-2"></i> الصورة الحالية</h5>
    </div>
    <div class="card-body text-center">
        <img src="<?php echo get_root_url() . $parent['profile_picture_url']; ?>" alt="صورة ولي الأمر" class="img-fluid rounded" style="max-height: 300px;">
    </div>
</div>
<?php endif; ?>

<?php
// Include footer template
include_template('footer');
?>
