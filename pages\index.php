<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (is_logged_in()) {
    // Redirect to appropriate dashboard based on user role
    if (has_role('system_owner')) {
        redirect('system_owner_dashboard.php');
    } elseif (has_role('center_admin')) {
        redirect('center_admin_dashboard.php');
    } elseif (has_role('teacher')) {
        redirect('teacher_dashboard.php');
    } elseif (has_role('student')) {
        redirect('student_dashboard.php');
    } elseif (has_role('parent')) {
        redirect('parent_dashboard.php');
    } else {
        // Default redirect if role is not recognized
        redirect('../index.php');
    }
} else {
    // If not logged in, redirect to main index page
    redirect('../index.php');
}
?>
