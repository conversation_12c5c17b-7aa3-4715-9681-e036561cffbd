<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/announcement_stats.php';

// Check if announcement ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف الإعلان غير صحيح');
    redirect('pages/announcements.php');
}

$announcement_id = (int)$_GET['id'];
$error = '';
$success = '';

// Check if columns exist in the announcements table
$is_public_exists = column_exists($pdo, 'announcements', 'is_public');
$is_featured_exists = column_exists($pdo, 'announcements', 'is_featured');
$media_type_exists = column_exists($pdo, 'announcements', 'media_type');
$media_url_exists = column_exists($pdo, 'announcements', 'media_url');
$background_color_exists = column_exists($pdo, 'announcements', 'background_color');
$text_color_exists = column_exists($pdo, 'announcements', 'text_color');
$view_count_exists = column_exists($pdo, 'announcements', 'view_count');
$click_count_exists = column_exists($pdo, 'announcements', 'click_count');
$start_date_exists = column_exists($pdo, 'announcements', 'start_date');
$end_date_exists = column_exists($pdo, 'announcements', 'end_date');

// Record view if user is logged in or announcement is public
try {
    // Build the query with conditional columns
    $query = "
        SELECT a.announcement_id, a.title, a.content, a.sender_user_id,
               a.target_role, a.target_center_id, a.is_active,
               " . ($is_public_exists ? "a.is_public," : "0 AS is_public,") . "
               " . ($is_featured_exists ? "a.is_featured," : "0 AS is_featured,") . "
               " . ($media_type_exists ? "a.media_type," : "'none' AS media_type,") . "
               " . ($media_url_exists ? "a.media_url," : "NULL AS media_url,") . "
               " . ($background_color_exists ? "a.background_color," : "'#ffffff' AS background_color,") . "
               " . ($text_color_exists ? "a.text_color," : "'#000000' AS text_color,") . "
               " . ($start_date_exists ? "a.start_date," : "a.created_at AS start_date,") . "
               " . ($end_date_exists ? "a.end_date," : "DATE_ADD(a.created_at, INTERVAL 30 DAY) AS end_date,") . "
               a.created_at, a.updated_at,
               " . ($view_count_exists ? "COALESCE(a.view_count, 0)" : "0") . " AS view_count,
               " . ($click_count_exists ? "COALESCE(a.click_count, 0)" : "0") . " AS click_count,
               u.full_name AS sender_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        WHERE a.announcement_id = ?
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$announcement_id]);
    $announcement = $stmt->fetch();

    if (!$announcement) {
        set_flash_message('danger', 'الإعلان غير موجود');
        redirect('pages/announcements.php');
    }

    // Check if user has access to this announcement
    $has_access = false;

    if ($is_public_exists && $announcement['is_public']) {
        $has_access = true;
    } elseif (is_logged_in()) {
        if ($announcement['target_role'] === 'all') {
            $has_access = true;
        } elseif ($announcement['target_role'] === $_SESSION['role_name']) {
            $has_access = true;
        } elseif ($announcement['target_role'] === 'center_specific' && isset($_SESSION['center_id']) && $_SESSION['center_id'] == $announcement['target_center_id']) {
            $has_access = true;
        }
    }

    if (!$has_access) {
        set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذا الإعلان');
        redirect('pages/announcements.php');
    }

    // Record view
    record_announcement_view($announcement_id);

    // Get center name if target_role is center_specific
    $center_name = '';
    if ($announcement['target_role'] === 'center_specific' && !empty($announcement['target_center_id'])) {
        $stmt = $pdo->prepare("SELECT center_name FROM centers WHERE center_id = ?");
        $stmt->execute([$announcement['target_center_id']]);
        $center = $stmt->fetch();
        if ($center) {
            $center_name = $center['center_name'];
        }
    }

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الإعلان: ' . $e->getMessage();
}

// Handle click tracking
if (isset($_GET['track_click']) && $_GET['track_click'] === '1') {
    record_announcement_click($announcement_id);

    // Redirect to the URL if provided
    if (!empty($_GET['url'])) {
        $url = sanitize_input($_GET['url']);
        redirect($url);
    }
}

// Page variables
$page_title = 'تفاصيل الإعلان: ' . $announcement['title'];
$active_page = 'announcements';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => is_logged_in() ? (has_role('system_owner') ? 'system_owner_dashboard.php' : (has_role('center_admin') ? 'center_admin_dashboard.php' : 'dashboard.php')) : '../index.php'],
    ['title' => 'الإعلانات', 'url' => 'announcements.php'],
    ['title' => 'تفاصيل الإعلان']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="fas fa-bullhorn me-2"></i>
        <?php echo $announcement['title']; ?>
    </h1>
    <div>
        <a href="announcements.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
        <?php if (has_role('system_owner') || (has_role('center_admin') && $_SESSION['center_id'] == $announcement['target_center_id'])): ?>
            <a href="system_announcements.php?id=<?php echo $announcement_id; ?>" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <a href="announcement_stats.php?id=<?php echo $announcement_id; ?>" class="btn btn-primary">
                <i class="fas fa-chart-bar me-1"></i> الإحصائيات
            </a>
        <?php endif; ?>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> معلومات الإعلان</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-striped">
                    <tr>
                        <th>العنوان</th>
                        <td><?php echo $announcement['title']; ?></td>
                    </tr>
                    <tr>
                        <th>المرسل</th>
                        <td><?php echo $announcement['sender_name']; ?></td>
                    </tr>
                    <tr>
                        <th>تاريخ الإنشاء</th>
                        <td><?php echo date('Y-m-d H:i', strtotime($announcement['created_at'])); ?></td>
                    </tr>
                    <tr>
                        <th>تاريخ البداية</th>
                        <td><?php echo isset($announcement['start_date']) ? $announcement['start_date'] : '-'; ?></td>
                    </tr>
                    <tr>
                        <th>تاريخ النهاية</th>
                        <td><?php echo isset($announcement['end_date']) ? $announcement['end_date'] : '-'; ?></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-striped">
                    <tr>
                        <th>الفئة المستهدفة</th>
                        <td>
                            <?php
                            switch ($announcement['target_role']) {
                                case 'all':
                                    echo 'الجميع';
                                    break;
                                case 'system_owner':
                                    echo 'مدير النظام';
                                    break;
                                case 'center_admin':
                                    echo 'مدير المركز';
                                    break;
                                case 'teacher':
                                    echo 'المعلم';
                                    break;
                                case 'student':
                                    echo 'الطالب';
                                    break;
                                case 'parent':
                                    echo 'ولي الأمر';
                                    break;
                                case 'center_specific':
                                    echo 'مركز محدد';
                                    break;
                                default:
                                    echo $announcement['target_role'];
                            }
                            ?>
                        </td>
                    </tr>
                    <?php if ($announcement['target_role'] == 'center_specific' && !empty($center_name)): ?>
                    <tr>
                        <th>المركز المستهدف</th>
                        <td><?php echo $center_name; ?></td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <th>الحالة</th>
                        <td><?php echo $announcement['is_active'] ? '<span class="badge bg-success">نشط</span>' : '<span class="badge bg-danger">غير نشط</span>'; ?></td>
                    </tr>
                    <tr>
                        <th>ظهور للزوار</th>
                        <td><?php echo $announcement['is_public'] ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-secondary">لا</span>'; ?></td>
                    </tr>
                    <tr>
                        <th>إعلان مميز</th>
                        <td><?php echo $announcement['is_featured'] ? '<span class="badge bg-success">نعم</span>' : '<span class="badge bg-secondary">لا</span>'; ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="card shadow mb-4" style="background-color: <?php echo isset($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>; color: <?php echo isset($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>;">
    <div class="card-header" style="background-color: <?php echo isset($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>; color: <?php echo isset($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>; border-bottom: 1px solid rgba(0,0,0,0.125);">
        <h5 class="card-title mb-0"><i class="fas fa-align-left me-2"></i> محتوى الإعلان</h5>
    </div>
    <div class="card-body">
        <?php if ($media_type_exists && $media_url_exists && isset($announcement['media_type']) && $announcement['media_type'] !== 'none' && !empty($announcement['media_url'])): ?>
            <div class="text-center mb-4">
                <?php if ($announcement['media_type'] === 'image'): ?>
                    <img src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 400px;">
                <?php elseif ($announcement['media_type'] === 'video'): ?>
                    <video controls class="img-fluid rounded" style="max-height: 400px;">
                        <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="video/mp4">
                        متصفحك لا يدعم تشغيل الفيديو.
                    </video>
                <?php elseif ($announcement['media_type'] === 'audio'): ?>
                    <audio controls class="w-100">
                        <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="audio/mpeg">
                        متصفحك لا يدعم تشغيل الصوت.
                    </audio>
                <?php endif; ?>
            </div>
        <?php elseif (!$media_type_exists && !$media_url_exists && isset($announcement['image_url']) && !empty($announcement['image_url'])): ?>
            <div class="text-center mb-4">
                <img src="<?php echo strpos($announcement['image_url'], 'http') === 0 ? $announcement['image_url'] : '../' . $announcement['image_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 400px;">
            </div>
        <?php endif; ?>

        <div class="announcement-content">
            <?php echo $announcement['content']; ?>
        </div>

        <div class="d-flex justify-content-between align-items-center mt-4">
            <div>
                <small class="text-muted">
                    <i class="fas fa-user me-1"></i> <?php echo $announcement['sender_name']; ?> |
                    <i class="fas fa-calendar-alt me-1"></i> <?php echo date('Y-m-d', strtotime($announcement['created_at'])); ?>
                </small>
            </div>
            <div>
                <small class="text-muted">
                    <i class="fas fa-eye me-1"></i> <?php echo isset($announcement['view_count']) ? number_format($announcement['view_count']) : '0'; ?> مشاهدة
                </small>
            </div>
        </div>
    </div>
</div>

<?php if ($media_url_exists && !empty($announcement['media_url']) && strpos($announcement['media_url'], 'http') === 0): ?>
<div class="text-center mb-4">
    <a href="announcement_details.php?id=<?php echo $announcement_id; ?>&track_click=1&url=<?php echo urlencode($announcement['media_url']); ?>" class="btn btn-primary" target="_blank">
        <i class="fas fa-external-link-alt me-1"></i> فتح الرابط
    </a>
</div>
<?php elseif (!$media_url_exists && isset($announcement['image_url']) && !empty($announcement['image_url']) && strpos($announcement['image_url'], 'http') === 0): ?>
<div class="text-center mb-4">
    <a href="announcement_details.php?id=<?php echo $announcement_id; ?>&track_click=1&url=<?php echo urlencode($announcement['image_url']); ?>" class="btn btn-primary" target="_blank">
        <i class="fas fa-external-link-alt me-1"></i> فتح الرابط
    </a>
</div>
<?php endif; ?>

<?php
// Include footer template
include_template('footer');
?>
