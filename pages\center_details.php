<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('auth/login.php');
}

// Check if user has appropriate role
if (!has_any_role(['system_owner', 'center_admin'])) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('index.php');
}

// Get center ID from query string
$center_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Check if center ID is valid
if ($center_id <= 0) {
    set_flash_message('danger', 'معرف المركز غير صالح');
    redirect('centers.php');
}

// Check if user has access to this center
if (has_role('center_admin') && $_SESSION['center_id'] != $center_id) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذا المركز');
    redirect('center_admin_dashboard.php');
}

// Get center information
try {
    $stmt = $pdo->prepare("
        SELECT c.*, 
               u.full_name AS director_name,
               u.email AS director_email,
               u.phone_number AS director_phone,
               (SELECT COUNT(*) FROM circles WHERE center_id = c.center_id) AS circle_count,
               (SELECT COUNT(*) FROM users WHERE center_id = c.center_id AND role_id = (SELECT role_id FROM roles WHERE role_name = 'teacher')) AS teacher_count,
               (SELECT COUNT(*) FROM users WHERE center_id = c.center_id AND role_id = (SELECT role_id FROM roles WHERE role_name = 'student')) AS student_count
        FROM centers c
        LEFT JOIN users u ON c.director_user_id = u.user_id
        WHERE c.center_id = ?
    ");
    $stmt->execute([$center_id]);
    $center = $stmt->fetch();

    if (!$center) {
        set_flash_message('danger', 'لم يتم العثور على المركز');
        redirect('centers.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المركز: ' . $e->getMessage();
}

// Get circles in this center
try {
    $stmt = $pdo->prepare("
        SELECT c.*, 
               u.full_name AS teacher_name,
               (SELECT COUNT(*) FROM student_circle_enrollments WHERE circle_id = c.circle_id AND status = 'approved') AS student_count
        FROM circles c
        LEFT JOIN users u ON c.teacher_user_id = u.user_id
        WHERE c.center_id = ?
        ORDER BY c.circle_name
    ");
    $stmt->execute([$center_id]);
    $circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get teachers in this center
try {
    $stmt = $pdo->prepare("
        SELECT u.*,
               (SELECT COUNT(*) FROM circles WHERE teacher_user_id = u.user_id) AS circle_count,
               (SELECT COUNT(*) FROM student_circle_enrollments sce JOIN circles c ON sce.circle_id = c.circle_id WHERE c.teacher_user_id = u.user_id AND sce.status = 'approved') AS student_count
        FROM users u
        WHERE u.center_id = ? AND u.role_id = (SELECT role_id FROM roles WHERE role_name = 'teacher')
        ORDER BY u.full_name
    ");
    $stmt->execute([$center_id]);
    $teachers = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المعلمين: ' . $e->getMessage();
}

// Page variables
$page_title = 'تفاصيل المركز: ' . $center['center_name'];

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo get_root_url(); ?>index.php">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="<?php echo get_root_url(); ?>pages/centers.php">المراكز</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?php echo $center['center_name']; ?></li>
        </ol>
    </nav>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="row">
        <!-- Center Information -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-building me-2"></i> معلومات المركز</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <?php if (!empty($center['logo_url'])): ?>
                            <img src="<?php echo get_root_url() . $center['logo_url']; ?>" alt="شعار المركز" class="img-fluid mb-3" style="max-height: 150px;">
                        <?php else: ?>
                            <div class="display-1 text-muted">
                                <i class="fas fa-mosque"></i>
                            </div>
                        <?php endif; ?>
                        <h4><?php echo $center['center_name']; ?></h4>
                        <p class="text-muted"><?php echo $center['address']; ?></p>
                        
                        <div class="badge bg-<?php echo $center['is_active'] ? 'success' : 'danger'; ?> mb-3">
                            <?php echo $center['is_active'] ? 'نشط' : 'غير نشط'; ?>
                        </div>
                    </div>
                    
                    <h6 class="border-bottom pb-2 mb-3">معلومات الاتصال</h6>
                    <p><strong>مسؤول الاتصال:</strong> <?php echo $center['contact_person_name']; ?></p>
                    <p><strong>البريد الإلكتروني:</strong> <?php echo $center['contact_email']; ?></p>
                    <p><strong>رقم الهاتف:</strong> <?php echo $center['contact_phone']; ?></p>
                    
                    <?php if ($center['director_name']): ?>
                        <h6 class="border-bottom pb-2 mb-3 mt-4">مدير المركز</h6>
                        <p><strong>الاسم:</strong> <?php echo $center['director_name']; ?></p>
                        <p><strong>البريد الإلكتروني:</strong> <?php echo $center['director_email']; ?></p>
                        <p><strong>رقم الهاتف:</strong> <?php echo $center['director_phone']; ?></p>
                    <?php endif; ?>
                    
                    <h6 class="border-bottom pb-2 mb-3 mt-4">معلومات إضافية</h6>
                    <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d', strtotime($center['created_at'])); ?></p>
                    
                    <?php if (has_role('system_owner')): ?>
                        <div class="d-grid gap-2 mt-4">
                            <a href="edit_center.php?id=<?php echo $center_id; ?>" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i> تعديل المركز
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i> إحصائيات المركز</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h1 class="display-4"><?php echo $center['circle_count']; ?></h1>
                                    <p class="mb-0">الحلقات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h1 class="display-4"><?php echo $center['teacher_count']; ?></h1>
                                    <p class="mb-0">المعلمين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body">
                                    <h1 class="display-4"><?php echo $center['student_count']; ?></h1>
                                    <p class="mb-0">الطلاب</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Circles -->
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> الحلقات (<?php echo count($circles); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($circles)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا توجد حلقات مسجلة في هذا المركز.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الحلقة</th>
                                        <th>المعلم</th>
                                        <th>المستوى</th>
                                        <th>عدد الطلاب</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($circles as $circle): ?>
                                        <tr>
                                            <td><?php echo $circle['circle_name']; ?></td>
                                            <td><?php echo $circle['teacher_name']; ?></td>
                                            <td><?php echo $circle['level']; ?></td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo $circle['student_count']; ?> / <?php echo $circle['max_students']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($circle['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="circle_details.php?id=<?php echo $circle['circle_id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Teachers -->
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-chalkboard-teacher me-2"></i> المعلمين (<?php echo count($teachers); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($teachers)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا يوجد معلمين مسجلين في هذا المركز.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المعلم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>عدد الحلقات</th>
                                        <th>عدد الطلاب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($teachers as $teacher): ?>
                                        <tr>
                                            <td><?php echo $teacher['full_name']; ?></td>
                                            <td><?php echo $teacher['email']; ?></td>
                                            <td><?php echo $teacher['phone_number']; ?></td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo $teacher['circle_count']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?php echo $teacher['student_count']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="user_details.php?id=<?php echo $teacher['user_id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer_inner.php';
?>
