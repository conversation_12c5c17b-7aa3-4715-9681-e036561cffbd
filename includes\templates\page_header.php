<?php
/**
 * Page Header Template
 * 
 * This template displays a page header with title and optional action button
 * 
 * @param string $page_title - The title of the page
 * @param string $page_icon - Font Awesome icon class (optional)
 * @param array $action_button - Action button details ['url' => '', 'text' => '', 'icon' => ''] (optional)
 */

// Default values
$page_icon = $page_icon ?? '';
$action_button = $action_button ?? null;
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <?php if (!empty($page_icon)): ?>
            <i class="<?php echo $page_icon; ?> me-2"></i>
        <?php endif; ?>
        <?php echo $page_title; ?>
    </h1>
    
    <?php if ($action_button): ?>
        <a href="<?php echo $action_button['url']; ?>" class="btn btn-primary">
            <?php if (isset($action_button['icon'])): ?>
                <i class="<?php echo $action_button['icon']; ?> me-1"></i>
            <?php endif; ?>
            <?php echo $action_button['text']; ?>
        </a>
    <?php endif; ?>
</div>
