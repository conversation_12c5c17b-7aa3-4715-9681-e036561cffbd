<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Page variables
$page_title = 'إضافة ولي أمر جديد';
$active_page = 'parents';
$success = '';
$error = '';
$center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_GET['center_id']) ? (int)$_GET['center_id'] : null);

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Check if address column exists
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'address'
    ");
    $stmt->execute();
    $address_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من أعمدة الجدول: ' . $e->getMessage();
    $address_exists = false;
}

// Get students for dropdown
$students = [];
try {
    $query = "
        SELECT u.user_id, u.full_name, u.email
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE r.role_name = 'student' AND u.is_active = TRUE
    ";

    if ($center_id) {
        $query .= " AND u.center_id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$center_id]);
    } else {
        $stmt = $pdo->prepare($query);
        $stmt->execute();
    }

    $students = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $username = sanitize_input($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $address = $address_exists ? sanitize_input($_POST['address']) : '';
    $selected_center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_POST['center_id']) ? (int)$_POST['center_id'] : null);
    $selected_students = isset($_POST['students']) ? $_POST['students'] : [];
    $relation_type = sanitize_input($_POST['relation_type']);

    // Validate required fields
    if (empty($username)) {
        $error = 'يرجى إدخال اسم المستخدم';
    } elseif (empty($password)) {
        $error = 'يرجى إدخال كلمة المرور';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيدها غير متطابقين';
    } elseif (empty($full_name)) {
        $error = 'يرجى إدخال الاسم الكامل';
    } elseif (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (empty($phone_number)) {
        $error = 'يرجى إدخال رقم الهاتف';
    } elseif (has_role('system_owner') && empty($selected_center_id)) {
        $error = 'يرجى اختيار المركز';
    } elseif (empty($selected_students)) {
        $error = 'يرجى اختيار طالب واحد على الأقل';
    } elseif (empty($relation_type)) {
        $error = 'يرجى تحديد نوع العلاقة';
    } else {
        try {
            // Check if username already exists
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->rowCount() > 0) {
                $error = 'اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر';
            } else {
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->rowCount() > 0) {
                    $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد آخر';
                } else {
                    // Get parent role ID
                    $stmt = $pdo->prepare("SELECT role_id FROM roles WHERE role_name = 'parent'");
                    $stmt->execute();
                    $role = $stmt->fetch();
                    $role_id = $role['role_id'];

                    // Hash password
                    $password_hash = password_hash($password, PASSWORD_DEFAULT);

                    // Begin transaction
                    $pdo->beginTransaction();

                    // Check if address column exists
                    $stmt = $pdo->prepare("
                        SELECT COUNT(*)
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'users'
                        AND COLUMN_NAME = 'address'
                    ");
                    $stmt->execute();
                    $address_exists = (bool)$stmt->fetchColumn();

                    // Insert new parent user based on existing columns
                    if ($address_exists) {
                        $stmt = $pdo->prepare("
                            INSERT INTO users (
                                username, password_hash, full_name, email, phone_number,
                                address, role_id, center_id, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                        ");
                        $stmt->execute([
                            $username, $password_hash, $full_name, $email, $phone_number,
                            $address, $role_id, $selected_center_id
                        ]);
                    } else {
                        $stmt = $pdo->prepare("
                            INSERT INTO users (
                                username, password_hash, full_name, email, phone_number,
                                role_id, center_id, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                        ");
                        $stmt->execute([
                            $username, $password_hash, $full_name, $email, $phone_number,
                            $role_id, $selected_center_id
                        ]);
                    }

                    $parent_id = $pdo->lastInsertId();

                    // Add parent-student relations
                    foreach ($selected_students as $student_id) {
                        $stmt = $pdo->prepare("
                            INSERT INTO parent_student_relations (
                                parent_user_id, student_user_id, relation_type, is_primary
                            ) VALUES (?, ?, ?, 1)
                        ");
                        $stmt->execute([$parent_id, $student_id, $relation_type]);
                    }

                    // Handle profile picture upload if provided
                    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                        $upload_dir = '../assets/images/profiles/';

                        // Create directory if it doesn't exist
                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0777, true);
                        }

                        $file_extension = pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION);
                        $new_filename = 'user_' . $parent_id . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;

                        if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                            // Update user with profile picture URL
                            $profile_picture_url = 'assets/images/profiles/' . $new_filename;
                            $stmt = $pdo->prepare("UPDATE users SET profile_picture_url = ? WHERE user_id = ?");
                            $stmt->execute([$profile_picture_url, $parent_id]);
                        }
                    }

                    // Commit transaction
                    $pdo->commit();

                    $success = 'تم إضافة ولي الأمر بنجاح';

                    // Redirect to parents list
                    set_flash_message('success', $success);
                    redirect('pages/parents.php');
                }
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء إضافة ولي الأمر: ' . $e->getMessage();
        }
    }
}

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'أولياء الأمور', 'url' => 'parents.php'],
    ['title' => 'إضافة ولي أمر جديد']
];

// Set up action button
$action_button = [
    'url' => 'parents.php',
    'text' => 'العودة إلى قائمة أولياء الأمور',
    'icon' => 'fas fa-arrow-right'
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-user-friends'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-user-plus me-2"></i> معلومات ولي الأمر الجديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" required value="<?php echo isset($_POST['username']) ? $_POST['username'] : ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required value="<?php echo isset($_POST['full_name']) ? $_POST['full_name'] : ''; ?>">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" required value="<?php echo isset($_POST['email']) ? $_POST['email'] : ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="phone_number" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                    <input type="tel" class="form-control" id="phone_number" name="phone_number" required value="<?php echo isset($_POST['phone_number']) ? $_POST['phone_number'] : ''; ?>">
                </div>
            </div>

            <div class="row">
                <?php if ($address_exists): ?>
                <div class="col-md-6 mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea class="form-control" id="address" name="address" rows="2"><?php echo isset($_POST['address']) ? $_POST['address'] : ''; ?></textarea>
                </div>
                <?php endif; ?>

                <div class="col-md-<?php echo $address_exists ? '6' : '12'; ?> mb-3">
                    <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                    <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                </div>
            </div>

            <?php if (has_role('system_owner')): ?>
            <div class="mb-3">
                <label for="center_id" class="form-label">المركز <span class="text-danger">*</span></label>
                <select class="form-select" id="center_id" name="center_id" required>
                    <option value="">-- اختر المركز --</option>
                    <?php foreach ($centers as $center): ?>
                        <option value="<?php echo $center['center_id']; ?>" <?php echo (isset($_POST['center_id']) && $_POST['center_id'] == $center['center_id']) || $center_id == $center['center_id'] ? 'selected' : ''; ?>>
                            <?php echo $center['center_name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php endif; ?>

            <div class="mb-3">
                <label for="relation_type" class="form-label">نوع العلاقة <span class="text-danger">*</span></label>
                <select class="form-select" id="relation_type" name="relation_type" required>
                    <option value="">-- اختر نوع العلاقة --</option>
                    <option value="أب" <?php echo (isset($_POST['relation_type']) && $_POST['relation_type'] == 'أب') ? 'selected' : ''; ?>>أب</option>
                    <option value="أم" <?php echo (isset($_POST['relation_type']) && $_POST['relation_type'] == 'أم') ? 'selected' : ''; ?>>أم</option>
                    <option value="وصي" <?php echo (isset($_POST['relation_type']) && $_POST['relation_type'] == 'وصي') ? 'selected' : ''; ?>>وصي</option>
                    <option value="أخرى" <?php echo (isset($_POST['relation_type']) && $_POST['relation_type'] == 'أخرى') ? 'selected' : ''; ?>>أخرى</option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">الأبناء المسجلين في النظام <span class="text-danger">*</span></label>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    يمكنك اختيار طالب واحد أو أكثر من أبنائك المسجلين في النظام لربطهم بحسابك كولي أمر
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <?php if (empty($students)): ?>
                                <div class="col-12">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        لا يوجد طلاب مسجلين في النظام حالياً. يرجى التأكد من تسجيل أبنائك كطلاب أولاً.
                                    </div>
                                </div>
                            <?php else: ?>
                                <?php foreach ($students as $student): ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check student-check-item">
                                            <input class="form-check-input" type="checkbox" name="students[]"
                                                id="student_<?php echo $student['user_id']; ?>"
                                                value="<?php echo $student['user_id']; ?>"
                                                <?php echo (isset($_POST['students']) && in_array($student['user_id'], $_POST['students'])) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="student_<?php echo $student['user_id']; ?>">
                                                <strong><?php echo $student['full_name']; ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo $student['email']; ?></small>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="mt-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllStudents">اختيار الكل</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllStudents">إلغاء اختيار الكل</button>
                </div>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // أزرار اختيار/إلغاء اختيار الكل
                    document.getElementById('selectAllStudents').addEventListener('click', function() {
                        document.querySelectorAll('input[name="students[]"]').forEach(function(checkbox) {
                            checkbox.checked = true;
                        });
                    });

                    document.getElementById('deselectAllStudents').addEventListener('click', function() {
                        document.querySelectorAll('input[name="students[]"]').forEach(function(checkbox) {
                            checkbox.checked = false;
                        });
                    });
                });
            </script>

            <style>
                .student-check-item {
                    padding: 10px;
                    border-radius: 5px;
                    transition: background-color 0.2s;
                }
                .student-check-item:hover {
                    background-color: #f8f9fa;
                }
            </style>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="parents.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">إضافة ولي الأمر</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (has_role('system_owner')): ?>
    // Update students list when center changes
    document.getElementById('center_id').addEventListener('change', function() {
        const centerId = this.value;
        const studentsContainer = document.querySelector('.card .card-body .row');

        // Show loading indicator
        studentsContainer.innerHTML = '<div class="col-12 text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>';

        if (centerId) {
            // Fetch students for selected center
            fetch('api/get_students.php?center_id=' + centerId)
                .then(response => response.json())
                .then(data => {
                    if (data.length === 0) {
                        studentsContainer.innerHTML = `
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    لا يوجد طلاب مسجلين في هذا المركز. يرجى التأكد من تسجيل الطلاب أولاً.
                                </div>
                            </div>
                        `;
                    } else {
                        studentsContainer.innerHTML = '';
                        data.forEach(student => {
                            const studentDiv = document.createElement('div');
                            studentDiv.className = 'col-md-6 mb-2';
                            studentDiv.innerHTML = `
                                <div class="form-check student-check-item">
                                    <input class="form-check-input" type="checkbox" name="students[]"
                                        id="student_${student.user_id}"
                                        value="${student.user_id}">
                                    <label class="form-check-label" for="student_${student.user_id}">
                                        <strong>${student.full_name}</strong>
                                        <br>
                                        <small class="text-muted">${student.email}</small>
                                    </label>
                                </div>
                            `;
                            studentsContainer.appendChild(studentDiv);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching students:', error);
                    studentsContainer.innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                حدث خطأ أثناء جلب بيانات الطلاب. يرجى المحاولة مرة أخرى.
                            </div>
                        </div>
                    `;
                });
        } else {
            studentsContainer.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يرجى اختيار مركز لعرض الطلاب المسجلين فيه.
                    </div>
                </div>
            `;
        }
    });
    <?php endif; ?>
});
</script>

<?php
// Include footer template
include_template('footer');
?>
