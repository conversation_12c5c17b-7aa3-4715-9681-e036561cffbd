<?php
// Test file to check path resolution

echo "<h1>Path Test</h1>";
echo "<p>Current file: " . __FILE__ . "</p>";
echo "<p>Current directory: " . __DIR__ . "</p>";
echo "<p>Document root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

// Try to include session_config.php
$root_path = dirname(__FILE__) . '/';
$session_config_path = $root_path . 'includes/session_config.php';

echo "<p>Session config path: " . $session_config_path . "</p>";
echo "<p>File exists: " . (file_exists($session_config_path) ? 'Yes' : 'No') . "</p>";

// List files in includes directory
echo "<h2>Files in includes directory:</h2>";
$includes_dir = $root_path . 'includes/';
if (is_dir($includes_dir)) {
    $files = scandir($includes_dir);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            echo "<li>" . $file . "</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p>Includes directory not found!</p>";
}
?>
