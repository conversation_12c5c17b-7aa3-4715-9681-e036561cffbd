<?php
require_once '../includes/session_config.php';
start_secure_session();
require_once '../includes/config.php';
require_once '../includes/db_connect.php';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    redirect('index.php');
}

$error = '';
$success = '';
$token = '';
$valid_token = false;

// Check if token is provided
if (isset($_GET['token'])) {
    $token = $_GET['token'];

    try {
        // Check if token exists and is valid
        $stmt = $pdo->prepare("
            SELECT pr.user_id, u.full_name
            FROM password_resets pr
            JOIN users u ON pr.user_id = u.user_id
            WHERE pr.token = ? AND pr.expires_at > NOW() AND pr.used = FALSE
        ");
        $stmt->execute([$token]);
        $reset = $stmt->fetch();

        if ($reset) {
            $valid_token = true;
            $user_id = $reset['user_id'];
            $full_name = $reset['full_name'];
        } else {
            $error = 'رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية.';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الرابط: ' . $e->getMessage();
    }
} else {
    $error = 'رابط إعادة تعيين كلمة المرور غير صالح.';
}

// Process reset password form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $valid_token) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    if (empty($password) || empty($confirm_password)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقين';
    } elseif (strlen($password) < 6) {
        $error = 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
    } else {
        try {
            // Update user password
            $password_hash = password_hash($password, PASSWORD_DEFAULT);

            $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE user_id = ?");
            $stmt->execute([$password_hash, $user_id]);

            // Mark token as used
            $stmt = $pdo->prepare("UPDATE password_resets SET used = TRUE WHERE token = ?");
            $stmt->execute([$token]);

            $success = 'تم إعادة تعيين كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إعادة تعيين كلمة المرور: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-light">
    <div class="container">
        <div class="auth-form">
            <h2 class="form-title">إعادة تعيين كلمة المرور</h2>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
                <div class="text-center mb-3">
                    <a href="forgot_password.php" class="btn btn-primary">طلب رابط جديد</a>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
                <div class="text-center mb-3">
                    <a href="login.php" class="btn btn-primary">تسجيل الدخول</a>
                </div>
            <?php elseif ($valid_token): ?>
                <p class="text-center mb-4">مرحباً <?php echo $full_name; ?>، يرجى إدخال كلمة المرور الجديدة.</p>

                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?token=' . $token; ?>">
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور الجديدة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <button class="btn btn-outline-secondary toggle-password" type="button" toggle="#password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <small class="text-muted">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">إعادة تعيين كلمة المرور</button>
                    </div>
                </form>
            <?php endif; ?>

            <div class="form-footer">
                <p>تذكرت كلمة المرور؟ <a href="login.php">تسجيل الدخول</a></p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>
</body>
</html>
