<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Page variables
$page_title = 'إدارة أولياء الأمور';
$active_page = 'parents';
$success = '';
$error = '';
$center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_GET['center_id']) ? (int)$_GET['center_id'] : null);

// Handle parent actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $parent_id = (int)$_GET['id'];
    
    if ($action === 'delete') {
        try {
            // Check if parent has relations with students
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM parent_student_relations WHERE parent_user_id = ?");
            $stmt->execute([$parent_id]);
            $relation_count = $stmt->fetchColumn();
            
            if ($relation_count > 0) {
                $error = 'لا يمكن حذف ولي الأمر لأنه مرتبط بطلاب. قم بإلغاء ارتباط الطلاب أولاً.';
            } else {
                // Delete parent
                $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ?");
                $stmt->execute([$parent_id]);
                $success = 'تم حذف ولي الأمر بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء حذف ولي الأمر: ' . $e->getMessage();
        }
    } elseif ($action === 'toggle') {
        try {
            // Toggle parent active status
            $stmt = $pdo->prepare("UPDATE users SET is_active = NOT is_active WHERE user_id = ?");
            $stmt->execute([$parent_id]);
            $success = 'تم تغيير حالة ولي الأمر بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تغيير حالة ولي الأمر: ' . $e->getMessage();
        }
    }
}

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Get parents based on role and filters
try {
    $query = "
        SELECT u.user_id, u.full_name, u.username, u.email, u.phone_number, u.is_active,
               u.created_at, c.center_name,
               (SELECT COUNT(*) FROM parent_student_relations psr WHERE psr.parent_user_id = u.user_id) AS children_count
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE r.role_name = 'parent'
    ";
    
    $params = [];
    
    if (has_role('center_admin')) {
        $query .= " AND u.center_id = ?";
        $params[] = $_SESSION['center_id'];
    } elseif (has_role('system_owner') && $center_id) {
        $query .= " AND u.center_id = ?";
        $params[] = $center_id;
    }
    
    $query .= " ORDER BY u.full_name";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $parents = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات أولياء الأمور: ' . $e->getMessage();
}

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true
]);

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'أولياء الأمور']
];

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-user-friends',
    'action_button' => [
        'url' => 'add_parent.php',
        'text' => 'إضافة ولي أمر جديد',
        'icon' => 'fas fa-plus'
    ]
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<!-- Filters -->
<?php if (has_role('system_owner')): ?>
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i> تصفية أولياء الأمور</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="center_id" class="form-label">المركز</label>
                <select class="form-select" id="center_id" name="center_id">
                    <option value="">جميع المراكز</option>
                    <?php foreach ($centers as $center): ?>
                        <option value="<?php echo $center['center_id']; ?>" <?php echo $center_id == $center['center_id'] ? 'selected' : ''; ?>>
                            <?php echo $center['center_name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-6 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-1"></i> تصفية
                </button>
                <a href="parents.php" class="btn btn-secondary">
                    <i class="fas fa-redo me-1"></i> إعادة ضبط
                </a>
            </div>
        </form>
    </div>
</div>
<?php endif; ?>

<?php if (empty($parents)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> لا يوجد أولياء أمور مطابقين لمعايير البحث.
    </div>
<?php else: ?>
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table id="parentsTable" class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>الاسم الكامل</th>
                            <th>اسم المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>رقم الهاتف</th>
                            <?php if (has_role('system_owner')): ?>
                                <th>المركز</th>
                            <?php endif; ?>
                            <th>عدد الأبناء</th>
                            <th>تاريخ التسجيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($parents as $parent): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $parent['full_name']; ?></strong>
                                </td>
                                <td><?php echo $parent['username']; ?></td>
                                <td><?php echo $parent['email']; ?></td>
                                <td><?php echo $parent['phone_number']; ?></td>
                                <?php if (has_role('system_owner')): ?>
                                    <td><?php echo $parent['center_name']; ?></td>
                                <?php endif; ?>
                                <td>
                                    <span class="badge bg-info">
                                        <?php echo $parent['children_count']; ?>
                                    </span>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($parent['created_at'])); ?></td>
                                <td>
                                    <?php if ($parent['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="parent_details.php?id=<?php echo $parent['user_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit_parent.php?id=<?php echo $parent['user_id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="parents.php?action=toggle&id=<?php echo $parent['user_id']; ?><?php echo $center_id ? '&center_id=' . $center_id : ''; ?>" class="btn btn-sm btn-secondary" title="<?php echo $parent['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                            <i class="fas <?php echo $parent['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                        </a>
                                        <a href="parents.php?action=delete&id=<?php echo $parent['user_id']; ?><?php echo $center_id ? '&center_id=' . $center_id : ''; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف ولي الأمر؟')">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (has_role('system_owner')): ?>
    // Update form when center changes
    document.getElementById('center_id').addEventListener('change', function() {
        const form = this.form;
        form.submit();
    });
    <?php endif; ?>
});
</script>

<?php
// Include footer template
include_template('footer');
?>
