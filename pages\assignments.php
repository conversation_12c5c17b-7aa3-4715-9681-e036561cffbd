<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('../auth/login.php');
}

// Check if user has appropriate role
if (!has_any_role(['teacher', 'center_admin', 'system_owner'])) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('../index.php');
}

$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$error = '';
$success = '';

// Get circle ID from query string if provided
$circle_id = isset($_GET['circle_id']) ? (int)$_GET['circle_id'] : null;

// Get user's circles based on role
try {
    if ($role_name === 'teacher') {
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name
            FROM circles c
            WHERE c.teacher_user_id = ? AND c.is_active = TRUE
            ORDER BY c.circle_name
        ");
        $stmt->execute([$user_id]);
    } elseif ($role_name === 'center_admin') {
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name
            FROM circles c
            WHERE c.center_id = ? AND c.is_active = TRUE
            ORDER BY c.circle_name
        ");
        $stmt->execute([$_SESSION['center_id']]);
    } else { // system_owner
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name
            FROM circles c
            WHERE c.is_active = TRUE
            ORDER BY c.circle_name
        ");
        $stmt->execute();
    }

    $circles = $stmt->fetchAll();

    if (empty($circles)) {
        $error = 'لا توجد حلقات نشطة حالياً';
    } elseif (!$circle_id) {
        // If no circle ID provided, use the first one
        $circle_id = $circles[0]['circle_id'];
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get selected circle information
if ($circle_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name, c.level, c.teacher_user_id, u.full_name AS teacher_name
            FROM circles c
            JOIN users u ON c.teacher_user_id = u.user_id
            WHERE c.circle_id = ?
        ");
        $stmt->execute([$circle_id]);
        $selected_circle = $stmt->fetch();

        // Check if user has access to this circle
        if ($role_name === 'teacher' && $selected_circle['teacher_user_id'] != $user_id) {
            set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الحلقة');
            redirect('teacher_dashboard.php');
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
    }
}

// Check if student_assignments table exists
$student_assignments_exists = false;
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'student_assignments'
    ");
    $stmt->execute();
    $student_assignments_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من وجود جدول واجبات الطلاب: ' . $e->getMessage();
}

// Get assignments for the selected circle
if ($circle_id && empty($error)) {
    try {
        if ($student_assignments_exists) {
            $stmt = $pdo->prepare("
                SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                       u.full_name AS created_by_name,
                       (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id) AS total_students,
                       (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id AND sa.status = 'submitted') AS submitted_count,
                       (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id AND sa.status = 'graded') AS graded_count
                FROM assignments a
                JOIN users u ON a.created_by_user_id = u.user_id
                WHERE a.circle_id = ?
                ORDER BY a.due_date DESC
            ");
        } else {
            // If student_assignments table doesn't exist, use a simpler query
            $stmt = $pdo->prepare("
                SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                       u.full_name AS created_by_name,
                       0 AS total_students,
                       0 AS submitted_count,
                       0 AS graded_count
                FROM assignments a
                JOIN users u ON a.created_by_user_id = u.user_id
                WHERE a.circle_id = ?
                ORDER BY a.due_date DESC
            ");
        }
        $stmt->execute([$circle_id]);
        $assignments = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الواجبات: ' . $e->getMessage();
    }
}

// Get students in the selected circle
if ($circle_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.full_name
            FROM student_circle_enrollments sce
            JOIN users u ON sce.student_user_id = u.user_id
            WHERE sce.circle_id = ? AND sce.status = 'approved'
            ORDER BY u.full_name
        ");
        $stmt->execute([$circle_id]);
        $students = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب: ' . $e->getMessage();
    }
}

// Process add assignment form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_assignment'])) {
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $due_date = sanitize_input($_POST['due_date']);
    $selected_circle_id = (int)$_POST['circle_id'];

    // Validate input
    if (empty($title) || empty($due_date)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $due_date)) {
        $error = 'تنسيق التاريخ غير صالح';
    } else {
        try {
            // Verify user has access to this circle
            if ($role_name === 'teacher') {
                $stmt = $pdo->prepare("SELECT circle_id FROM circles WHERE circle_id = ? AND teacher_user_id = ?");
                $stmt->execute([$selected_circle_id, $user_id]);
                if ($stmt->rowCount() === 0) {
                    $error = 'غير مصرح لك بإضافة واجبات لهذه الحلقة';
                }
            } elseif ($role_name === 'center_admin') {
                $stmt = $pdo->prepare("SELECT circle_id FROM circles WHERE circle_id = ? AND center_id = ?");
                $stmt->execute([$selected_circle_id, $_SESSION['center_id']]);
                if ($stmt->rowCount() === 0) {
                    $error = 'غير مصرح لك بإضافة واجبات لهذه الحلقة';
                }
            }

            if (empty($error)) {
                // Start transaction
                $pdo->beginTransaction();

                // Insert assignment
                $stmt = $pdo->prepare("
                    INSERT INTO assignments (circle_id, title, description, due_date, created_by_user_id)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$selected_circle_id, $title, $description, $due_date, $user_id]);

                $assignment_id = $pdo->lastInsertId();

                // Assign to all students in the circle if student_assignments table exists
                if ($student_assignments_exists) {
                    $stmt = $pdo->prepare("
                        INSERT INTO student_assignments (assignment_id, student_user_id, status)
                        SELECT ?, sce.student_user_id, 'pending'
                        FROM student_circle_enrollments sce
                        WHERE sce.circle_id = ? AND sce.status = 'approved'
                    ");
                    $stmt->execute([$assignment_id, $selected_circle_id]);
                } else {
                    // If student_assignments table doesn't exist, show a warning
                    $success = 'تم إضافة الواجب بنجاح، ولكن لم يتم تعيينه للطلاب لأن جدول واجبات الطلاب غير موجود. <a href="' . get_root_url() . 'create_student_assignments_table.php" class="alert-link">انقر هنا لإنشاء الجدول</a>';
                }

                // Commit transaction
                $pdo->commit();

                $success = 'تم إضافة الواجب بنجاح';

                // Refresh assignments
                $stmt = $pdo->prepare("
                    SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                           u.full_name AS created_by_name,
                           (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id) AS total_students,
                           (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id AND sa.status = 'submitted') AS submitted_count,
                           (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id AND sa.status = 'graded') AS graded_count
                    FROM assignments a
                    JOIN users u ON a.created_by_user_id = u.user_id
                    WHERE a.circle_id = ?
                    ORDER BY a.due_date DESC
                ");
                $stmt->execute([$selected_circle_id]);
                $assignments = $stmt->fetchAll();

                // Update circle_id to the selected one
                $circle_id = $selected_circle_id;
            }
        } catch (PDOException $e) {
            // Rollback transaction if active
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }

            $error = 'حدث خطأ أثناء إضافة الواجب: ' . $e->getMessage();
        }
    }
}

// Process delete assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_assignment'])) {
    $assignment_id = (int)$_POST['assignment_id'];

    try {
        // Verify user has access to this assignment
        if ($role_name === 'teacher') {
            $stmt = $pdo->prepare("
                SELECT a.assignment_id
                FROM assignments a
                JOIN circles c ON a.circle_id = c.circle_id
                WHERE a.assignment_id = ? AND c.teacher_user_id = ?
            ");
            $stmt->execute([$assignment_id, $user_id]);
        } elseif ($role_name === 'center_admin') {
            $stmt = $pdo->prepare("
                SELECT a.assignment_id
                FROM assignments a
                JOIN circles c ON a.circle_id = c.circle_id
                WHERE a.assignment_id = ? AND c.center_id = ?
            ");
            $stmt->execute([$assignment_id, $_SESSION['center_id']]);
        } else { // system_owner
            $stmt = $pdo->prepare("SELECT assignment_id FROM assignments WHERE assignment_id = ?");
            $stmt->execute([$assignment_id]);
        }

        if ($stmt->rowCount() === 0) {
            $error = 'غير مصرح لك بحذف هذا الواجب';
        } else {
            // Start transaction
            $pdo->beginTransaction();

            // Delete student assignments if the table exists
            if ($student_assignments_exists) {
                $stmt = $pdo->prepare("DELETE FROM student_assignments WHERE assignment_id = ?");
                $stmt->execute([$assignment_id]);
            }

            // Delete assignment
            $stmt = $pdo->prepare("DELETE FROM assignments WHERE assignment_id = ?");
            $stmt->execute([$assignment_id]);

            // Commit transaction
            $pdo->commit();

            $success = 'تم حذف الواجب بنجاح';

            // Refresh assignments
            $stmt = $pdo->prepare("
                SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                       u.full_name AS created_by_name,
                       (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id) AS total_students,
                       (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id AND sa.status = 'submitted') AS submitted_count,
                       (SELECT COUNT(*) FROM student_assignments sa WHERE sa.assignment_id = a.assignment_id AND sa.status = 'graded') AS graded_count
                FROM assignments a
                JOIN users u ON a.created_by_user_id = u.user_id
                WHERE a.circle_id = ?
                ORDER BY a.due_date DESC
            ");
            $stmt->execute([$circle_id]);
            $assignments = $stmt->fetchAll();
        }
    } catch (PDOException $e) {
        // Rollback transaction if active
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        $error = 'حدث خطأ أثناء حذف الواجب: ' . $e->getMessage();
    }
}
?>

<?php
// Set page title
$page_title = 'إدارة الواجبات';

// Include header
include_once '../includes/header_inner.php';
?>

    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>إدارة الواجبات</h1>
            <?php if (!empty($selected_circle)): ?>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAssignmentModal">
                    <i class="fas fa-plus me-1"></i> إضافة واجب جديد
                </button>
            <?php endif; ?>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <?php if (isset($student_assignments_exists) && !$student_assignments_exists): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> جدول واجبات الطلاب (student_assignments) غير موجود. بعض وظائف الواجبات قد لا تعمل بشكل صحيح.
                <a href="<?php echo get_root_url(); ?>create_student_assignments_table.php" class="alert-link">انقر هنا لإنشاء الجدول</a>
            </div>
        <?php endif; ?>

        <?php if (!empty($circles)): ?>
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">اختر الحلقة</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="row g-3">
                        <div class="col-md-6">
                            <select class="form-select" id="circle_id" name="circle_id" required>
                                <?php foreach ($circles as $circle): ?>
                                    <option value="<?php echo $circle['circle_id']; ?>" <?php echo $circle['circle_id'] == $circle_id ? 'selected' : ''; ?>>
                                        <?php echo $circle['circle_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary">عرض الواجبات</button>
                        </div>
                    </form>
                </div>
            </div>

            <?php if (!empty($selected_circle)): ?>
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tasks me-2"></i>
                            الواجبات - <?php echo $selected_circle['circle_name']; ?>
                            (المستوى: <?php echo $selected_circle['level']; ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($assignments)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> لا توجد واجبات مسجلة لهذه الحلقة.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>العنوان</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>حالة التسليم</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>بواسطة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($assignments as $assignment): ?>
                                            <tr>
                                                <td><?php echo $assignment['title']; ?></td>
                                                <td>
                                                    <?php
                                                    $due_date = strtotime($assignment['due_date']);
                                                    $now = time();
                                                    $days_diff = round(($due_date - $now) / (60 * 60 * 24));

                                                    echo date('Y-m-d', $due_date);

                                                    if ($days_diff < 0) {
                                                        echo ' <span class="badge bg-danger">انتهى</span>';
                                                    } elseif ($days_diff == 0) {
                                                        echo ' <span class="badge bg-warning">اليوم</span>';
                                                    } else {
                                                        echo ' <span class="badge bg-info">متبقي ' . $days_diff . ' يوم</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <?php
                                                        $total = $assignment['total_students'];
                                                        $submitted = $assignment['submitted_count'];
                                                        $graded = $assignment['graded_count'];

                                                        $submitted_percentage = $total > 0 ? round(($submitted / $total) * 100) : 0;
                                                        $graded_percentage = $total > 0 ? round(($graded / $total) * 100) : 0;
                                                        ?>
                                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $graded_percentage; ?>%"
                                                             aria-valuenow="<?php echo $graded_percentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                                            <?php echo $graded; ?> تم تقييمه
                                                        </div>
                                                        <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo $submitted_percentage - $graded_percentage; ?>%"
                                                             aria-valuenow="<?php echo $submitted_percentage - $graded_percentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                                            <?php echo $submitted - $graded; ?> تم تسليمه
                                                        </div>
                                                    </div>
                                                    <small><?php echo $submitted; ?> من <?php echo $total; ?> طالب</small>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($assignment['created_at'])); ?></td>
                                                <td><?php echo $assignment['created_by_name']; ?></td>
                                                <td>
                                                    <a href="assignment_details.php?id=<?php echo $assignment['assignment_id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAssignmentModal<?php echo $assignment['assignment_id']; ?>">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </button>

                                                    <!-- Delete Assignment Modal -->
                                                    <div class="modal fade" id="deleteAssignmentModal<?php echo $assignment['assignment_id']; ?>" tabindex="-1" aria-labelledby="deleteAssignmentModalLabel<?php echo $assignment['assignment_id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="deleteAssignmentModalLabel<?php echo $assignment['assignment_id']; ?>">تأكيد الحذف</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <p>هل أنت متأكد من رغبتك في حذف الواجب "<?php echo $assignment['title']; ?>"؟</p>
                                                                    <p class="text-danger">سيتم حذف جميع التسليمات المرتبطة بهذا الواجب.</p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?circle_id=' . $circle_id; ?>">
                                                                        <input type="hidden" name="assignment_id" value="<?php echo $assignment['assignment_id']; ?>">
                                                                        <button type="submit" name="delete_assignment" class="btn btn-danger">حذف</button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <!-- Add Assignment Modal -->
    <div class="modal fade" id="addAssignmentModal" tabindex="-1" aria-labelledby="addAssignmentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAssignmentModalLabel">إضافة واجب جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?circle_id=' . $circle_id; ?>">
                    <div class="modal-body">
                        <input type="hidden" name="circle_id" value="<?php echo $circle_id; ?>">

                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان الواجب <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الواجب</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="due_date" class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="due_date" name="due_date" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="add_assignment" class="btn btn-primary">إضافة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form when changing circle
            const circleSelect = document.getElementById('circle_id');

            if (circleSelect) {
                circleSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }
        });
    </script>

<?php
// Include footer
include_once '../includes/footer_inner.php';
?>
