<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Set page title
$page_title = 'إعداد قاعدة البيانات';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success_messages = [];
$error_messages = [];

// Function to execute SQL file
function execute_sql_file($pdo, $file_path) {
    if (!file_exists($file_path)) {
        return "الملف غير موجود: $file_path";
    }

    $sql = file_get_contents($file_path);
    
    // Split SQL by semicolon to get individual queries
    $queries = explode(';', $sql);
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (empty($query)) continue;
        
        try {
            $pdo->exec($query);
        } catch (PDOException $e) {
            return "خطأ في تنفيذ الاستعلام: " . $e->getMessage();
        }
    }
    
    return true;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_assignments_table'])) {
        $result = execute_sql_file($pdo, ROOT_PATH . 'sql/create_assignments_table.sql');
        if ($result === true) {
            $success_messages[] = 'تم إنشاء جدول الواجبات بنجاح';
        } else {
            $error_messages[] = $result;
        }
    }
    
    if (isset($_POST['create_student_assignments_table'])) {
        $result = execute_sql_file($pdo, ROOT_PATH . 'sql/create_student_assignments_table.sql');
        if ($result === true) {
            $success_messages[] = 'تم إنشاء جدول واجبات الطلاب بنجاح';
        } else {
            $error_messages[] = $result;
        }
    }
    
    if (isset($_POST['insert_remaining_surahs'])) {
        $result = execute_sql_file($pdo, ROOT_PATH . 'sql/insert_remaining_surahs.sql');
        if ($result === true) {
            $success_messages[] = 'تم إدخال باقي السور بنجاح';
        } else {
            $error_messages[] = $result;
        }
    }
}

// Check if tables exist
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'assignments'
    ");
    $stmt->execute();
    $assignments_table_exists = (bool)$stmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'student_assignments'
    ");
    $stmt->execute();
    $student_assignments_table_exists = (bool)$stmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'quran_surahs'
    ");
    $stmt->execute();
    $quran_surahs_table_exists = (bool)$stmt->fetchColumn();
    
    if ($quran_surahs_table_exists) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM quran_surahs");
        $stmt->execute();
        $surah_count = $stmt->fetchColumn();
    } else {
        $surah_count = 0;
    }
} catch (PDOException $e) {
    $error_messages[] = 'حدث خطأ أثناء التحقق من وجود الجداول: ' . $e->getMessage();
}

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">إعداد قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_messages)): ?>
                        <?php foreach ($success_messages as $message): ?>
                            <div class="alert alert-success"><?php echo $message; ?></div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_messages)): ?>
                        <?php foreach ($error_messages as $message): ?>
                            <div class="alert alert-danger"><?php echo $message; ?></div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                        <div class="mb-3">
                            <h5>جدول الواجبات (assignments)</h5>
                            <p>
                                الحالة: 
                                <?php if ($assignments_table_exists): ?>
                                    <span class="badge bg-success">موجود</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير موجود</span>
                                <?php endif; ?>
                            </p>
                            <button type="submit" name="create_assignments_table" class="btn btn-primary" <?php echo $assignments_table_exists ? 'disabled' : ''; ?>>
                                إنشاء جدول الواجبات
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <h5>جدول واجبات الطلاب (student_assignments)</h5>
                            <p>
                                الحالة: 
                                <?php if ($student_assignments_table_exists): ?>
                                    <span class="badge bg-success">موجود</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير موجود</span>
                                <?php endif; ?>
                            </p>
                            <button type="submit" name="create_student_assignments_table" class="btn btn-primary" <?php echo $student_assignments_table_exists || !$assignments_table_exists ? 'disabled' : ''; ?>>
                                إنشاء جدول واجبات الطلاب
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <h5>جدول سور القرآن (quran_surahs)</h5>
                            <p>
                                الحالة: 
                                <?php if ($quran_surahs_table_exists): ?>
                                    <span class="badge bg-success">موجود</span>
                                    <span class="ms-2">عدد السور: <?php echo $surah_count; ?> / 114</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير موجود</span>
                                <?php endif; ?>
                            </p>
                            <button type="submit" name="insert_remaining_surahs" class="btn btn-primary" <?php echo !$quran_surahs_table_exists || $surah_count >= 114 ? 'disabled' : ''; ?>>
                                إدخال باقي السور
                            </button>
                        </div>
                        
                        <div class="mt-4">
                            <a href="<?php echo get_root_url(); ?>pages/system_owner_dashboard.php" class="btn btn-secondary">
                                العودة إلى لوحة التحكم
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer_inner.php';
?>
