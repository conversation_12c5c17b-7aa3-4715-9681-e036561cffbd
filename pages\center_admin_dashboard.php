<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has center_admin role
if (!is_logged_in() || !has_role('center_admin')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Get admin information
$admin_id = $_SESSION['user_id'];
$center_id = $_SESSION['center_id'];

// Get center information
try {
    $stmt = $pdo->prepare("
        SELECT center_name, address, contact_person_name, contact_email, contact_phone, logo_url
        FROM centers
        WHERE center_id = ?
    ");
    $stmt->execute([$center_id]);
    $center = $stmt->fetch();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المركز: ' . $e->getMessage();
}

// Get statistics
try {
    // Count teachers
    $stmt = $pdo->prepare("
        SELECT COUNT(*) AS teacher_count
        FROM users
        WHERE role_id = (SELECT role_id FROM roles WHERE role_name = 'teacher')
        AND center_id = ?
    ");
    $stmt->execute([$center_id]);
    $teacher_count = $stmt->fetch()['teacher_count'];

    // Count students
    $stmt = $pdo->prepare("
        SELECT COUNT(*) AS student_count
        FROM users
        WHERE role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
        AND center_id = ?
    ");
    $stmt->execute([$center_id]);
    $student_count = $stmt->fetch()['student_count'];

    // Count circles
    $stmt = $pdo->prepare("
        SELECT COUNT(*) AS circle_count
        FROM circles
        WHERE center_id = ? AND is_active = TRUE
    ");
    $stmt->execute([$center_id]);
    $circle_count = $stmt->fetch()['circle_count'];

    // Count parents
    $stmt = $pdo->prepare("
        SELECT COUNT(DISTINCT parent_user_id) AS parent_count
        FROM student_circle_enrollments sce
        JOIN circles c ON sce.circle_id = c.circle_id
        WHERE c.center_id = ? AND parent_user_id IS NOT NULL
    ");
    $stmt->execute([$center_id]);
    $parent_count = $stmt->fetch()['parent_count'];
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الإحصائيات: ' . $e->getMessage();
}

// Get recent activities
try {
    // Recent enrollments
    $stmt = $pdo->prepare("
        SELECT sce.enrollment_id, sce.enrollment_date, sce.status,
               s.full_name AS student_name, c.circle_name
        FROM student_circle_enrollments sce
        JOIN users s ON sce.student_user_id = s.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        WHERE c.center_id = ?
        ORDER BY sce.enrollment_date DESC
        LIMIT 5
    ");
    $stmt->execute([$center_id]);
    $recent_enrollments = $stmt->fetchAll();

    // Recent memorization progress
    $stmt = $pdo->prepare("
        SELECT mp.progress_id, mp.surah_name, mp.recitation_date,
               s.full_name AS student_name, t.full_name AS teacher_name,
               c.circle_name
        FROM memorization_progress mp
        JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
        JOIN users s ON sce.student_user_id = s.user_id
        JOIN users t ON mp.recorded_by_user_id = t.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        WHERE c.center_id = ?
        ORDER BY mp.recitation_date DESC
        LIMIT 5
    ");
    $stmt->execute([$center_id]);
    $recent_progress = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الأنشطة الحديثة: ' . $e->getMessage();
}

// Get attendance statistics for today
$today = date('Y-m-d');
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name,
               COUNT(sce.enrollment_id) AS total_students,
               SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) AS present_count,
               SUM(CASE WHEN ar.status = 'absent_excused' THEN 1 ELSE 0 END) AS excused_count,
               SUM(CASE WHEN ar.status = 'absent_unexcused' THEN 1 ELSE 0 END) AS unexcused_count,
               SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) AS late_count
        FROM circles c
        LEFT JOIN student_circle_enrollments sce ON c.circle_id = sce.circle_id AND sce.status = 'approved'
        LEFT JOIN attendance_records ar ON sce.enrollment_id = ar.enrollment_id AND ar.session_date = ?
        WHERE c.center_id = ? AND c.is_active = TRUE
        GROUP BY c.circle_id
        ORDER BY c.circle_name
    ");
    $stmt->execute([$today, $center_id]);
    $attendance_stats = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع إحصائيات الحضور: ' . $e->getMessage();
}

// Get unread messages
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) AS unread_count
        FROM messages
        WHERE recipient_user_id = ? AND read_at IS NULL
    ");
    $stmt->execute([$admin_id]);
    $unread_messages_count = $stmt->fetch()['unread_count'];
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الرسائل: ' . $e->getMessage();
}

// Page variables
$page_title = 'لوحة تحكم مدير المركز';
$active_page = 'dashboard';

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">مرحباً، <?php echo $_SESSION['full_name']; ?></h1>
    <a href="<?php echo get_root_url(); ?>pages/center_settings.php" class="btn btn-outline-primary">
        <i class="fas fa-cog me-1"></i> إعدادات المركز
    </a>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (isset($center)): ?>
    <div class="alert alert-info">
        <div class="row align-items-center">
            <div class="col-auto">
                <?php if (!empty($center['logo_url'])): ?>
                    <img src="<?php echo get_root_url() . $center['logo_url']; ?>" alt="شعار المركز" height="60">
                <?php else: ?>
                    <i class="fas fa-building fa-3x"></i>
                <?php endif; ?>
            </div>
            <div class="col">
                <h4 class="alert-heading mb-1"><?php echo $center['center_name']; ?></h4>
                <p class="mb-0"><?php echo $center['address']; ?></p>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Statistics Row -->
<div class="row mb-4">
    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-user-tie fa-3x mb-3"></i>
                <h2 class="card-title"><?php echo isset($teacher_count) ? $teacher_count : '0'; ?></h2>
                <p class="card-text">المعلمين</p>
            </div>
            <div class="card-footer bg-primary border-0 text-center">
                <a href="<?php echo get_root_url(); ?>pages/teachers.php" class="text-white">عرض التفاصيل <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-user-graduate fa-3x mb-3"></i>
                <h2 class="card-title"><?php echo isset($student_count) ? $student_count : '0'; ?></h2>
                <p class="card-text">الطلاب</p>
            </div>
            <div class="card-footer bg-success border-0 text-center">
                <a href="<?php echo get_root_url(); ?>pages/students.php" class="text-white">عرض التفاصيل <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card bg-warning text-dark h-100">
            <div class="card-body text-center">
                <i class="fas fa-circle fa-3x mb-3"></i>
                <h2 class="card-title"><?php echo isset($circle_count) ? $circle_count : '0'; ?></h2>
                <p class="card-text">الحلقات</p>
            </div>
            <div class="card-footer bg-warning border-0 text-center">
                <a href="<?php echo get_root_url(); ?>pages/circles.php" class="text-dark">عرض التفاصيل <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-user-friends fa-3x mb-3"></i>
                <h2 class="card-title"><?php echo isset($parent_count) ? $parent_count : '0'; ?></h2>
                <p class="card-text">أولياء الأمور</p>
            </div>
            <div class="card-footer bg-info border-0 text-center">
                <a href="<?php echo get_root_url(); ?>pages/parents.php" class="text-white">عرض التفاصيل <i class="fas fa-arrow-circle-right"></i></a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo get_root_url(); ?>pages/add_teacher.php" class="btn btn-outline-primary d-block py-3">
                            <i class="fas fa-user-plus fa-2x mb-2"></i><br>
                            إضافة معلم
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo get_root_url(); ?>pages/add_student.php" class="btn btn-outline-success d-block py-3">
                            <i class="fas fa-user-graduate fa-2x mb-2"></i><br>
                            إضافة طالب
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo get_root_url(); ?>pages/add_circle.php" class="btn btn-outline-warning d-block py-3">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i><br>
                            إنشاء حلقة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo get_root_url(); ?>pages/messages.php" class="btn btn-outline-info d-block py-3">
                            <i class="fas fa-envelope fa-2x mb-2"></i><br>
                            الرسائل
                            <?php if (isset($unread_messages_count) && $unread_messages_count > 0): ?>
                                <span class="badge bg-danger"><?php echo $unread_messages_count; ?></span>
                            <?php endif; ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include announcements modal
include_once '../includes/announcement_modal.php';

// Include footer template
include_template('footer');
?>
