<?php
require_once '../includes/common.php';
require_once '../includes/activity_logger.php';

// Check if user is logged in and log logout activity
if (is_logged_in() && isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
    log_logout_activity($pdo, $_SESSION['user_id'], $_SESSION['username']);
}

// Unset all session variables
$_SESSION = array();

// If it's desired to kill the session, also delete the session cookie.
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Finally, destroy the session.
session_destroy();

// Redirect to login page
header("Location: ../index.php");
exit;
?>
