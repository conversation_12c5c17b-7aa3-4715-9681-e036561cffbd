<?php
/**
 * Helper functions for announcements
 */

/**
 * Check if announcements table exists
 *
 * @param PDO $pdo Database connection
 * @return bool True if table exists, false otherwise
 */
function announcements_table_exists($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
        ");
        $stmt->execute();
        return (bool)$stmt->fetchColumn();
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Create announcements table if it doesn't exist
 *
 * @param PDO $pdo Database connection
 * @return bool True if successful, false otherwise
 */
function create_announcements_table($pdo) {
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS announcements (
                announcement_id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                sender_user_id INT NOT NULL,
                target_role VARCHAR(50) DEFAULT 'all',
                target_center_id INT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (sender_user_id) REFERENCES users(user_id),
                FOREIGN KEY (target_center_id) REFERENCES centers(center_id)
            )
        ");

        $pdo->exec("
            CREATE TABLE IF NOT EXISTS announcement_reads (
                read_id INT AUTO_INCREMENT PRIMARY KEY,
                announcement_id INT NOT NULL,
                user_id INT NOT NULL,
                read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                UNIQUE KEY (announcement_id, user_id)
            )
        ");

        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Get announcements for a user
 *
 * @param PDO $pdo Database connection
 * @param int $user_id User ID
 * @param string $role_name User role name
 * @param int $center_id User center ID
 * @param int $limit Maximum number of announcements to return
 * @return array|false Array of announcements or false on error
 */
function get_announcements_for_user($pdo, $user_id, $role_name, $center_id, $limit = 5) {
    // Check if table exists
    if (!announcements_table_exists($pdo)) {
        // Try to create the table
        if (!create_announcements_table($pdo)) {
            return false;
        }
    }

    try {
        // Regular users see announcements for all, their role, or their specific center
        $stmt = $pdo->prepare("
            SELECT a.*, u.full_name AS creator_name,
                   (SELECT COUNT(*) FROM announcement_reads ar WHERE ar.announcement_id = a.announcement_id AND ar.user_id = ?) AS is_read
            FROM announcements a
            JOIN users u ON a.sender_user_id = u.user_id
            WHERE a.is_active = TRUE
            AND CURRENT_DATE BETWEEN a.start_date AND a.end_date
            AND (a.target_role IS NULL OR a.target_role = 'all' OR a.target_role = ? OR
                (a.target_role = 'center_specific' AND a.target_center_id = ?))
            ORDER BY a.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$user_id, $role_name, $center_id, $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Mark announcement as read
 *
 * @param PDO $pdo Database connection
 * @param int $announcement_id Announcement ID
 * @param int $user_id User ID
 * @return bool True if successful, false otherwise
 */
function mark_announcement_as_read($pdo, $announcement_id, $user_id) {
    try {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO announcement_reads (announcement_id, user_id)
            VALUES (?, ?)
        ");
        return $stmt->execute([$announcement_id, $user_id]);
    } catch (PDOException $e) {
        return false;
    }
}
?>
