<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

echo "<h1>اختبار نظام نشاط المستخدمين</h1>";

try {
    // Test 1: Check existing tables
    echo "<h2>1. فحص الجداول الموجودة:</h2>";
    $existing_tables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }

    $required_tables = ['users', 'roles', 'centers', 'circles', 'student_circle_enrollments'];
    $optional_tables = ['announcements', 'whatsapp_logs', 'attendance_records', 'memorization_progress', 'system_logs'];

    echo "<h3>الجداول المطلوبة:</h3><ul>";
    foreach ($required_tables as $table) {
        $exists = in_array($table, $existing_tables);
        echo "<li style='color: " . ($exists ? 'green' : 'red') . "'>{$table}: " . ($exists ? '✓ موجود' : '✗ غير موجود') . "</li>";
    }
    echo "</ul>";

    echo "<h3>الجداول الاختيارية:</h3><ul>";
    foreach ($optional_tables as $table) {
        $exists = in_array($table, $existing_tables);
        echo "<li style='color: " . ($exists ? 'green' : 'orange') . "'>{$table}: " . ($exists ? '✓ موجود' : '- غير موجود') . "</li>";
    }
    echo "</ul>";

    // Test 2: Check data counts
    echo "<h2>2. فحص البيانات الموجودة:</h2>";

    // Users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $user_count = $stmt->fetchColumn();
    echo "<p>عدد المستخدمين: <strong>{$user_count}</strong></p>";

    // Enrollments
    $stmt = $pdo->query("SELECT COUNT(*) FROM student_circle_enrollments");
    $enrollment_count = $stmt->fetchColumn();
    echo "<p>عدد التسجيلات في الحلقات: <strong>{$enrollment_count}</strong></p>";

    // Optional tables data
    foreach ($optional_tables as $table) {
        if (in_array($table, $existing_tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            echo "<p>عدد السجلات في {$table}: <strong>{$count}</strong></p>";
        }
    }

    // Test 3: Test activity queries
    echo "<h2>3. اختبار استعلامات النشاط:</h2>";
    $days_filter = 30; // Last 30 days

    // Test user registrations
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
    $stmt->execute([$days_filter]);
    $recent_users = $stmt->fetchColumn();
    echo "<p>المستخدمين الجدد (آخر 30 يوم): <strong>{$recent_users}</strong></p>";

    // Test enrollments
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM student_circle_enrollments WHERE enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND enrollment_date IS NOT NULL");
    $stmt->execute([$days_filter]);
    $recent_enrollments = $stmt->fetchColumn();
    echo "<p>التسجيلات الجديدة (آخر 30 يوم): <strong>{$recent_enrollments}</strong></p>";

    // Test optional tables
    if (in_array('announcements', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM announcements WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
        $stmt->execute([$days_filter]);
        $recent_announcements = $stmt->fetchColumn();
        echo "<p>الإعلانات الجديدة (آخر 30 يوم): <strong>{$recent_announcements}</strong></p>";
    }

    if (in_array('whatsapp_logs', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM whatsapp_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
        $stmt->execute([$days_filter]);
        $recent_whatsapp = $stmt->fetchColumn();
        echo "<p>رسائل الواتساب (آخر 30 يوم): <strong>{$recent_whatsapp}</strong></p>";
    }

    if (in_array('attendance_records', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM attendance_records WHERE session_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND session_date IS NOT NULL");
        $stmt->execute([$days_filter]);
        $recent_attendance = $stmt->fetchColumn();
        echo "<p>سجلات الحضور (آخر 30 يوم): <strong>{$recent_attendance}</strong></p>";
    }

    if (in_array('memorization_progress', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM memorization_progress WHERE recitation_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND recitation_date IS NOT NULL");
        $stmt->execute([$days_filter]);
        $recent_memorization = $stmt->fetchColumn();
        echo "<p>تقييمات الحفظ (آخر 30 يوم): <strong>{$recent_memorization}</strong></p>";
    }

    if (in_array('system_logs', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
        $stmt->execute([$days_filter]);
        $recent_logs = $stmt->fetchColumn();
        echo "<p>سجلات النظام (آخر 30 يوم): <strong>{$recent_logs}</strong></p>";
    }

    // Test 4: Test sample activity query
    echo "<h2>4. اختبار استعلام النشاط المدمج:</h2>";

    $queries = [];
    $params = [];

    // User registrations
    $user_registration_query = "
        SELECT
            'تسجيل مستخدم جديد' as activity_type,
            CONCAT('تم تسجيل مستخدم جديد: ', u.full_name, ' (', r.role_name, ')') as description,
            u.created_at as timestamp,
            'النظام' as user_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND u.created_at IS NOT NULL
        LIMIT 5
    ";
    $queries[] = $user_registration_query;
    $params[] = $days_filter;

    // Enrollments
    $enrollment_query = "
        SELECT
            'تسجيل في الحلقات' as activity_type,
            CONCAT('تسجيل طالب: ', u.full_name, ' في حلقة ', c.circle_name,
                   CASE WHEN p.full_name IS NOT NULL THEN CONCAT(' (ولي الأمر: ', p.full_name, ')') ELSE '' END) as description,
            sce.enrollment_date as timestamp,
            'النظام' as user_name
        FROM student_circle_enrollments sce
        JOIN users u ON sce.student_user_id = u.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        LEFT JOIN users p ON sce.parent_user_id = p.user_id
        WHERE sce.enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND sce.enrollment_date IS NOT NULL
        LIMIT 5
    ";
    $queries[] = $enrollment_query;
    $params[] = $days_filter;

    if (!empty($queries)) {
        $final_query = "(" . implode(") UNION (", $queries) . ") ORDER BY timestamp DESC LIMIT 10";

        $stmt = $pdo->prepare($final_query);
        $stmt->execute($params);
        $sample_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($sample_activities)) {
            echo "<h3>عينة من الأنشطة الحديثة:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>نوع النشاط</th><th>الوصف</th><th>التاريخ</th><th>المستخدم</th></tr>";
            foreach ($sample_activities as $activity) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($activity['activity_type']) . "</td>";
                echo "<td>" . htmlspecialchars($activity['description']) . "</td>";
                echo "<td>" . htmlspecialchars($activity['timestamp']) . "</td>";
                echo "<td>" . htmlspecialchars($activity['user_name']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>لا توجد أنشطة حديثة في آخر 30 يوم</p>";
        }
    }

    // Test 5: Recommendations
    echo "<h2>5. التوصيات:</h2>";

    $total_recent_activity = $recent_users + $recent_enrollments;
    if (isset($recent_announcements)) $total_recent_activity += $recent_announcements;
    if (isset($recent_whatsapp)) $total_recent_activity += $recent_whatsapp;
    if (isset($recent_attendance)) $total_recent_activity += $recent_attendance;
    if (isset($recent_memorization)) $total_recent_activity += $recent_memorization;
    if (isset($recent_logs)) $total_recent_activity += $recent_logs;

    echo "<ul>";

    if ($total_recent_activity == 0) {
        echo "<li style='color: red;'>⚠️ لا توجد أنشطة حديثة. يُنصح بتشغيل ملف إضافة البيانات التجريبية.</li>";
        echo "<li><a href='add_real_activity_data.php' style='color: blue;'>👉 إضافة بيانات تجريبية</a></li>";
    } else {
        echo "<li style='color: green;'>✅ يوجد {$total_recent_activity} نشاط حديث. النظام جاهز للاستخدام!</li>";
    }

    if (!in_array('system_logs', $existing_tables)) {
        echo "<li style='color: orange;'>💡 يُنصح بإنشاء جدول system_logs لتتبع أنشطة النظام.</li>";
    }

    if (!in_array('announcements', $existing_tables)) {
        echo "<li style='color: orange;'>💡 يُنصح بإنشاء جدول announcements لتتبع الإعلانات.</li>";
    }

    echo "<li><a href='user_activity.php' style='color: green; font-weight: bold;'>🚀 انتقل إلى صفحة نشاط المستخدمين</a></li>";
    echo "</ul>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ عام: " . $e->getMessage() . "</p>";
}

echo "<br><hr><p><a href='../dashboard.php'>العودة للوحة التحكم</a></p>";
?>
