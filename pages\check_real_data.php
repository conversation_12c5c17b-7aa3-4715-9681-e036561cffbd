<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

echo "<h1>فحص البيانات الحقيقية في قاعدة البيانات</h1>";

try {
    // Check what tables exist
    echo "<h2>الجداول الموجودة:</h2>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>{$table}</li>";
    }
    echo "</ul>";

    // Check users table
    echo "<h2>جدول المستخدمين (users):</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $user_count = $stmt->fetchColumn();
    echo "<p>إجمالي المستخدمين: {$user_count}</p>";
    
    $stmt = $pdo->query("
        SELECT r.role_name, COUNT(*) as count 
        FROM users u 
        JOIN roles r ON u.role_id = r.role_id 
        GROUP BY r.role_name
    ");
    $role_counts = $stmt->fetchAll();
    echo "<ul>";
    foreach ($role_counts as $role) {
        echo "<li>{$role['role_name']}: {$role['count']}</li>";
    }
    echo "</ul>";

    // Check recent users
    echo "<h3>آخر 5 مستخدمين:</h3>";
    $stmt = $pdo->query("
        SELECT u.full_name, r.role_name, u.created_at 
        FROM users u 
        JOIN roles r ON u.role_id = r.role_id 
        ORDER BY u.created_at DESC 
        LIMIT 5
    ");
    $recent_users = $stmt->fetchAll();
    echo "<ul>";
    foreach ($recent_users as $user) {
        echo "<li>{$user['full_name']} ({$user['role_name']}) - {$user['created_at']}</li>";
    }
    echo "</ul>";

    // Check circles
    echo "<h2>جدول الحلقات (circles):</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM circles");
    $circle_count = $stmt->fetchColumn();
    echo "<p>إجمالي الحلقات: {$circle_count}</p>";
    
    if ($circle_count > 0) {
        $stmt = $pdo->query("
            SELECT c.circle_name, center.center_name, teacher.full_name as teacher_name
            FROM circles c
            LEFT JOIN centers center ON c.center_id = center.center_id
            LEFT JOIN users teacher ON c.teacher_user_id = teacher.user_id
            LIMIT 5
        ");
        $circles = $stmt->fetchAll();
        echo "<ul>";
        foreach ($circles as $circle) {
            echo "<li>{$circle['circle_name']} - {$circle['center_name']} - معلم: {$circle['teacher_name']}</li>";
        }
        echo "</ul>";
    }

    // Check enrollments
    echo "<h2>جدول التسجيل في الحلقات (student_circle_enrollments):</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM student_circle_enrollments");
    $enrollment_count = $stmt->fetchColumn();
    echo "<p>إجمالي التسجيلات: {$enrollment_count}</p>";
    
    if ($enrollment_count > 0) {
        $stmt = $pdo->query("
            SELECT student.full_name as student_name, c.circle_name, sce.enrollment_date
            FROM student_circle_enrollments sce
            JOIN users student ON sce.student_user_id = student.user_id
            JOIN circles c ON sce.circle_id = c.circle_id
            ORDER BY sce.enrollment_date DESC
            LIMIT 5
        ");
        $enrollments = $stmt->fetchAll();
        echo "<ul>";
        foreach ($enrollments as $enrollment) {
            echo "<li>{$enrollment['student_name']} في {$enrollment['circle_name']} - {$enrollment['enrollment_date']}</li>";
        }
        echo "</ul>";
    }

    // Check attendance records
    echo "<h2>جدول سجلات الحضور (attendance_records):</h2>";
    if (in_array('attendance_records', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM attendance_records");
        $attendance_count = $stmt->fetchColumn();
        echo "<p>إجمالي سجلات الحضور: {$attendance_count}</p>";
        
        if ($attendance_count > 0) {
            $stmt = $pdo->query("
                SELECT ar.attendance_date, ar.status, student.full_name as student_name, teacher.full_name as teacher_name
                FROM attendance_records ar
                JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
                JOIN users student ON sce.student_user_id = student.user_id
                LEFT JOIN users teacher ON ar.recorded_by_user_id = teacher.user_id
                ORDER BY ar.attendance_date DESC
                LIMIT 5
            ");
            $attendance_records = $stmt->fetchAll();
            echo "<ul>";
            foreach ($attendance_records as $record) {
                echo "<li>{$record['student_name']} - {$record['status']} - {$record['attendance_date']} (سجل بواسطة: {$record['teacher_name']})</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>جدول attendance_records غير موجود</p>";
    }

    // Check memorization progress
    echo "<h2>جدول تقدم الحفظ (memorization_progress):</h2>";
    if (in_array('memorization_progress', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM memorization_progress");
        $memorization_count = $stmt->fetchColumn();
        echo "<p>إجمالي سجلات الحفظ: {$memorization_count}</p>";
        
        if ($memorization_count > 0) {
            $stmt = $pdo->query("
                SELECT mp.surah_name, mp.ayah_from, mp.ayah_to, mp.memorization_quality, 
                       student.full_name as student_name, teacher.full_name as teacher_name, mp.recitation_date
                FROM memorization_progress mp
                JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
                JOIN users student ON sce.student_user_id = student.user_id
                LEFT JOIN users teacher ON mp.recorded_by_user_id = teacher.user_id
                ORDER BY mp.recitation_date DESC
                LIMIT 5
            ");
            $memorization_records = $stmt->fetchAll();
            echo "<ul>";
            foreach ($memorization_records as $record) {
                echo "<li>{$record['student_name']} - {$record['surah_name']} ({$record['ayah_from']}-{$record['ayah_to']}) - {$record['memorization_quality']} - {$record['recitation_date']} (سجل بواسطة: {$record['teacher_name']})</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>جدول memorization_progress غير موجود</p>";
    }

    // Check system logs
    echo "<h2>جدول سجلات النظام (system_logs):</h2>";
    if (in_array('system_logs', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM system_logs");
        $logs_count = $stmt->fetchColumn();
        echo "<p>إجمالي سجلات النظام: {$logs_count}</p>";
        
        if ($logs_count > 0) {
            $stmt = $pdo->query("
                SELECT sl.log_category, sl.message, sl.log_level, u.full_name as user_name, sl.created_at
                FROM system_logs sl
                LEFT JOIN users u ON sl.user_id = u.user_id
                ORDER BY sl.created_at DESC
                LIMIT 5
            ");
            $system_logs = $stmt->fetchAll();
            echo "<ul>";
            foreach ($system_logs as $log) {
                echo "<li>[{$log['log_level']}] {$log['log_category']}: {$log['message']} - {$log['user_name']} - {$log['created_at']}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>جدول system_logs غير موجود</p>";
    }

    // Check announcements
    echo "<h2>جدول الإعلانات (announcements):</h2>";
    if (in_array('announcements', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM announcements");
        $announcements_count = $stmt->fetchColumn();
        echo "<p>إجمالي الإعلانات: {$announcements_count}</p>";
        
        if ($announcements_count > 0) {
            $stmt = $pdo->query("
                SELECT a.title, a.target_role, u.full_name as created_by, a.created_at
                FROM announcements a
                LEFT JOIN users u ON a.created_by_user_id = u.user_id
                ORDER BY a.created_at DESC
                LIMIT 5
            ");
            $announcements = $stmt->fetchAll();
            echo "<ul>";
            foreach ($announcements as $announcement) {
                echo "<li>{$announcement['title']} - {$announcement['target_role']} - {$announcement['created_by']} - {$announcement['created_at']}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>جدول announcements غير موجود</p>";
    }

    // Check WhatsApp logs
    echo "<h2>جدول سجلات الواتساب (whatsapp_logs):</h2>";
    if (in_array('whatsapp_logs', $tables)) {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM whatsapp_logs");
        $whatsapp_count = $stmt->fetchColumn();
        echo "<p>إجمالي سجلات الواتساب: {$whatsapp_count}</p>";
        
        if ($whatsapp_count > 0) {
            $stmt = $pdo->query("
                SELECT recipient_number, message_type, status, created_at
                FROM whatsapp_logs
                ORDER BY created_at DESC
                LIMIT 5
            ");
            $whatsapp_logs = $stmt->fetchAll();
            echo "<ul>";
            foreach ($whatsapp_logs as $log) {
                echo "<li>{$log['recipient_number']} - {$log['message_type']} - {$log['status']} - {$log['created_at']}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>جدول whatsapp_logs غير موجود</p>";
    }

} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

echo "<br><a href='user_activity.php'>العودة لصفحة نشاط المستخدمين</a>";
?>
