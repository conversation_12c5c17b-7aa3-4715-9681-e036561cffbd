<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Set page title
$page_title = 'إنشاء جدول واجبات الطلاب';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success_messages = [];
$error_messages = [];

// Create student_assignments table
try {
    // Start transaction
    $pdo->beginTransaction();

    // Check if table exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'student_assignments'
    ");
    $stmt->execute();
    $table_exists = (bool)$stmt->fetchColumn();

    if ($table_exists) {
        $success_messages[] = 'جدول واجبات الطلاب (student_assignments) موجود بالفعل.';
    } else {
        // Create student_assignments table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `student_assignments` (
              `student_assignment_id` INT AUTO_INCREMENT PRIMARY KEY,
              `assignment_id` INT NOT NULL,
              `student_user_id` INT NOT NULL,
              `status` ENUM('pending', 'submitted', 'graded', 'late', 'missed') DEFAULT 'pending',
              `submission_text` TEXT,
              `submission_date` DATETIME DEFAULT NULL,
              `grade` DECIMAL(5,2) DEFAULT NULL,
              `feedback` TEXT,
              `graded_by_user_id` INT DEFAULT NULL,
              `graded_at` DATETIME DEFAULT NULL,
              `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ");

        // Create indexes for better performance
        $pdo->exec("CREATE INDEX idx_student_assignments_assignment_id ON student_assignments(assignment_id);");
        $pdo->exec("CREATE INDEX idx_student_assignments_student_user_id ON student_assignments(student_user_id);");
        $pdo->exec("CREATE INDEX idx_student_assignments_status ON student_assignments(status);");

        // Add foreign keys if the referenced tables exist
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'assignments'
        ");
        $stmt->execute();
        $assignments_table_exists = (bool)$stmt->fetchColumn();

        if ($assignments_table_exists) {
            $pdo->exec("
                ALTER TABLE `student_assignments`
                ADD CONSTRAINT `fk_student_assignments_assignment_id`
                FOREIGN KEY (`assignment_id`) REFERENCES `assignments`(`assignment_id`) ON DELETE CASCADE
            ");
        }

        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'users'
        ");
        $stmt->execute();
        $users_table_exists = (bool)$stmt->fetchColumn();

        if ($users_table_exists) {
            $pdo->exec("
                ALTER TABLE `student_assignments`
                ADD CONSTRAINT `fk_student_assignments_student_user_id`
                FOREIGN KEY (`student_user_id`) REFERENCES `users`(`user_id`) ON DELETE CASCADE
            ");

            $pdo->exec("
                ALTER TABLE `student_assignments`
                ADD CONSTRAINT `fk_student_assignments_graded_by_user_id`
                FOREIGN KEY (`graded_by_user_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL
            ");
        }

        $success_messages[] = 'تم إنشاء جدول واجبات الطلاب (student_assignments) بنجاح.';
    }

    // Commit transaction
    $pdo->commit();

    // Check if student_circle_enrollments table exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'student_circle_enrollments'
    ");
    $stmt->execute();
    $enrollments_table_exists = (bool)$stmt->fetchColumn();

    if (!$enrollments_table_exists) {
        // Create student_circle_enrollments table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `student_circle_enrollments` (
              `enrollment_id` INT AUTO_INCREMENT PRIMARY KEY,
              `student_user_id` INT NOT NULL,
              `circle_id` INT NOT NULL,
              `enrollment_date` DATE NOT NULL DEFAULT CURRENT_DATE(),
              `status` ENUM('pending', 'approved', 'rejected', 'withdrawn') DEFAULT 'pending',
              `notes` TEXT,
              `approved_by_user_id` INT DEFAULT NULL,
              `approved_at` DATETIME DEFAULT NULL,
              `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
              `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

              FOREIGN KEY (`student_user_id`) REFERENCES `users`(`user_id`) ON DELETE CASCADE,
              FOREIGN KEY (`circle_id`) REFERENCES `circles`(`circle_id`) ON DELETE CASCADE,
              FOREIGN KEY (`approved_by_user_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,

              UNIQUE KEY `unique_student_circle` (`student_user_id`, `circle_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ");

        $success_messages[] = 'تم إنشاء جدول تسجيلات الطلاب في الحلقات (student_circle_enrollments) بنجاح.';
    }

} catch (PDOException $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    $error_messages[] = 'حدث خطأ أثناء إنشاء الجدول: ' . $e->getMessage();
}

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">إنشاء جدول واجبات الطلاب</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_messages)): ?>
                        <?php foreach ($success_messages as $message): ?>
                            <div class="alert alert-success"><?php echo $message; ?></div>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <?php if (!empty($error_messages)): ?>
                        <?php foreach ($error_messages as $message): ?>
                            <div class="alert alert-danger"><?php echo $message; ?></div>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <div class="mt-4">
                        <a href="<?php echo get_root_url(); ?>pages/assignments.php" class="btn btn-primary">
                            الذهاب إلى صفحة الواجبات
                        </a>
                        <a href="<?php echo get_root_url(); ?>pages/system_owner_dashboard.php" class="btn btn-secondary ms-2">
                            العودة إلى لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer_inner.php';
?>
