<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has parent role
if (!is_logged_in() || !has_role('parent')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('../auth/login.php');
}

// Get parent information
$parent_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Process adding a new child
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_child') {
    $student_id = isset($_POST['student_id']) ? (int)$_POST['student_id'] : 0;
    $relation_type = isset($_POST['relation_type']) ? sanitize_input($_POST['relation_type']) : 'أب';
    $is_primary = isset($_POST['is_primary']) ? 1 : 0;
    
    if (empty($student_id)) {
        $error = 'يرجى اختيار الطالب';
    } else {
        try {
            // Check if the student exists and is not already linked to this parent
            $stmt = $pdo->prepare("
                SELECT u.user_id, u.full_name 
                FROM users u
                LEFT JOIN Parent_Student_Relations psr ON u.user_id = psr.student_user_id AND psr.parent_user_id = ?
                WHERE u.user_id = ? AND u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
                AND psr.relation_id IS NULL
            ");
            $stmt->execute([$parent_id, $student_id]);
            $student = $stmt->fetch();
            
            if ($student) {
                // If this is set as primary, update all other relations to non-primary
                if ($is_primary) {
                    $stmt = $pdo->prepare("
                        UPDATE Parent_Student_Relations 
                        SET is_primary = FALSE 
                        WHERE parent_user_id = ?
                    ");
                    $stmt->execute([$parent_id]);
                }
                
                // Add the new relation
                $stmt = $pdo->prepare("
                    INSERT INTO Parent_Student_Relations (parent_user_id, student_user_id, relation_type, is_primary)
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$parent_id, $student_id, $relation_type, $is_primary]);
                
                $success = 'تم إضافة الطالب ' . $student['full_name'] . ' بنجاح';
            } else {
                $error = 'الطالب غير موجود أو مرتبط بالفعل بك';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة الطالب: ' . $e->getMessage();
        }
    }
}

// Process removing a child
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'remove_child') {
    $relation_id = isset($_POST['relation_id']) ? (int)$_POST['relation_id'] : 0;
    
    if (empty($relation_id)) {
        $error = 'معرف العلاقة غير صالح';
    } else {
        try {
            // Check if the relation exists and belongs to this parent
            $stmt = $pdo->prepare("
                SELECT psr.relation_id, u.full_name 
                FROM Parent_Student_Relations psr
                JOIN users u ON psr.student_user_id = u.user_id
                WHERE psr.relation_id = ? AND psr.parent_user_id = ?
            ");
            $stmt->execute([$relation_id, $parent_id]);
            $relation = $stmt->fetch();
            
            if ($relation) {
                // Remove the relation
                $stmt = $pdo->prepare("
                    DELETE FROM Parent_Student_Relations 
                    WHERE relation_id = ?
                ");
                $stmt->execute([$relation_id]);
                
                $success = 'تم إزالة الطالب ' . $relation['full_name'] . ' بنجاح';
            } else {
                $error = 'العلاقة غير موجودة أو لا تنتمي إليك';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إزالة الطالب: ' . $e->getMessage();
        }
    }
}

// Get children information using the new Parent_Student_Relations table
try {
    $stmt = $pdo->prepare("
        SELECT psr.relation_id, psr.relation_type, psr.is_primary,
               u.user_id, u.full_name, u.profile_picture_url, 
               c.circle_id, c.circle_name, c.level, 
               t.full_name AS teacher_name,
               ce.center_name
        FROM Parent_Student_Relations psr
        JOIN users u ON psr.student_user_id = u.user_id
        LEFT JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id AND sce.status = 'approved'
        LEFT JOIN circles c ON sce.circle_id = c.circle_id
        LEFT JOIN users t ON c.teacher_user_id = t.user_id
        LEFT JOIN centers ce ON c.center_id = ce.center_id
        WHERE psr.parent_user_id = ?
        ORDER BY psr.is_primary DESC, u.full_name
    ");
    $stmt->execute([$parent_id]);
    $children = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الأبناء: ' . $e->getMessage();
}

// Get available students to add (students not already linked to this parent)
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name
        FROM users u
        WHERE u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
        AND u.user_id NOT IN (
            SELECT student_user_id FROM Parent_Student_Relations WHERE parent_user_id = ?
        )
        ORDER BY u.full_name
    ");
    $stmt->execute([$parent_id]);
    $available_students = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب المتاحين: ' . $e->getMessage();
}
?>
