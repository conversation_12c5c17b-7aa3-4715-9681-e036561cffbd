<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Page variables
$page_title = 'إضافة طالب جديد';
$active_page = 'students';
$success = '';
$error = '';
$center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_GET['center_id']) ? (int)$_GET['center_id'] : null);

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Get circles for dropdown
$circles = [];
try {
    $query = "
        SELECT c.circle_id, c.circle_name, c.level, u.full_name AS teacher_name
        FROM circles c
        LEFT JOIN users u ON c.teacher_user_id = u.user_id
        WHERE c.is_active = TRUE
    ";

    if ($center_id) {
        $query .= " AND c.center_id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$center_id]);
    } else {
        $stmt = $pdo->prepare($query);
        $stmt->execute();
    }

    $circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get parents for dropdown
$parents = [];
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name, u.email, u.phone_number
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE r.role_name = 'parent' AND u.is_active = TRUE
        ORDER BY u.full_name
    ");
    $stmt->execute();
    $parents = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات أولياء الأمور: ' . $e->getMessage();
}

// Check if birth_date, gender and address columns exist
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'birth_date'
    ");
    $stmt->execute();
    $birth_date_exists = (bool)$stmt->fetchColumn();

    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'gender'
    ");
    $stmt->execute();
    $gender_exists = (bool)$stmt->fetchColumn();

    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'address'
    ");
    $stmt->execute();
    $address_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من أعمدة الجدول: ' . $e->getMessage();
    $birth_date_exists = false;
    $gender_exists = false;
    $address_exists = false;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $username = sanitize_input($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $birth_date = $birth_date_exists ? sanitize_input($_POST['birth_date']) : '';
    $gender = $gender_exists ? sanitize_input($_POST['gender']) : '';
    $address = $address_exists ? sanitize_input($_POST['address']) : '';
    $selected_center_id = has_role('center_admin') ? $_SESSION['center_id'] : (int)$_POST['center_id'];
    $circle_id = isset($_POST['circle_id']) ? (int)$_POST['circle_id'] : null;
    $parent_id = isset($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;

    // Validate required fields
    if (empty($username)) {
        $error = 'يرجى إدخال اسم المستخدم';
    } elseif (empty($password)) {
        $error = 'يرجى إدخال كلمة المرور';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيدها غير متطابقين';
    } elseif (empty($full_name)) {
        $error = 'يرجى إدخال الاسم الكامل';
    } elseif (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif ($birth_date_exists && empty($birth_date)) {
        $error = 'يرجى إدخال تاريخ الميلاد';
    } elseif ($gender_exists && empty($gender)) {
        $error = 'يرجى اختيار الجنس';
    } elseif (has_role('system_owner') && empty($selected_center_id)) {
        $error = 'يرجى اختيار المركز';
    } else {
        try {
            // Check if username already exists
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->rowCount() > 0) {
                $error = 'اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر';
            } else {
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->rowCount() > 0) {
                    $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد آخر';
                } else {
                    // Get student role ID
                    $stmt = $pdo->prepare("SELECT role_id FROM roles WHERE role_name = 'student'");
                    $stmt->execute();
                    $role = $stmt->fetch();
                    $role_id = $role['role_id'];

                    // Hash password
                    $password_hash = password_hash($password, PASSWORD_DEFAULT);

                    // Begin transaction
                    $pdo->beginTransaction();

                    // Check if birth_date, gender and address columns exist
                    $stmt = $pdo->prepare("
                        SELECT COUNT(*)
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'users'
                        AND COLUMN_NAME = 'birth_date'
                    ");
                    $stmt->execute();
                    $birth_date_exists = (bool)$stmt->fetchColumn();

                    $stmt = $pdo->prepare("
                        SELECT COUNT(*)
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'users'
                        AND COLUMN_NAME = 'gender'
                    ");
                    $stmt->execute();
                    $gender_exists = (bool)$stmt->fetchColumn();

                    $stmt = $pdo->prepare("
                        SELECT COUNT(*)
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'users'
                        AND COLUMN_NAME = 'address'
                    ");
                    $stmt->execute();
                    $address_exists = (bool)$stmt->fetchColumn();

                    // Insert new student user based on existing columns
                    if ($birth_date_exists && $gender_exists && $address_exists) {
                        // All columns exist
                        $stmt = $pdo->prepare("
                            INSERT INTO users (
                                username, password_hash, full_name, email, phone_number,
                                birth_date, gender, address, role_id, center_id, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                        ");
                        $stmt->execute([
                            $username, $password_hash, $full_name, $email, $phone_number,
                            $birth_date, $gender, $address, $role_id, $selected_center_id
                        ]);
                    } elseif ($birth_date_exists && $gender_exists && !$address_exists) {
                        // birth_date and gender exist, but not address
                        $stmt = $pdo->prepare("
                            INSERT INTO users (
                                username, password_hash, full_name, email, phone_number,
                                birth_date, gender, role_id, center_id, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                        ");
                        $stmt->execute([
                            $username, $password_hash, $full_name, $email, $phone_number,
                            $birth_date, $gender, $role_id, $selected_center_id
                        ]);
                    } elseif ($address_exists) {
                        // address exists, but not birth_date or gender
                        $stmt = $pdo->prepare("
                            INSERT INTO users (
                                username, password_hash, full_name, email, phone_number,
                                address, role_id, center_id, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                        ");
                        $stmt->execute([
                            $username, $password_hash, $full_name, $email, $phone_number,
                            $address, $role_id, $selected_center_id
                        ]);
                    } else {
                        // None of the optional columns exist
                        $stmt = $pdo->prepare("
                            INSERT INTO users (
                                username, password_hash, full_name, email, phone_number,
                                role_id, center_id, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                        ");
                        $stmt->execute([
                            $username, $password_hash, $full_name, $email, $phone_number,
                            $role_id, $selected_center_id
                        ]);
                    }

                    $student_id = $pdo->lastInsertId();

                    // Enroll student in circle if selected
                    if ($circle_id) {
                        if ($parent_id) {
                            // If parent is selected, include parent_user_id
                            $stmt = $pdo->prepare("
                                INSERT INTO student_circle_enrollments (
                                    student_user_id, circle_id, parent_user_id, enrollment_date, status
                                ) VALUES (?, ?, ?, CURDATE(), 'approved')
                            ");
                            $stmt->execute([$student_id, $circle_id, $parent_id]);
                        } else {
                            // If no parent is selected, don't include parent_user_id
                            $stmt = $pdo->prepare("
                                INSERT INTO student_circle_enrollments (
                                    student_user_id, circle_id, enrollment_date, status
                                ) VALUES (?, ?, CURDATE(), 'approved')
                            ");
                            $stmt->execute([$student_id, $circle_id]);
                        }
                    }

                    // Add parent-student relation if parent selected
                    if ($parent_id) {
                        $stmt = $pdo->prepare("
                            INSERT INTO parent_student_relations (
                                parent_user_id, student_user_id, relation_type, is_primary
                            ) VALUES (?, ?, 'أب', 1)
                        ");
                        $stmt->execute([$parent_id, $student_id]);
                    }

                    // Handle profile picture upload if provided
                    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                        $upload_dir = '../assets/images/profiles/';

                        // Create directory if it doesn't exist
                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0777, true);
                        }

                        $file_extension = pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION);
                        $new_filename = 'user_' . $student_id . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;

                        if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                            // Update user with profile picture URL
                            $profile_picture_url = 'assets/images/profiles/' . $new_filename;
                            $stmt = $pdo->prepare("UPDATE users SET profile_picture_url = ? WHERE user_id = ?");
                            $stmt->execute([$profile_picture_url, $student_id]);
                        }
                    }

                    // Commit transaction
                    $pdo->commit();

                    $success = 'تم إضافة الطالب بنجاح';

                    // Redirect to students list
                    set_flash_message('success', $success);
                    redirect('pages/students.php');
                }
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء إضافة الطالب: ' . $e->getMessage();
        }
    }
}

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'الطلاب', 'url' => 'students.php'],
    ['title' => 'إضافة طالب جديد']
];

// Set up action button
$action_button = [
    'url' => 'students.php',
    'text' => 'العودة إلى قائمة الطلاب',
    'icon' => 'fas fa-arrow-right'
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-user-graduate'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-user-plus me-2"></i> معلومات الطالب الجديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" required value="<?php echo isset($_POST['username']) ? $_POST['username'] : ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required value="<?php echo isset($_POST['full_name']) ? $_POST['full_name'] : ''; ?>">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" required value="<?php echo isset($_POST['email']) ? $_POST['email'] : ''; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="phone_number" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone_number" name="phone_number" value="<?php echo isset($_POST['phone_number']) ? $_POST['phone_number'] : ''; ?>">
                </div>
            </div>

            <div class="row">
                <?php if ($birth_date_exists): ?>
                <div class="col-md-6 mb-3">
                    <label for="birth_date" class="form-label">تاريخ الميلاد <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date" required value="<?php echo isset($_POST['birth_date']) ? $_POST['birth_date'] : ''; ?>">
                </div>
                <?php endif; ?>

                <?php if ($gender_exists): ?>
                <div class="col-md-<?php echo $birth_date_exists ? '6' : '12'; ?> mb-3">
                    <label for="gender" class="form-label">الجنس <span class="text-danger">*</span></label>
                    <select class="form-select" id="gender" name="gender" required>
                        <option value="">-- اختر الجنس --</option>
                        <option value="ذكر" <?php echo (isset($_POST['gender']) && $_POST['gender'] == 'ذكر') ? 'selected' : ''; ?>>ذكر</option>
                        <option value="أنثى" <?php echo (isset($_POST['gender']) && $_POST['gender'] == 'أنثى') ? 'selected' : ''; ?>>أنثى</option>
                    </select>
                </div>
                <?php endif; ?>

                <?php if (!$birth_date_exists && !$gender_exists): ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> لتسجيل تاريخ الميلاد والجنس، يرجى تحديث قاعدة البيانات باستخدام ملف SQL/update_users_table.sql
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <?php if ($address_exists): ?>
            <div class="mb-3">
                <label for="address" class="form-label">العنوان</label>
                <textarea class="form-control" id="address" name="address" rows="2"><?php echo isset($_POST['address']) ? $_POST['address'] : ''; ?></textarea>
            </div>
            <?php endif; ?>

            <div class="row">
                <?php if (has_role('system_owner')): ?>
                <div class="col-md-6 mb-3">
                    <label for="center_id" class="form-label">المركز <span class="text-danger">*</span></label>
                    <select class="form-select" id="center_id" name="center_id" required>
                        <option value="">-- اختر المركز --</option>
                        <?php foreach ($centers as $center): ?>
                            <option value="<?php echo $center['center_id']; ?>" <?php echo (isset($_POST['center_id']) && $_POST['center_id'] == $center['center_id']) ? 'selected' : ''; ?>>
                                <?php echo $center['center_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>

                <div class="col-md-6 mb-3">
                    <label for="circle_id" class="form-label">الحلقة</label>
                    <select class="form-select" id="circle_id" name="circle_id">
                        <option value="">-- اختر الحلقة --</option>
                        <?php foreach ($circles as $circle): ?>
                            <option value="<?php echo $circle['circle_id']; ?>" <?php echo (isset($_POST['circle_id']) && $_POST['circle_id'] == $circle['circle_id']) ? 'selected' : ''; ?>>
                                <?php echo $circle['circle_name']; ?> (<?php echo $circle['level']; ?>) - <?php echo $circle['teacher_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="parent_id" class="form-label">ولي الأمر</label>
                    <select class="form-select" id="parent_id" name="parent_id">
                        <option value="">-- اختر ولي الأمر --</option>
                        <?php foreach ($parents as $parent): ?>
                            <option value="<?php echo $parent['user_id']; ?>" <?php echo (isset($_POST['parent_id']) && $_POST['parent_id'] == $parent['user_id']) ? 'selected' : ''; ?>>
                                <?php echo $parent['full_name']; ?> (<?php echo $parent['email']; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="form-text">إذا لم يكن ولي الأمر موجوداً، يمكنك <a href="add_parent.php" target="_blank">إضافة ولي أمر جديد</a></div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                    <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="students.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">إضافة الطالب</button>
            </div>
        </form>
    </div>
</div>

<?php
// Include footer template
include_template('footer');
?>
