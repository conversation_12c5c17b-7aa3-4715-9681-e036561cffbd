<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has teacher role
if (!is_logged_in() || !has_role('teacher')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$teacher_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Get circle ID from query string if provided
$circle_id = isset($_GET['circle_id']) ? (int)$_GET['circle_id'] : null;
$student_id = isset($_GET['student_id']) ? (int)$_GET['student_id'] : null;
$schedule_id = isset($_GET['schedule_id']) ? (int)$_GET['schedule_id'] : null;

// Get teacher's circles
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name
        FROM circles c
        WHERE c.teacher_user_id = ? AND c.is_active = TRUE
        ORDER BY c.circle_name
    ");
    $stmt->execute([$teacher_id]);
    $circles = $stmt->fetchAll();

    if (empty($circles)) {
        $error = 'ليس لديك حلقات نشطة حالياً';
    } elseif (!$circle_id) {
        // If no circle ID provided, use the first one
        $circle_id = $circles[0]['circle_id'];
    } else {
        // Verify that the teacher has access to this circle
        $has_access = false;
        foreach ($circles as $circle) {
            if ($circle['circle_id'] == $circle_id) {
                $has_access = true;
                break;
            }
        }

        if (!$has_access) {
            set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الحلقة');
            redirect('pages/teacher_dashboard.php');
        }
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get selected circle information
if ($circle_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.circle_name
            FROM circles c
            WHERE c.circle_id = ?
        ");
        $stmt->execute([$circle_id]);
        $selected_circle = $stmt->fetch();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
    }
}

// Get students in the selected circle
if ($circle_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.full_name, sce.enrollment_id
            FROM student_circle_enrollments sce
            JOIN users u ON sce.student_user_id = u.user_id
            WHERE sce.circle_id = ? AND sce.status = 'approved'
            ORDER BY u.full_name
        ");
        $stmt->execute([$circle_id]);
        $students = $stmt->fetchAll();

        // If student_id is not set or not in this circle, use the first student
        if (!$student_id || !in_array($student_id, array_column($students, 'user_id'))) {
            if (!empty($students)) {
                $student_id = $students[0]['user_id'];
            }
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب: ' . $e->getMessage();
    }
}

// Get selected student information
if ($student_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.full_name, u.profile_picture_url, sce.enrollment_id
            FROM users u
            JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id
            WHERE u.user_id = ? AND sce.circle_id = ?
        ");
        $stmt->execute([$student_id, $circle_id]);
        $selected_student = $stmt->fetch();

        if (!$selected_student) {
            $error = 'الطالب غير مسجل في هذه الحلقة';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الطالب: ' . $e->getMessage();
    }
}

// Get Quran surahs for dropdown
$surahs = [
    'الفاتحة', 'البقرة', 'آل عمران', 'النساء', 'المائدة', 'الأنعام', 'الأعراف', 'الأنفال', 'التوبة', 'يونس',
    'هود', 'يوسف', 'الرعد', 'إبراهيم', 'الحجر', 'النحل', 'الإسراء', 'الكهف', 'مريم', 'طه',
    'الأنبياء', 'الحج', 'المؤمنون', 'النور', 'الفرقان', 'الشعراء', 'النمل', 'القصص', 'العنكبوت', 'الروم',
    'لقمان', 'السجدة', 'الأحزاب', 'سبأ', 'فاطر', 'يس', 'الصافات', 'ص', 'الزمر', 'غافر',
    'فصلت', 'الشورى', 'الزخرف', 'الدخان', 'الجاثية', 'الأحقاف', 'محمد', 'الفتح', 'الحجرات', 'ق',
    'الذاريات', 'الطور', 'النجم', 'القمر', 'الرحمن', 'الواقعة', 'الحديد', 'المجادلة', 'الحشر', 'الممتحنة',
    'الصف', 'الجمعة', 'المنافقون', 'التغابن', 'الطلاق', 'التحريم', 'الملك', 'القلم', 'الحاقة', 'المعارج',
    'نوح', 'الجن', 'المزمل', 'المدثر', 'القيامة', 'الإنسان', 'المرسلات', 'النبأ', 'النازعات', 'عبس',
    'التكوير', 'الانفطار', 'المطففين', 'الانشقاق', 'البروج', 'الطارق', 'الأعلى', 'الغاشية', 'الفجر', 'البلد',
    'الشمس', 'الليل', 'الضحى', 'الشرح', 'التين', 'العلق', 'القدر', 'البينة', 'الزلزلة', 'العاديات',
    'القارعة', 'التكاثر', 'العصر', 'الهمزة', 'الفيل', 'قريش', 'الماعون', 'الكوثر', 'الكافرون', 'النصر',
    'المسد', 'الإخلاص', 'الفلق', 'الناس'
];

// Get surah ayat counts (simplified for this example)
$surah_ayat_counts = [
    'الفاتحة' => 7, 'البقرة' => 286, 'آل عمران' => 200, 'النساء' => 176, 'المائدة' => 120, 'الأنعام' => 165,
    'الأعراف' => 206, 'الأنفال' => 75, 'التوبة' => 129, 'يونس' => 109, 'هود' => 123, 'يوسف' => 111,
    'الرعد' => 43, 'إبراهيم' => 52, 'الحجر' => 99, 'النحل' => 128, 'الإسراء' => 111, 'الكهف' => 110,
    'مريم' => 98, 'طه' => 135, 'الأنبياء' => 112, 'الحج' => 78, 'المؤمنون' => 118, 'النور' => 64,
    'الفرقان' => 77, 'الشعراء' => 227, 'النمل' => 93, 'القصص' => 88, 'العنكبوت' => 69, 'الروم' => 60,
    'لقمان' => 34, 'السجدة' => 30, 'الأحزاب' => 73, 'سبأ' => 54, 'فاطر' => 45, 'يس' => 83,
    'الصافات' => 182, 'ص' => 88, 'الزمر' => 75, 'غافر' => 85, 'فصلت' => 54, 'الشورى' => 53,
    'الزخرف' => 89, 'الدخان' => 59, 'الجاثية' => 37, 'الأحقاف' => 35, 'محمد' => 38, 'الفتح' => 29,
    'الحجرات' => 18, 'ق' => 45, 'الذاريات' => 60, 'الطور' => 49, 'النجم' => 62, 'القمر' => 55,
    'الرحمن' => 78, 'الواقعة' => 96, 'الحديد' => 29, 'المجادلة' => 22, 'الحشر' => 24, 'الممتحنة' => 13,
    'الصف' => 14, 'الجمعة' => 11, 'المنافقون' => 11, 'التغابن' => 18, 'الطلاق' => 12, 'التحريم' => 12,
    'الملك' => 30, 'القلم' => 52, 'الحاقة' => 52, 'المعارج' => 44, 'نوح' => 28, 'الجن' => 28,
    'المزمل' => 20, 'المدثر' => 56, 'القيامة' => 40, 'الإنسان' => 31, 'المرسلات' => 50, 'النبأ' => 40,
    'النازعات' => 46, 'عبس' => 42, 'التكوير' => 29, 'الانفطار' => 19, 'المطففين' => 36, 'الانشقاق' => 25,
    'البروج' => 22, 'الطارق' => 17, 'الأعلى' => 19, 'الغاشية' => 26, 'الفجر' => 30, 'البلد' => 20,
    'الشمس' => 15, 'الليل' => 21, 'الضحى' => 11, 'الشرح' => 8, 'التين' => 8, 'العلق' => 19,
    'القدر' => 5, 'البينة' => 8, 'الزلزلة' => 8, 'العاديات' => 11, 'القارعة' => 11, 'التكاثر' => 8,
    'العصر' => 3, 'الهمزة' => 9, 'الفيل' => 5, 'قريش' => 4, 'الماعون' => 7, 'الكوثر' => 3,
    'الكافرون' => 6, 'النصر' => 3, 'المسد' => 5, 'الإخلاص' => 4, 'الفلق' => 5, 'الناس' => 6
];

// Get scheduled memorization for the selected student
if (isset($selected_student) && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT ms.schedule_id, ms.target_surah_start, ms.target_ayah_start,
                   ms.target_surah_end, ms.target_ayah_end, ms.type, ms.due_date
            FROM memorization_schedules ms
            WHERE ms.enrollment_id = ? AND ms.is_completed = FALSE
            ORDER BY ms.due_date ASC
        ");
        $stmt->execute([$selected_student['enrollment_id']]);
        $scheduled_memorization = $stmt->fetchAll();

        // If schedule_id is provided, verify it belongs to this student
        if ($schedule_id) {
            $valid_schedule = false;
            foreach ($scheduled_memorization as $schedule) {
                if ($schedule['schedule_id'] == $schedule_id) {
                    $valid_schedule = true;
                    $selected_schedule = $schedule;
                    break;
                }
            }

            if (!$valid_schedule) {
                $schedule_id = null;
            }
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع خطط الحفظ: ' . $e->getMessage();
    }
}

// Get recent memorization progress for the selected student
if (isset($selected_student) && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to,
                   mp.recitation_date, mp.memorization_quality, mp.tajweed_application, mp.fluency,
                   mp.teacher_notes
            FROM memorization_progress mp
            WHERE mp.enrollment_id = ?
            ORDER BY mp.recitation_date DESC
            LIMIT 10
        ");
        $stmt->execute([$selected_student['enrollment_id']]);
        $recent_progress = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات التقدم: ' . $e->getMessage();
    }
}

// Process memorization form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_memorization'])) {
    $enrollment_id = (int)$_POST['enrollment_id'];
    $surah_name = sanitize_input($_POST['surah_name']);
    $ayah_from = (int)$_POST['ayah_from'];
    $ayah_to = (int)$_POST['ayah_to'];
    $memorization_quality = sanitize_input($_POST['memorization_quality']);
    $tajweed_application = sanitize_input($_POST['tajweed_application']);
    $fluency = sanitize_input($_POST['fluency']);
    $teacher_notes = sanitize_input($_POST['teacher_notes']);
    $schedule_id = isset($_POST['schedule_id']) ? (int)$_POST['schedule_id'] : null;

    // Validate input
    if (empty($surah_name) || $ayah_from <= 0 || $ayah_to <= 0) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif ($ayah_from > $ayah_to) {
        $error = 'يجب أن تكون الآية الأولى أقل من أو تساوي الآية الأخيرة';
    } elseif (!isset($surah_ayat_counts[$surah_name])) {
        $error = 'اسم السورة غير صالح';
    } elseif ($ayah_to > $surah_ayat_counts[$surah_name]) {
        $error = 'رقم الآية أكبر من عدد آيات السورة';
    } else {
        try {
            // Verify enrollment belongs to a circle taught by this teacher
            $stmt = $pdo->prepare("
                SELECT sce.enrollment_id
                FROM student_circle_enrollments sce
                JOIN circles c ON sce.circle_id = c.circle_id
                WHERE sce.enrollment_id = ? AND c.teacher_user_id = ?
            ");
            $stmt->execute([$enrollment_id, $teacher_id]);

            if ($stmt->rowCount() === 0) {
                $error = 'غير مصرح لك بتسجيل تسميع لهذا الطالب';
            } else {
                // Insert memorization progress
                $stmt = $pdo->prepare("
                    INSERT INTO memorization_progress (
                        schedule_id, enrollment_id, surah_name, ayah_from, ayah_to,
                        recitation_date, memorization_quality, tajweed_application, fluency,
                        teacher_notes, recorded_by_user_id
                    ) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $schedule_id, $enrollment_id, $surah_name, $ayah_from, $ayah_to,
                    $memorization_quality, $tajweed_application, $fluency,
                    $teacher_notes, $teacher_id
                ]);

                // If this is for a scheduled memorization, mark it as completed
                if ($schedule_id) {
                    $stmt = $pdo->prepare("
                        UPDATE memorization_schedules
                        SET is_completed = TRUE
                        WHERE schedule_id = ?
                    ");
                    $stmt->execute([$schedule_id]);
                }

                $success = 'تم تسجيل التسميع بنجاح';

                // Refresh recent progress
                $stmt = $pdo->prepare("
                    SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to,
                           mp.recitation_date, mp.memorization_quality, mp.tajweed_application, mp.fluency,
                           mp.teacher_notes
                    FROM memorization_progress mp
                    WHERE mp.enrollment_id = ?
                    ORDER BY mp.recitation_date DESC
                    LIMIT 10
                ");
                $stmt->execute([$enrollment_id]);
                $recent_progress = $stmt->fetchAll();

                // Refresh scheduled memorization
                $stmt = $pdo->prepare("
                    SELECT ms.schedule_id, ms.target_surah_start, ms.target_ayah_start,
                           ms.target_surah_end, ms.target_ayah_end, ms.type, ms.due_date
                    FROM memorization_schedules ms
                    WHERE ms.enrollment_id = ? AND ms.is_completed = FALSE
                    ORDER BY ms.due_date ASC
                ");
                $stmt->execute([$enrollment_id]);
                $scheduled_memorization = $stmt->fetchAll();
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تسجيل التسميع: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل التسميع - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <?php include_once '../includes/header.php'; ?>

    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>تسجيل التسميع</h1>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <?php if (!empty($circles) && empty($error)): ?>
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-book-reader me-2"></i>
                                تسجيل التسميع - <?php echo isset($selected_circle) ? $selected_circle['circle_name'] : ''; ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="mb-4">
                                <div class="row g-3 align-items-end">
                                    <div class="col-md-5">
                                        <label for="circle_id" class="form-label">الحلقة</label>
                                        <select class="form-select" id="circle_id" name="circle_id" required>
                                            <?php foreach ($circles as $circle): ?>
                                                <option value="<?php echo $circle['circle_id']; ?>" <?php echo $circle['circle_id'] == $circle_id ? 'selected' : ''; ?>>
                                                    <?php echo $circle['circle_name']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <label for="student_id" class="form-label">الطالب</label>
                                        <select class="form-select" id="student_id" name="student_id" required <?php echo empty($students) ? 'disabled' : ''; ?>>
                                            <?php if (!empty($students)): ?>
                                                <?php foreach ($students as $student): ?>
                                                    <option value="<?php echo $student['user_id']; ?>" <?php echo $student['user_id'] == $student_id ? 'selected' : ''; ?>>
                                                        <?php echo $student['full_name']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <option value="">لا يوجد طلاب</option>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary w-100">عرض</button>
                                    </div>
                                </div>
                            </form>

                            <?php if (isset($selected_student) && !empty($selected_student)): ?>
                                <div class="student-info mb-4">
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo !empty($selected_student['profile_picture_url']) ? '../' . $selected_student['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>"
                                             class="rounded-circle me-3" width="60" height="60" alt="صورة الطالب">
                                        <div>
                                            <h4 class="mb-1"><?php echo $selected_student['full_name']; ?></h4>
                                            <?php if (isset($scheduled_memorization) && !empty($scheduled_memorization)): ?>
                                                <span class="badge bg-info">لديه <?php echo count($scheduled_memorization); ?> خطط حفظ معلقة</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <?php if (isset($scheduled_memorization) && !empty($scheduled_memorization)): ?>
                                    <div class="scheduled-memorization mb-4">
                                        <h5>خطط الحفظ المعلقة</h5>
                                        <div class="list-group">
                                            <?php foreach ($scheduled_memorization as $schedule): ?>
                                                <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?circle_id=' . $circle_id . '&student_id=' . $student_id . '&schedule_id=' . $schedule['schedule_id']; ?>"
                                                   class="list-group-item list-group-item-action <?php echo $schedule_id == $schedule['schedule_id'] ? 'active' : ''; ?>">
                                                    <div class="d-flex w-100 justify-content-between">
                                                        <h6 class="mb-1">
                                                            <?php echo $schedule['type'] == 'memorization' ? 'حفظ جديد' : 'مراجعة'; ?>:
                                                            <?php echo $schedule['target_surah_start']; ?> (<?php echo $schedule['target_ayah_start']; ?>) -
                                                            <?php echo $schedule['target_surah_end']; ?> (<?php echo $schedule['target_ayah_end']; ?>)
                                                        </h6>
                                                        <small>
                                                            <?php
                                                            $due_date = strtotime($schedule['due_date']);
                                                            $now = time();
                                                            $days_diff = round(($due_date - $now) / (60 * 60 * 24));

                                                            if ($days_diff < 0) {
                                                                echo '<span class="badge bg-danger">متأخر ' . abs($days_diff) . ' يوم</span>';
                                                            } elseif ($days_diff == 0) {
                                                                echo '<span class="badge bg-warning">اليوم</span>';
                                                            } else {
                                                                echo '<span class="badge bg-info">متبقي ' . $days_diff . ' يوم</span>';
                                                            }
                                                            ?>
                                                        </small>
                                                    </div>
                                                    <small>تاريخ الاستحقاق: <?php echo date('Y-m-d', strtotime($schedule['due_date'])); ?></small>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?circle_id=' . $circle_id . '&student_id=' . $student_id . ($schedule_id ? '&schedule_id=' . $schedule_id : ''); ?>" id="memorization-form">
                                    <input type="hidden" name="enrollment_id" value="<?php echo $selected_student['enrollment_id']; ?>">
                                    <?php if ($schedule_id): ?>
                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule_id; ?>">
                                    <?php endif; ?>

                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="surah_name" class="form-label">السورة</label>
                                            <select class="form-select" id="surah_name" name="surah_name" required>
                                                <option value="">اختر السورة</option>
                                                <?php foreach ($surahs as $surah): ?>
                                                    <option value="<?php echo $surah; ?>" <?php echo (isset($selected_schedule) && $selected_schedule['target_surah_start'] == $surah) ? 'selected' : ''; ?>>
                                                        <?php echo $surah; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="ayah_from" class="form-label">من الآية</label>
                                            <input type="number" class="form-control" id="ayah_from" name="ayah_from" min="1"
                                                   value="<?php echo isset($selected_schedule) ? $selected_schedule['target_ayah_start'] : '1'; ?>" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="ayah_to" class="form-label">إلى الآية</label>
                                            <input type="number" class="form-control" id="ayah_to" name="ayah_to" min="1"
                                                   value="<?php echo isset($selected_schedule) ? $selected_schedule['target_ayah_end'] : ''; ?>" required>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="memorization_quality" class="form-label">جودة الحفظ</label>
                                            <select class="form-select" id="memorization_quality" name="memorization_quality" required>
                                                <option value="">اختر التقييم</option>
                                                <option value="excellent">ممتاز</option>
                                                <option value="very_good">جيد جداً</option>
                                                <option value="good">جيد</option>
                                                <option value="fair">مقبول</option>
                                                <option value="poor">ضعيف</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="tajweed_application" class="form-label">تطبيق أحكام التجويد</label>
                                            <select class="form-select" id="tajweed_application" name="tajweed_application" required>
                                                <option value="">اختر التقييم</option>
                                                <option value="excellent">ممتاز</option>
                                                <option value="very_good">جيد جداً</option>
                                                <option value="good">جيد</option>
                                                <option value="fair">مقبول</option>
                                                <option value="poor">ضعيف</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="fluency" class="form-label">الطلاقة</label>
                                            <select class="form-select" id="fluency" name="fluency" required>
                                                <option value="">اختر التقييم</option>
                                                <option value="excellent">ممتاز</option>
                                                <option value="very_good">جيد جداً</option>
                                                <option value="good">جيد</option>
                                                <option value="fair">مقبول</option>
                                                <option value="poor">ضعيف</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="teacher_notes" class="form-label">ملاحظات المعلم</label>
                                        <textarea class="form-control" id="teacher_notes" name="teacher_notes" rows="3" placeholder="أدخل ملاحظاتك حول التسميع (اختياري)"></textarea>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="submit" name="save_memorization" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i> حفظ التسميع
                                        </button>
                                    </div>
                                </form>
                            <?php elseif (!empty($students)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> يرجى اختيار طالب لتسجيل التسميع.
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> لا يوجد طلاب مسجلين في هذه الحلقة.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <?php if (isset($selected_student) && !empty($selected_student)): ?>
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i> آخر التسميعات</h5>
                            </div>
                            <div class="card-body">
                                <?php if (isset($recent_progress) && !empty($recent_progress)): ?>
                                    <div class="list-group">
                                        <?php foreach ($recent_progress as $progress): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1">
                                                        <?php echo $progress['surah_name']; ?> (<?php echo $progress['ayah_from']; ?> - <?php echo $progress['ayah_to']; ?>)
                                                    </h6>
                                                    <small><?php echo date('Y-m-d', strtotime($progress['recitation_date'])); ?></small>
                                                </div>
                                                <div class="d-flex justify-content-between mt-2">
                                                    <span class="badge bg-<?php
                                                        $quality = $progress['memorization_quality'];
                                                        $color = '';
                                                        switch ($quality) {
                                                            case 'excellent': $color = 'success'; break;
                                                            case 'very_good': $color = 'primary'; break;
                                                            case 'good': $color = 'info'; break;
                                                            case 'fair': $color = 'warning'; break;
                                                            case 'poor': $color = 'danger'; break;
                                                        }
                                                        echo $color;
                                                    ?>">
                                                        الحفظ: <?php echo ucfirst(str_replace('_', ' ', $progress['memorization_quality'])); ?>
                                                    </span>
                                                    <span class="badge bg-<?php
                                                        $tajweed = $progress['tajweed_application'];
                                                        $color = '';
                                                        switch ($tajweed) {
                                                            case 'excellent': $color = 'success'; break;
                                                            case 'very_good': $color = 'primary'; break;
                                                            case 'good': $color = 'info'; break;
                                                            case 'fair': $color = 'warning'; break;
                                                            case 'poor': $color = 'danger'; break;
                                                        }
                                                        echo $color;
                                                    ?>">
                                                        التجويد: <?php echo ucfirst(str_replace('_', ' ', $progress['tajweed_application'])); ?>
                                                    </span>
                                                </div>
                                                <?php if (!empty($progress['teacher_notes'])): ?>
                                                    <small class="d-block mt-2 text-muted">
                                                        <i class="fas fa-comment me-1"></i> <?php echo $progress['teacher_notes']; ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-center">لا توجد تسميعات سابقة لهذا الطالب.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> إرشادات</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> <strong>ممتاز:</strong> حفظ متقن بدون أخطاء
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-primary me-2"></i> <strong>جيد جداً:</strong> حفظ جيد مع أخطاء بسيطة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-info me-2"></i> <strong>جيد:</strong> حفظ مقبول مع بعض الأخطاء
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-exclamation-circle text-warning me-2"></i> <strong>مقبول:</strong> حفظ ضعيف مع أخطاء متكررة
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times-circle text-danger me-2"></i> <strong>ضعيف:</strong> حفظ غير مكتمل أو به أخطاء كثيرة
                                </li>
                            </ul>
                            <hr>
                            <p class="mb-0">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                يمكنك إضافة ملاحظات تفصيلية لمساعدة الطالب على تحسين حفظه وتلاوته.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?php include_once '../includes/footer.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form when changing circle or student
            const circleSelect = document.getElementById('circle_id');
            const studentSelect = document.getElementById('student_id');

            if (circleSelect && studentSelect) {
                circleSelect.addEventListener('change', function() {
                    this.form.submit();
                });

                studentSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }

            // Update max ayah value based on selected surah
            const surahSelect = document.getElementById('surah_name');
            const ayahToInput = document.getElementById('ayah_to');

            if (surahSelect && ayahToInput) {
                const surahAyatCounts = <?php echo json_encode($surah_ayat_counts); ?>;

                surahSelect.addEventListener('change', function() {
                    const selectedSurah = this.value;
                    if (selectedSurah && surahAyatCounts[selectedSurah]) {
                        ayahToInput.max = surahAyatCounts[selectedSurah];
                        ayahToInput.placeholder = `الحد الأقصى: ${surahAyatCounts[selectedSurah]}`;
                    }
                });

                // Trigger change event to set initial max value
                if (surahSelect.value) {
                    surahSelect.dispatchEvent(new Event('change'));
                }
            }
        });
    </script>
</body>
</html>