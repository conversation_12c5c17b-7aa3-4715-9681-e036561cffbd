<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and is a teacher
if (!is_logged_in() || !has_role('teacher')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('../auth/login.php');
}

// Get circle ID from URL
$circle_id = isset($_GET['circle_id']) ? (int)$_GET['circle_id'] : 0;

$error = '';
$success = '';
$user_id = $_SESSION['user_id'];

// Get circle information and verify teacher access
$circle = null;
if ($circle_id > 0) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name, c.description, c.level, c.teacher_user_id,
                   cen.center_name, cen.center_id,
                   COUNT(sce.student_user_id) AS student_count
            FROM circles c
            JOIN centers cen ON c.center_id = cen.center_id
            LEFT JOIN student_circle_enrollments sce ON c.circle_id = sce.circle_id AND sce.status = 'approved'
            WHERE c.circle_id = ? AND c.is_active = TRUE AND c.teacher_user_id = ?
            GROUP BY c.circle_id
        ");
        $stmt->execute([$circle_id, $user_id]);
        $circle = $stmt->fetch();

        if (!$circle) {
            set_flash_message('danger', 'الحلقة غير موجودة أو غير مصرح لك بالوصول إليها');
            redirect('teacher_dashboard.php');
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
    }
}

// Get teacher's circles for dropdown
$teacher_circles = [];
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name, c.level,
               COUNT(sce.student_user_id) AS student_count
        FROM circles c
        LEFT JOIN student_circle_enrollments sce ON c.circle_id = sce.circle_id AND sce.status = 'approved'
        WHERE c.teacher_user_id = ? AND c.is_active = TRUE
        GROUP BY c.circle_id
        ORDER BY c.circle_name
    ");
    $stmt->execute([$user_id]);
    $teacher_circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_announcement'])) {
    $selected_circle_id = (int)$_POST['circle_id'];
    $title = sanitize_input($_POST['title']);
    $content = sanitize_input($_POST['content']);
    $target_audience = sanitize_input($_POST['target_audience']);
    $priority = sanitize_input($_POST['priority']);
    $end_date = sanitize_input($_POST['end_date']);

    // Validation
    if (empty($title)) {
        $error = 'يرجى إدخال عنوان الإعلان';
    } elseif (empty($content)) {
        $error = 'يرجى إدخال محتوى الإعلان';
    } elseif ($selected_circle_id <= 0) {
        $error = 'يرجى اختيار الحلقة';
    } elseif (empty($end_date)) {
        $error = 'يرجى تحديد تاريخ انتهاء الإعلان';
    } else {
        // Verify teacher owns the selected circle
        $stmt = $pdo->prepare("
            SELECT circle_id FROM circles 
            WHERE circle_id = ? AND teacher_user_id = ? AND is_active = TRUE
        ");
        $stmt->execute([$selected_circle_id, $user_id]);
        
        if (!$stmt->fetch()) {
            $error = 'غير مصرح لك بإنشاء إعلان لهذه الحلقة';
        } else {
            try {
                // Insert announcement
                $stmt = $pdo->prepare("
                    INSERT INTO announcements (
                        title, content, sender_user_id, target_role, target_center_id,
                        circle_id, priority_level, is_active, start_date, end_date,
                        created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, TRUE, CURDATE(), ?, NOW())
                ");
                
                $stmt->execute([
                    $title,
                    $content,
                    $user_id,
                    $target_audience,
                    $_SESSION['center_id'],
                    $selected_circle_id,
                    $priority,
                    $end_date
                ]);

                $announcement_id = $pdo->lastInsertId();

                // If targeting students, create individual announcement assignments
                if ($target_audience === 'student' || $target_audience === 'all') {
                    $stmt = $pdo->prepare("
                        SELECT sce.student_user_id 
                        FROM student_circle_enrollments sce
                        WHERE sce.circle_id = ? AND sce.status = 'approved'
                    ");
                    $stmt->execute([$selected_circle_id]);
                    $students = $stmt->fetchAll();

                    // Create announcement_recipients table if it doesn't exist
                    $pdo->exec("
                        CREATE TABLE IF NOT EXISTS announcement_recipients (
                            recipient_id INT AUTO_INCREMENT PRIMARY KEY,
                            announcement_id INT NOT NULL,
                            user_id INT NOT NULL,
                            is_read BOOLEAN DEFAULT FALSE,
                            read_at DATETIME NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE,
                            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                            UNIQUE KEY unique_announcement_user (announcement_id, user_id)
                        )
                    ");

                    // Insert recipients
                    foreach ($students as $student) {
                        $stmt = $pdo->prepare("
                            INSERT IGNORE INTO announcement_recipients (announcement_id, user_id)
                            VALUES (?, ?)
                        ");
                        $stmt->execute([$announcement_id, $student['student_user_id']]);
                    }
                }

                $success = 'تم إنشاء الإعلان بنجاح وإرساله إلى الطلاب';
                
                // Redirect to circle announcements page
                set_flash_message('success', $success);
                redirect('circle_announcements.php?circle_id=' . $selected_circle_id);
                
            } catch (PDOException $e) {
                $error = 'حدث خطأ أثناء إنشاء الإعلان: ' . $e->getMessage();
            }
        }
    }
}

// Page variables
$page_title = 'إنشاء إعلان للحلقة';
$active_page = 'announcements';

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">
            <i class="fas fa-bullhorn me-2"></i> 
            إنشاء إعلان للحلقة
        </h1>
        <div>
            <?php if ($circle): ?>
            <a href="<?php echo get_root_url(); ?>pages/circle_announcements.php?circle_id=<?php echo $circle['circle_id']; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة إلى إعلانات الحلقة
            </a>
            <?php else: ?>
            <a href="<?php echo get_root_url(); ?>pages/teacher_dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة إلى لوحة التحكم
            </a>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <?php if (empty($teacher_circles)): ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> لا توجد حلقات مسجلة لك. يجب أن تكون مسؤولاً عن حلقة واحدة على الأقل لإنشاء إعلانات.
            <div class="mt-2">
                <a href="<?php echo get_root_url(); ?>pages/teacher_dashboard.php" class="btn btn-primary">
                    العودة إلى لوحة التحكم
                </a>
            </div>
        </div>
    <?php else: ?>
        <!-- Create Announcement Form -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i> إنشاء إعلان جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?><?php echo $circle ? '?circle_id=' . $circle['circle_id'] : ''; ?>">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="circle_id" class="form-label">الحلقة <span class="text-danger">*</span></label>
                                <select class="form-select" id="circle_id" name="circle_id" required>
                                    <option value="">اختر الحلقة</option>
                                    <?php foreach ($teacher_circles as $tc): ?>
                                    <option value="<?php echo $tc['circle_id']; ?>" <?php echo ($circle && $circle['circle_id'] == $tc['circle_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($tc['circle_name']); ?> 
                                        (<?php echo $tc['student_count']; ?> طالب)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="target_audience" class="form-label">الجمهور المستهدف <span class="text-danger">*</span></label>
                                <select class="form-select" id="target_audience" name="target_audience" required>
                                    <option value="student">الطلاب فقط</option>
                                    <option value="parent">أولياء الأمور فقط</option>
                                    <option value="all">الطلاب وأولياء الأمور</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="priority" class="form-label">مستوى الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="normal">عادي</option>
                                    <option value="high">مهم</option>
                                    <option value="urgent">عاجل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">تاريخ انتهاء الإعلان <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" 
                                       min="<?php echo date('Y-m-d'); ?>" 
                                       value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان الإعلان <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" 
                               placeholder="أدخل عنوان الإعلان" maxlength="255" required>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">محتوى الإعلان <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content" name="content" rows="6" 
                                  placeholder="اكتب محتوى الإعلان هنا..." required></textarea>
                        <div class="form-text">يمكنك استخدام النص العادي. سيتم عرض النص كما هو مكتوب.</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" name="create_announcement" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i> إنشاء وإرسال الإعلان
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-undo me-1"></i> إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="card mt-4" id="preview-card" style="display: none;">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i> معاينة الإعلان
                </h5>
            </div>
            <div class="card-body">
                <h5 id="preview-title"></h5>
                <p id="preview-content"></p>
                <div class="text-muted small">
                    <p class="mb-1"><i class="fas fa-user me-1"></i> بواسطة: <?php echo htmlspecialchars($_SESSION['full_name']); ?></p>
                    <p class="mb-0"><i class="fas fa-clock me-1"></i> الآن</p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Live preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const contentInput = document.getElementById('content');
    const previewCard = document.getElementById('preview-card');
    const previewTitle = document.getElementById('preview-title');
    const previewContent = document.getElementById('preview-content');

    function updatePreview() {
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();

        if (title || content) {
            previewTitle.textContent = title || 'عنوان الإعلان';
            previewContent.textContent = content || 'محتوى الإعلان';
            previewCard.style.display = 'block';
        } else {
            previewCard.style.display = 'none';
        }
    }

    titleInput.addEventListener('input', updatePreview);
    contentInput.addEventListener('input', updatePreview);
});
</script>

<?php
// Include footer template
include_template('footer');
?>
