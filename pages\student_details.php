<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('../auth/login.php');
}

// Get user role
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$error = '';
$success = '';

// Check if student ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    set_flash_message('danger', 'معرف الطالب غير صالح');
    redirect('parent_dashboard.php');
}

$student_id = (int)$_GET['id'];

// Check if user has permission to view this student's details
$has_permission = false;

if ($role_name === 'system_owner' || $role_name === 'center_admin') {
    $has_permission = true;
} elseif ($role_name === 'teacher') {
    // Check if the student is in one of the teacher's circles
    try {
        $stmt = $pdo->prepare("
            SELECT 1
            FROM student_circle_enrollments sce
            JOIN circles c ON sce.circle_id = c.circle_id
            WHERE sce.student_user_id = ? AND c.teacher_user_id = ?
            LIMIT 1
        ");
        $stmt->execute([$student_id, $user_id]);
        $has_permission = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
    }
} elseif ($role_name === 'parent') {
    // Check if the student is a child of this parent
    try {
        $stmt = $pdo->prepare("
            SELECT 1
            FROM Parent_Student_Relations
            WHERE parent_user_id = ? AND student_user_id = ?
            LIMIT 1
        ");
        $stmt->execute([$user_id, $student_id]);
        $has_permission = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
    }
} elseif ($role_name === 'student') {
    // Students can only view their own details
    $has_permission = ($student_id === $user_id);
}

if (!$has_permission) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى بيانات هذا الطالب');
    redirect('parent_dashboard.php');
}

// Get student information
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.username, u.full_name, u.email, u.phone_number, u.profile_picture_url,
               c.center_name
        FROM users u
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ? AND u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
    ");
    $stmt->execute([$student_id]);
    $student = $stmt->fetch();

    if (!$student) {
        set_flash_message('danger', 'الطالب غير موجود');
        redirect('parent_dashboard.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطالب: ' . $e->getMessage();
}

// Get student's circle information
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name, c.level, c.schedule_details,
               t.user_id AS teacher_id, t.full_name AS teacher_name,
               ce.center_name, sce.enrollment_id, sce.enrollment_date
        FROM student_circle_enrollments sce
        JOIN circles c ON sce.circle_id = c.circle_id
        JOIN users t ON c.teacher_user_id = t.user_id
        JOIN centers ce ON c.center_id = ce.center_id
        WHERE sce.student_user_id = ? AND sce.status = 'approved'
    ");
    $stmt->execute([$student_id]);
    $circle = $stmt->fetch();

    if ($circle) {
        $enrollment_id = $circle['enrollment_id'];
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
}

// Get student's parents
try {
    $stmt = $pdo->prepare("
        SELECT psr.relation_id, psr.relation_type, psr.is_primary,
               u.user_id, u.full_name, u.email, u.phone_number
        FROM Parent_Student_Relations psr
        JOIN users u ON psr.parent_user_id = u.user_id
        WHERE psr.student_user_id = ?
        ORDER BY psr.is_primary DESC, u.full_name
    ");
    $stmt->execute([$student_id]);
    $parents = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات أولياء الأمور: ' . $e->getMessage();
}

// Get student's memorization progress
if (isset($enrollment_id)) {
    try {
        // Get recent memorization progress
        $stmt = $pdo->prepare("
            SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to,
                   mp.recitation_date, mp.memorization_quality, mp.tajweed_application, mp.fluency,
                   mp.teacher_notes, u.full_name AS teacher_name
            FROM memorization_progress mp
            JOIN users u ON mp.recorded_by_user_id = u.user_id
            WHERE mp.enrollment_id = ?
            ORDER BY mp.recitation_date DESC
            LIMIT 10
        ");
        $stmt->execute([$enrollment_id]);
        $memorization_progress = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات التقدم: ' . $e->getMessage();
    }

    // Get attendance records
    try {
        $stmt = $pdo->prepare("
            SELECT ar.attendance_id, ar.session_date, ar.status, ar.notes
            FROM attendance_records ar
            WHERE ar.enrollment_id = ?
            ORDER BY ar.session_date DESC
            LIMIT 10
        ");
        $stmt->execute([$enrollment_id]);
        $attendance_records = $stmt->fetchAll();

        // Calculate attendance statistics
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS total_sessions,
                   SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) AS present_count,
                   SUM(CASE WHEN status = 'absent_excused' THEN 1 ELSE 0 END) AS excused_count,
                   SUM(CASE WHEN status = 'absent_unexcused' THEN 1 ELSE 0 END) AS unexcused_count,
                   SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) AS late_count
            FROM attendance_records
            WHERE enrollment_id = ?
        ");
        $stmt->execute([$enrollment_id]);
        $attendance_stats = $stmt->fetch();

        // Calculate attendance percentage
        if ($attendance_stats['total_sessions'] > 0) {
            $attendance_percentage = round(($attendance_stats['present_count'] / $attendance_stats['total_sessions']) * 100);
        } else {
            $attendance_percentage = 0;
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الحضور: ' . $e->getMessage();
    }

    // Get current memorization schedule
    try {
        $stmt = $pdo->prepare("
            SELECT schedule_id, target_surah_start, target_ayah_start,
                   target_surah_end, target_ayah_end,
                   target_juz, target_page_start, target_page_end,
                   type, assigned_date, due_date, is_completed
            FROM memorization_schedules
            WHERE enrollment_id = ? AND is_completed = FALSE
            ORDER BY due_date ASC
            LIMIT 1
        ");
        $stmt->execute([$enrollment_id]);
        $current_schedule = $stmt->fetch();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات خطة الحفظ: ' . $e->getMessage();
    }

    // Get assignments
    try {
        // Check if student_assignments table exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'student_assignments'
        ");
        $stmt->execute();
        $table_exists = (bool)$stmt->fetchColumn();

        if ($table_exists) {
            // If table exists, get assignments with student data
            $stmt = $pdo->prepare("
                SELECT a.assignment_id, a.title, a.description, a.due_date,
                       sa.status, sa.submission_date, sa.grade, sa.feedback
                FROM assignments a
                JOIN student_assignments sa ON a.assignment_id = sa.assignment_id
                WHERE sa.student_user_id = ? AND a.circle_id = ?
                ORDER BY a.due_date DESC
                LIMIT 10
            ");
            $stmt->execute([$student_id, $circle['circle_id']]);
            $assignments = $stmt->fetchAll();
        } else {
            // If table doesn't exist, just get assignments for the circle
            $stmt = $pdo->prepare("
                SELECT a.assignment_id, a.title, a.description, a.due_date,
                       'pending' AS status, NULL AS submission_date, NULL AS grade, NULL AS feedback
                FROM assignments a
                WHERE a.circle_id = ?
                ORDER BY a.due_date DESC
                LIMIT 10
            ");
            $stmt->execute([$circle['circle_id']]);
            $assignments = $stmt->fetchAll();
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الواجبات: ' . $e->getMessage();
        $assignments = []; // Initialize as empty array to prevent further errors
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطالب - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .profile-header {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .profile-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .attendance-badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
        .quality-badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if ($role_name === 'parent'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="parent_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="my_children.php">أبنائي</a>
                            </li>
                        <?php elseif ($role_name === 'teacher'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="teacher_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="my_circles.php">حلقاتي</a>
                            </li>
                        <?php elseif ($role_name === 'center_admin'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="center_admin_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="students.php">الطلاب</a>
                            </li>
                        <?php elseif ($role_name === 'system_owner'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="system_owner_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="users.php">المستخدمين</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="messages.php">الرسائل</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container py-4">
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <!-- Student Profile Header -->
        <div class="profile-header">
            <div class="row align-items-center">
                <div class="col-md-3 text-center">
                    <img src="<?php echo !empty($student['profile_picture_url']) ? '../' . $student['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>"
                         class="profile-image mb-3" alt="صورة الطالب">
                </div>
                <div class="col-md-9">
                    <h1 class="mb-2"><?php echo $student['full_name']; ?></h1>
                    <p class="mb-1"><i class="fas fa-envelope me-2"></i> <?php echo $student['email']; ?></p>
                    <p class="mb-1"><i class="fas fa-phone me-2"></i> <?php echo $student['phone_number'] ?: 'غير متوفر'; ?></p>
                    <p class="mb-1"><i class="fas fa-building me-2"></i> <?php echo $student['center_name'] ?: 'غير منتسب لمركز'; ?></p>

                    <?php if ($role_name === 'teacher' || $role_name === 'center_admin' || $role_name === 'system_owner'): ?>
                        <div class="mt-3">
                            <a href="memorization.php?student_id=<?php echo $student_id; ?>&circle_id=<?php echo $circle['circle_id']; ?>" class="btn btn-success">
                                <i class="fas fa-book-reader me-1"></i> تسميع
                            </a>
                            <a href="messages.php?new=1&recipient=<?php echo $student_id; ?>" class="btn btn-primary">
                                <i class="fas fa-envelope me-1"></i> إرسال رسالة
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Circle Information -->
        <?php if (isset($circle)): ?>
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> معلومات الحلقة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم الحلقة:</strong> <?php echo $circle['circle_name']; ?></p>
                            <p><strong>المستوى:</strong> <?php echo $circle['level']; ?></p>
                            <p><strong>تاريخ الالتحاق:</strong> <?php echo date('Y-m-d', strtotime($circle['enrollment_date'])); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>المعلم:</strong> <?php echo $circle['teacher_name']; ?></p>
                            <p><strong>المركز:</strong> <?php echo $circle['center_name']; ?></p>
                            <p><strong>مواعيد الحلقة:</strong> <?php echo $circle['schedule_details']; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-warning mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i> هذا الطالب غير مسجل في أي حلقة حالياً.
            </div>
        <?php endif; ?>

        <!-- Tabs Navigation -->
        <ul class="nav nav-tabs mb-0" id="studentTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="parents-tab" data-bs-toggle="tab" data-bs-target="#parents" type="button" role="tab" aria-controls="parents" aria-selected="true">
                    <i class="fas fa-users me-1"></i> أولياء الأمور
                </button>
            </li>
            <?php if (isset($circle)): ?>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="progress-tab" data-bs-toggle="tab" data-bs-target="#progress" type="button" role="tab" aria-controls="progress" aria-selected="false">
                        <i class="fas fa-chart-line me-1"></i> تقدم الحفظ
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab" aria-controls="attendance" aria-selected="false">
                        <i class="fas fa-calendar-check me-1"></i> الحضور والغياب
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="assignments-tab" data-bs-toggle="tab" data-bs-target="#assignments" type="button" role="tab" aria-controls="assignments" aria-selected="false">
                        <i class="fas fa-tasks me-1"></i> الواجبات
                    </button>
                </li>
            <?php endif; ?>
        </ul>

        <!-- Tabs Content -->
        <div class="tab-content" id="studentTabsContent">
            <!-- Parents Tab -->
            <div class="tab-pane fade show active" id="parents" role="tabpanel" aria-labelledby="parents-tab">
                <?php if (empty($parents)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا يوجد أولياء أمور مسجلين لهذا الطالب.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>نوع العلاقة</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($parents as $parent): ?>
                                    <tr>
                                        <td><?php echo $parent['full_name']; ?></td>
                                        <td><?php echo $parent['relation_type']; ?></td>
                                        <td><?php echo $parent['email']; ?></td>
                                        <td><?php echo $parent['phone_number'] ?: 'غير متوفر'; ?></td>
                                        <td>
                                            <?php if ($parent['is_primary']): ?>
                                                <span class="badge bg-primary">ولي الأمر الرئيسي</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">ولي أمر ثانوي</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <?php if (isset($circle)): ?>
                <!-- Progress Tab -->
                <div class="tab-pane fade" id="progress" role="tabpanel" aria-labelledby="progress-tab">
                    <?php if (isset($current_schedule) && $current_schedule !== false): ?>
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-bookmark me-2"></i> خطة الحفظ الحالية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><?php echo isset($current_schedule['type']) && $current_schedule['type'] == 'memorization' ? 'حفظ جديد' : 'مراجعة'; ?></h5>
                                        <?php if (isset($current_schedule['target_surah_start']) && !empty($current_schedule['target_surah_start'])): ?>
                                            <p>
                                                <strong>المقطع:</strong>
                                                <?php echo $current_schedule['target_surah_start']; ?>
                                                (<?php echo $current_schedule['target_ayah_start']; ?>) -
                                                <?php echo $current_schedule['target_surah_end']; ?>
                                                (<?php echo $current_schedule['target_ayah_end']; ?>)
                                            </p>
                                        <?php endif; ?>

                                        <?php if (isset($current_schedule['target_juz']) && !empty($current_schedule['target_juz'])): ?>
                                            <p><strong>الجزء:</strong> <?php echo $current_schedule['target_juz']; ?></p>
                                        <?php endif; ?>

                                        <?php if (isset($current_schedule['target_page_start']) && !empty($current_schedule['target_page_start'])): ?>
                                            <p>
                                                <strong>الصفحات:</strong>
                                                <?php echo $current_schedule['target_page_start']; ?> -
                                                <?php echo $current_schedule['target_page_end']; ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>تاريخ التكليف:</strong>
                                            <?php echo isset($current_schedule['assigned_date']) && !empty($current_schedule['assigned_date']) ?
                                                date('Y-m-d', strtotime($current_schedule['assigned_date'])) : 'غير محدد'; ?>
                                        </p>
                                        <p><strong>تاريخ الاستحقاق:</strong>
                                            <?php echo isset($current_schedule['due_date']) && !empty($current_schedule['due_date']) ?
                                                date('Y-m-d', strtotime($current_schedule['due_date'])) : 'غير محدد'; ?>
                                        </p>

                                        <?php
                                        if (isset($current_schedule['due_date']) && !empty($current_schedule['due_date'])) {
                                            $due_date = strtotime($current_schedule['due_date']);
                                            $now = time();
                                            $days_left = ceil(($due_date - $now) / (60 * 60 * 24));

                                            if ($days_left < 0) {
                                                echo '<div class="alert alert-danger">متأخر بـ ' . abs($days_left) . ' يوم</div>';
                                            } elseif ($days_left == 0) {
                                                echo '<div class="alert alert-warning">مستحق اليوم</div>';
                                            } else {
                                                echo '<div class="alert alert-info">متبقي ' . $days_left . ' يوم</div>';
                                            }
                                        } else {
                                            echo '<div class="alert alert-secondary">تاريخ الاستحقاق غير محدد</div>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (empty($memorization_progress)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا يوجد تقدم مسجل لهذا الطالب حالياً.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>السورة</th>
                                        <th>الآيات</th>
                                        <th>جودة الحفظ</th>
                                        <th>التجويد</th>
                                        <th>الطلاقة</th>
                                        <th>المعلم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($memorization_progress as $progress): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d', strtotime($progress['recitation_date'])); ?></td>
                                            <td><?php echo $progress['surah_name']; ?></td>
                                            <td><?php echo $progress['ayah_from'] . ' - ' . $progress['ayah_to']; ?></td>
                                            <td>
                                                <?php
                                                $quality = $progress['memorization_quality'];
                                                $color = '';
                                                switch ($quality) {
                                                    case 'excellent': $color = 'success'; $label = 'ممتاز'; break;
                                                    case 'very_good': $color = 'primary'; $label = 'جيد جداً'; break;
                                                    case 'good': $color = 'info'; $label = 'جيد'; break;
                                                    case 'fair': $color = 'warning'; $label = 'مقبول'; break;
                                                    case 'poor': $color = 'danger'; $label = 'ضعيف'; break;
                                                    default: $color = 'secondary'; $label = $quality;
                                                }
                                                echo '<span class="badge bg-' . $color . ' quality-badge">' . $label . '</span>';
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $quality = $progress['tajweed_application'];
                                                $color = '';
                                                switch ($quality) {
                                                    case 'excellent': $color = 'success'; $label = 'ممتاز'; break;
                                                    case 'very_good': $color = 'primary'; $label = 'جيد جداً'; break;
                                                    case 'good': $color = 'info'; $label = 'جيد'; break;
                                                    case 'fair': $color = 'warning'; $label = 'مقبول'; break;
                                                    case 'poor': $color = 'danger'; $label = 'ضعيف'; break;
                                                    default: $color = 'secondary'; $label = $quality;
                                                }
                                                echo '<span class="badge bg-' . $color . ' quality-badge">' . $label . '</span>';
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $quality = $progress['fluency'];
                                                $color = '';
                                                switch ($quality) {
                                                    case 'excellent': $color = 'success'; $label = 'ممتاز'; break;
                                                    case 'very_good': $color = 'primary'; $label = 'جيد جداً'; break;
                                                    case 'good': $color = 'info'; $label = 'جيد'; break;
                                                    case 'fair': $color = 'warning'; $label = 'مقبول'; break;
                                                    case 'poor': $color = 'danger'; $label = 'ضعيف'; break;
                                                    default: $color = 'secondary'; $label = $quality;
                                                }
                                                echo '<span class="badge bg-' . $color . ' quality-badge">' . $label . '</span>';
                                                ?>
                                            </td>
                                            <td><?php echo $progress['teacher_name']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Attendance Tab -->
                <div class="tab-pane fade" id="attendance" role="tabpanel" aria-labelledby="attendance-tab">
                    <?php if (isset($attendance_stats)): ?>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i> إحصائيات الحضور</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-6 mb-3">
                                                <h2 class="text-primary"><?php echo $attendance_percentage; ?>%</h2>
                                                <p>نسبة الحضور</p>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <h2 class="text-dark"><?php echo $attendance_stats['total_sessions']; ?></h2>
                                                <p>إجمالي الجلسات</p>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <h3 class="text-success"><?php echo $attendance_stats['present_count']; ?></h3>
                                                <p>حضور</p>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <h3 class="text-warning"><?php echo $attendance_stats['late_count']; ?></h3>
                                                <p>تأخير</p>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <h3 class="text-info"><?php echo $attendance_stats['excused_count']; ?></h3>
                                                <p>غياب بعذر</p>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <h3 class="text-danger"><?php echo $attendance_stats['unexcused_count']; ?></h3>
                                                <p>غياب بدون عذر</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i> رسم بياني للحضور</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="attendanceChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (empty($attendance_records)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا توجد سجلات حضور لهذا الطالب حالياً.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($attendance_records as $record): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d', strtotime($record['session_date'])); ?></td>
                                            <td>
                                                <?php
                                                $status = $record['status'];
                                                $color = '';
                                                $label = '';
                                                switch ($status) {
                                                    case 'present': $color = 'success'; $label = 'حاضر'; break;
                                                    case 'absent_excused': $color = 'info'; $label = 'غائب بعذر'; break;
                                                    case 'absent_unexcused': $color = 'danger'; $label = 'غائب بدون عذر'; break;
                                                    case 'late': $color = 'warning'; $label = 'متأخر'; break;
                                                    default: $color = 'secondary'; $label = $status;
                                                }
                                                echo '<span class="badge bg-' . $color . ' attendance-badge">' . $label . '</span>';
                                                ?>
                                            </td>
                                            <td><?php echo $record['notes'] ?: '-'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Assignments Tab -->
                <div class="tab-pane fade" id="assignments" role="tabpanel" aria-labelledby="assignments-tab">
                    <?php if (empty($assignments)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا توجد واجبات لهذا الطالب حالياً.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>تاريخ التسليم</th>
                                        <th>الحالة</th>
                                        <th>التقييم</th>
                                        <th>ملاحظات المعلم</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($assignments as $assignment): ?>
                                        <tr>
                                            <td>
                                                <a href="assignment_details.php?id=<?php echo $assignment['assignment_id']; ?>">
                                                    <?php echo $assignment['title']; ?>
                                                </a>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($assignment['due_date'])); ?></td>
                                            <td>
                                                <?php
                                                $status = $assignment['status'];
                                                $color = '';
                                                $label = '';
                                                switch ($status) {
                                                    case 'pending': $color = 'warning'; $label = 'قيد الانتظار'; break;
                                                    case 'submitted': $color = 'info'; $label = 'تم التسليم'; break;
                                                    case 'late_submission': $color = 'warning'; $label = 'تسليم متأخر'; break;
                                                    case 'graded': $color = 'success'; $label = 'تم التقييم'; break;
                                                    case 'not_submitted': $color = 'danger'; $label = 'لم يتم التسليم'; break;
                                                    default: $color = 'secondary'; $label = $status;
                                                }
                                                echo '<span class="badge bg-' . $color . '">' . $label . '</span>';
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($assignment['status'] === 'graded'): ?>
                                                    <span class="badge bg-primary"><?php echo $assignment['grade']; ?>/10</span>
                                                <?php else: ?>
                                                    -
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $assignment['feedback'] ?: '-'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>

    <?php if (isset($attendance_stats)): ?>
    <script>
        // Initialize attendance chart
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('attendanceChart').getContext('2d');
            const attendanceChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['حضور', 'تأخير', 'غياب بعذر', 'غياب بدون عذر'],
                    datasets: [{
                        data: [
                            <?php echo $attendance_stats['present_count']; ?>,
                            <?php echo $attendance_stats['late_count']; ?>,
                            <?php echo $attendance_stats['excused_count']; ?>,
                            <?php echo $attendance_stats['unexcused_count']; ?>
                        ],
                        backgroundColor: [
                            '#28a745', // success
                            '#ffc107', // warning
                            '#17a2b8', // info
                            '#dc3545'  // danger
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        });
    </script>
    <?php endif; ?>
</body>
</html>