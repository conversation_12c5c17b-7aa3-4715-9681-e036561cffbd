<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is already logged in
if (is_logged_in()) {
    redirect('index.php');
}

$error = '';
$success = '';

// Get roles for dropdown
try {
    $stmt = $pdo->prepare("SELECT role_id, role_name FROM roles WHERE role_name IN ('parent', 'student', 'teacher')");
    $stmt->execute();
    $roles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الأدوار: ' . $e->getMessage();
}

// Get centers for dropdown
try {
    $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE");
    $stmt->execute();
    $centers = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع المراكز: ' . $e->getMessage();
}

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $role_id = (int)$_POST['role_id'];
    $center_id = !empty($_POST['center_id']) ? (int)$_POST['center_id'] : null;

    // Validate input
    if (empty($username) || empty($password) || empty($confirm_password) || empty($full_name) || empty($email)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقين';
    } elseif (strlen($password) < 6) {
        $error = 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } else {
        try {
            // Check if username already exists
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->rowCount() > 0) {
                $error = 'اسم المستخدم موجود بالفعل، يرجى اختيار اسم مستخدم آخر';
            } else {
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->rowCount() > 0) {
                    $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد إلكتروني آخر';
                } else {
                    // Hash password
                    $password_hash = password_hash($password, PASSWORD_DEFAULT);

                    // Insert new user
                    $stmt = $pdo->prepare("
                        INSERT INTO users (username, password_hash, full_name, email, phone_number, role_id, center_id, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, TRUE)
                    ");
                    $stmt->execute([$username, $password_hash, $full_name, $email, $phone_number, $role_id, $center_id]);

                    $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.';
                }
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إنشاء الحساب: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-light">
    <div class="container">
        <div class="auth-form">
            <h2 class="form-title">إنشاء حساب جديد</h2>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
                <div class="text-center mb-3">
                    <a href="login.php" class="btn btn-primary">تسجيل الدخول</a>
                </div>
            <?php else: ?>
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <button class="btn btn-outline-secondary toggle-password" type="button" toggle="#password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <small class="text-muted">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user-circle"></i></span>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="phone_number" class="form-label">رقم الهاتف</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                            <input type="text" class="form-control" id="phone_number" name="phone_number">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="role_id" class="form-label">نوع الحساب <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user-tag"></i></span>
                            <select class="form-select" id="role_id" name="role_id" required>
                                <option value="">اختر نوع الحساب</option>
                                <?php foreach ($roles as $role): ?>
                                    <option value="<?php echo $role['role_id']; ?>">
                                        <?php
                                        $role_display = '';
                                        switch ($role['role_name']) {
                                            case 'parent':
                                                $role_display = 'ولي أمر';
                                                break;
                                            case 'student':
                                                $role_display = 'طالب';
                                                break;
                                            case 'teacher':
                                                $role_display = 'معلم';
                                                break;
                                            default:
                                                $role_display = $role['role_name'];
                                        }
                                        echo $role_display;
                                        ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3" id="center-field">
                        <label for="center_id" class="form-label">المركز</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                            <select class="form-select" id="center_id" name="center_id">
                                <option value="">اختر المركز</option>
                                <?php foreach ($centers as $center): ?>
                                    <option value="<?php echo $center['center_id']; ?>">
                                        <?php echo $center['center_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <small class="text-muted">مطلوب للمعلمين والطلاب</small>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                        <label class="form-check-label" for="terms">أوافق على <a href="#">الشروط والأحكام</a></label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">إنشاء حساب</button>
                    </div>
                </form>
            <?php endif; ?>

            <div class="form-footer">
                <p>لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a></p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>

    <script>
        $(document).ready(function() {
            // Show/hide center field based on role selection
            $('#role_id').on('change', function() {
                var selectedRole = $(this).find('option:selected').text();
                if (selectedRole === 'معلم' || selectedRole === 'طالب') {
                    $('#center-field').show();
                    $('#center_id').prop('required', true);
                } else {
                    $('#center-field').hide();
                    $('#center_id').prop('required', false);
                }
            });
        });
    </script>
</body>
</html>
