<?php
/**
 * System Logger Helper
 * Provides functions for logging system activities and events
 */

// Ensure database connection is available
if (!isset($pdo)) {
    require_once dirname(__FILE__) . '/db_connect.php';
}

/**
 * Log system activity
 *
 * @param string $category Category of the log (e.g., 'USER', 'SYSTEM', 'AUTH', 'WHATSAPP')
 * @param string $message Log message
 * @param string $level Log level (INFO, WARNING, ERROR, DEBUG, CRITICAL)
 * @param int|null $user_id User ID if applicable
 * @param array|null $additional_data Additional data to store as JSON
 * @return bool Success status
 */
function log_system_activity($category, $message, $level = 'INFO', $user_id = null, $additional_data = null) {
    global $pdo;

    try {
        // Create table if it doesn't exist
        create_system_logs_table();

        $stmt = $pdo->prepare("
            INSERT INTO system_logs (log_category, message, log_level, user_id, ip_address, user_agent, additional_data)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        $additional_data_json = $additional_data ? json_encode($additional_data, JSON_UNESCAPED_UNICODE) : null;

        return $stmt->execute([
            strtoupper($category),
            $message,
            strtoupper($level),
            $user_id,
            $ip_address,
            $user_agent,
            $additional_data_json
        ]);
    } catch (PDOException $e) {
        // Silently fail to avoid infinite loops
        error_log('Failed to log system activity: ' . $e->getMessage());
        return false;
    }
}

/**
 * Create system logs table if it doesn't exist
 */
function create_system_logs_table() {
    global $pdo;

    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS system_logs (
                log_id INT AUTO_INCREMENT PRIMARY KEY,
                log_level ENUM('INFO', 'WARNING', 'ERROR', 'DEBUG', 'CRITICAL') NOT NULL DEFAULT 'INFO',
                log_category VARCHAR(50) NOT NULL DEFAULT 'GENERAL',
                message TEXT NOT NULL,
                user_id INT NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                additional_data JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
                INDEX idx_log_level (log_level),
                INDEX idx_log_category (log_category),
                INDEX idx_created_at (created_at),
                INDEX idx_user_id (user_id)
            )
        ");
    } catch (PDOException $e) {
        error_log('Failed to create system_logs table: ' . $e->getMessage());
    }
}

/**
 * Log user authentication events
 */
function log_auth_event($event_type, $username, $success = true, $additional_info = null) {
    $user_id = null;
    if ($success && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }

    $level = $success ? 'INFO' : 'WARNING';
    $message = $success ?
        "تسجيل دخول ناجح للمستخدم: {$username}" :
        "محاولة تسجيل دخول فاشلة للمستخدم: {$username}";

    if ($event_type === 'logout') {
        $message = "تسجيل خروج للمستخدم: {$username}";
        $level = 'INFO';
    }

    log_system_activity('AUTH', $message, $level, $user_id, $additional_info);
}

/**
 * Log user management events
 */
function log_user_event($action, $target_user_id, $target_username, $additional_info = null) {
    $actions_map = [
        'create' => 'إنشاء مستخدم جديد',
        'update' => 'تحديث بيانات المستخدم',
        'delete' => 'حذف المستخدم',
        'activate' => 'تفعيل المستخدم',
        'deactivate' => 'إلغاء تفعيل المستخدم',
        'password_reset' => 'إعادة تعيين كلمة المرور'
    ];

    $action_text = $actions_map[$action] ?? $action;
    $message = "{$action_text}: {$target_username}";

    $level = in_array($action, ['delete', 'deactivate']) ? 'WARNING' : 'INFO';

    log_system_activity('USER', $message, $level, $_SESSION['user_id'] ?? null, array_merge([
        'target_user_id' => $target_user_id,
        'action' => $action
    ], $additional_info ?? []));
}

/**
 * Log center management events
 */
function log_center_event($action, $center_id, $center_name, $additional_info = null) {
    $actions_map = [
        'create' => 'إنشاء مركز جديد',
        'update' => 'تحديث بيانات المركز',
        'delete' => 'حذف المركز',
        'activate' => 'تفعيل المركز',
        'deactivate' => 'إلغاء تفعيل المركز'
    ];

    $action_text = $actions_map[$action] ?? $action;
    $message = "{$action_text}: {$center_name}";

    $level = in_array($action, ['delete', 'deactivate']) ? 'WARNING' : 'INFO';

    log_system_activity('CENTER', $message, $level, $_SESSION['user_id'] ?? null, array_merge([
        'center_id' => $center_id,
        'action' => $action
    ], $additional_info ?? []));
}

/**
 * Log circle management events
 */
function log_circle_event($action, $circle_id, $circle_name, $additional_info = null) {
    $actions_map = [
        'create' => 'إنشاء حلقة جديدة',
        'update' => 'تحديث بيانات الحلقة',
        'delete' => 'حذف الحلقة',
        'activate' => 'تفعيل الحلقة',
        'deactivate' => 'إلغاء تفعيل الحلقة'
    ];

    $action_text = $actions_map[$action] ?? $action;
    $message = "{$action_text}: {$circle_name}";

    $level = in_array($action, ['delete', 'deactivate']) ? 'WARNING' : 'INFO';

    log_system_activity('CIRCLE', $message, $level, $_SESSION['user_id'] ?? null, array_merge([
        'circle_id' => $circle_id,
        'action' => $action
    ], $additional_info ?? []));
}

/**
 * Log attendance events
 */
function log_attendance_event($student_name, $circle_name, $status, $date, $additional_info = null) {
    $status_map = [
        'present' => 'حاضر',
        'absent_excused' => 'غائب بعذر',
        'absent_unexcused' => 'غائب بدون عذر',
        'late' => 'متأخر'
    ];

    $status_text = $status_map[$status] ?? $status;
    $message = "تسجيل حضور: {$student_name} - {$circle_name} - {$status_text} - {$date}";

    $level = in_array($status, ['absent_unexcused', 'late']) ? 'WARNING' : 'INFO';

    log_system_activity('ATTENDANCE', $message, $level, $_SESSION['user_id'] ?? null, $additional_info);
}

/**
 * Log WhatsApp events
 */
function log_whatsapp_event($event_type, $recipient, $message_type, $success = true, $error_message = null) {
    $level = $success ? 'INFO' : 'ERROR';
    $status = $success ? 'نجح' : 'فشل';

    $message = "إرسال رسالة واتساب {$status}: {$message_type} إلى {$recipient}";

    $additional_data = [
        'event_type' => $event_type,
        'recipient' => $recipient,
        'message_type' => $message_type,
        'success' => $success
    ];

    if (!$success && $error_message) {
        $additional_data['error'] = $error_message;
        $message .= " - خطأ: {$error_message}";
    }

    log_system_activity('WHATSAPP', $message, $level, $_SESSION['user_id'] ?? null, $additional_data);
}

/**
 * Log system configuration changes
 */
function log_config_event($setting_key, $old_value, $new_value, $additional_info = null) {
    $message = "تغيير إعداد النظام: {$setting_key}";

    $additional_data = [
        'setting_key' => $setting_key,
        'old_value' => $old_value,
        'new_value' => $new_value
    ];

    if ($additional_info) {
        $additional_data = array_merge($additional_data, $additional_info);
    }

    log_system_activity('CONFIG', $message, 'INFO', $_SESSION['user_id'] ?? null, $additional_data);
}

/**
 * Log database events
 */
function log_database_event($action, $table_name, $record_id = null, $additional_info = null) {
    $actions_map = [
        'create' => 'إنشاء سجل جديد',
        'update' => 'تحديث سجل',
        'delete' => 'حذف سجل',
        'backup' => 'نسخ احتياطي',
        'restore' => 'استعادة من نسخة احتياطية'
    ];

    $action_text = $actions_map[$action] ?? $action;
    $message = "{$action_text} في جدول: {$table_name}";

    if ($record_id) {
        $message .= " (ID: {$record_id})";
    }

    $level = in_array($action, ['delete', 'restore']) ? 'WARNING' : 'INFO';

    log_system_activity('DATABASE', $message, $level, $_SESSION['user_id'] ?? null, array_merge([
        'table_name' => $table_name,
        'action' => $action,
        'record_id' => $record_id
    ], $additional_info ?? []));
}

/**
 * Log security events
 */
function log_security_event($event_type, $description, $severity = 'WARNING', $additional_info = null) {
    $message = "حدث أمني: {$event_type} - {$description}";

    log_system_activity('SECURITY', $message, strtoupper($severity), $_SESSION['user_id'] ?? null, $additional_info);
}

/**
 * Log system errors
 */
function log_system_error($error_message, $file = null, $line = null, $additional_info = null) {
    $message = "خطأ في النظام: {$error_message}";

    $additional_data = $additional_info ?? [];
    if ($file) {
        $additional_data['file'] = $file;
    }
    if ($line) {
        $additional_data['line'] = $line;
    }

    log_system_activity('SYSTEM', $message, 'ERROR', $_SESSION['user_id'] ?? null, $additional_data);
}

/**
 * Get recent system logs
 *
 * @param int $limit Number of logs to retrieve
 * @param string $level Filter by log level
 * @param string $category Filter by category
 * @return array Array of log entries
 */
function get_recent_logs($limit = 50, $level = null, $category = null) {
    global $pdo;

    try {
        $where_conditions = [];
        $params = [];

        if ($level) {
            $where_conditions[] = "log_level = ?";
            $params[] = strtoupper($level);
        }

        if ($category) {
            $where_conditions[] = "log_category = ?";
            $params[] = strtoupper($category);
        }

        $where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);

        $query = "
            SELECT sl.*, u.full_name as user_name
            FROM system_logs sl
            LEFT JOIN users u ON sl.user_id = u.user_id
            {$where_clause}
            ORDER BY sl.created_at DESC
            LIMIT {$limit}
        ";

        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log('Failed to get recent logs: ' . $e->getMessage());
        return [];
    }
}

/**
 * Clean old logs
 *
 * @param int $days Number of days to keep
 * @return int Number of deleted records
 */
function clean_old_logs($days = 90) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
        $stmt->execute([$days]);

        $deleted_count = $stmt->rowCount();

        // Log this cleanup action
        log_system_activity('SYSTEM', "تم حذف {$deleted_count} سجل أقدم من {$days} يوم", 'INFO');

        return $deleted_count;
    } catch (PDOException $e) {
        error_log('Failed to clean old logs: ' . $e->getMessage());
        return 0;
    }
}
?>
