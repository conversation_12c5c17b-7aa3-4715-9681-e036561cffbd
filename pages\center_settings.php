<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has center_admin role
if (!is_logged_in() || !has_role('center_admin')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Page variables
$page_title = 'إعدادات المركز';
$active_page = 'settings';
$success = '';
$error = '';

// Get center information
$center_id = $_SESSION['center_id'];

try {
    $stmt = $pdo->prepare("
        SELECT * FROM centers WHERE center_id = ?
    ");
    $stmt->execute([$center_id]);
    $center = $stmt->fetch();

    if (!$center) {
        $error = 'لم يتم العثور على بيانات المركز';
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المركز: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $center_name = sanitize_input($_POST['center_name']);
    $address = sanitize_input($_POST['address']);
    $contact_person_name = sanitize_input($_POST['contact_person_name']);
    $contact_email = sanitize_input($_POST['contact_email']);
    $contact_phone = sanitize_input($_POST['contact_phone']);
    $description = sanitize_input($_POST['description']);

    // Validate required fields
    if (empty($center_name)) {
        $error = 'يرجى إدخال اسم المركز';
    } elseif (empty($address)) {
        $error = 'يرجى إدخال عنوان المركز';
    } elseif (empty($contact_person_name)) {
        $error = 'يرجى إدخال اسم مسؤول الاتصال';
    } elseif (empty($contact_email)) {
        $error = 'يرجى إدخال البريد الإلكتروني لمسؤول الاتصال';
    } elseif (empty($contact_phone)) {
        $error = 'يرجى إدخال رقم هاتف مسؤول الاتصال';
    } else {
        try {
            // Begin transaction
            $pdo->beginTransaction();

            // Update center information
            $stmt = $pdo->prepare("
                UPDATE centers
                SET center_name = ?, address = ?, contact_person_name = ?,
                    contact_email = ?, contact_phone = ?, description = ?
                WHERE center_id = ?
            ");
            $stmt->execute([
                $center_name, $address, $contact_person_name,
                $contact_email, $contact_phone, $description,
                $center_id
            ]);

            // Handle logo upload if provided
            if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../assets/images/logos/';

                // Create directory if it doesn't exist
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_extension = pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION);
                $new_filename = 'center_' . $center_id . '.' . $file_extension;
                $upload_path = $upload_dir . $new_filename;

                if (move_uploaded_file($_FILES['logo']['tmp_name'], $upload_path)) {
                    // Update center with logo URL
                    $logo_url = 'assets/images/logos/' . $new_filename;
                    $stmt = $pdo->prepare("UPDATE centers SET logo_url = ? WHERE center_id = ?");
                    $stmt->execute([$logo_url, $center_id]);
                }
            }

            // Commit transaction
            $pdo->commit();

            $success = 'تم تحديث بيانات المركز بنجاح';

            // Refresh center data
            $stmt = $pdo->prepare("SELECT * FROM centers WHERE center_id = ?");
            $stmt->execute([$center_id]);
            $center = $stmt->fetch();
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء تحديث بيانات المركز: ' . $e->getMessage();
        }
    }
}

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => get_root_url() . 'pages/center_admin_dashboard.php'],
    ['title' => 'إعدادات المركز']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-cogs'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<?php if (isset($center)): ?>
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-edit me-2"></i> تعديل بيانات المركز</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="center_name" class="form-label">اسم المركز <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="center_name" name="center_name" required value="<?php echo isset($center['center_name']) ? $center['center_name'] : ''; ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="logo" class="form-label">شعار المركز</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="2" required><?php echo isset($center['address']) ? $center['address'] : ''; ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="contact_person_name" class="form-label">اسم مسؤول الاتصال <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="contact_person_name" name="contact_person_name" required value="<?php echo isset($center['contact_person_name']) ? $center['contact_person_name'] : ''; ?>">
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="contact_email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" required value="<?php echo isset($center['contact_email']) ? $center['contact_email'] : ''; ?>">
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="contact_phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone" required value="<?php echo isset($center['contact_phone']) ? $center['contact_phone'] : ''; ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المركز</label>
                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo isset($center['description']) ? $center['description'] : ''; ?></textarea>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo get_root_url(); ?>pages/center_admin_dashboard.php" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> معلومات المركز</h5>
                </div>
                <div class="card-body text-center">
                    <?php if (!empty($center['logo_url'])): ?>
                        <img src="<?php echo '../' . $center['logo_url']; ?>" alt="شعار المركز" class="img-fluid mb-3" style="max-height: 150px;">
                    <?php else: ?>
                        <div class="mb-3">
                            <i class="fas fa-building fa-5x text-muted"></i>
                        </div>
                    <?php endif; ?>

                    <h4><?php echo isset($center['center_name']) ? $center['center_name'] : 'المركز'; ?></h4>
                    <p class="text-muted"><?php echo isset($center['address']) ? $center['address'] : ''; ?></p>

                    <hr>

                    <div class="text-start">
                        <p><strong>تاريخ الإنشاء:</strong> <?php echo isset($center['created_at']) ? date('Y-m-d', strtotime($center['created_at'])) : 'غير محدد'; ?></p>
                        <p><strong>الحالة:</strong>
                            <?php if (isset($center['is_active']) && $center['is_active']): ?>
                                <span class="badge bg-success">نشط</span>
                            <?php else: ?>
                                <span class="badge bg-danger">غير نشط</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0"><i class="fas fa-lightbulb me-2"></i> نصائح</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check-circle text-success me-2"></i> قم بتحديث بيانات المركز بانتظام للحفاظ على دقة المعلومات.</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i> استخدم شعارًا واضحًا وبجودة عالية لتمثيل المركز.</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i> تأكد من صحة بيانات الاتصال لتسهيل التواصل مع المركز.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php
// Include footer template
include_template('footer');
?>
