<?php
/**
 * Activity Logger - نظام تسجيل الأنشطة الشامل
 */

/**
 * تسجيل نشاط في قاعدة البيانات
 */
function log_activity($pdo, $category, $action, $description, $user_id = null, $additional_data = null) {
    try {
        // إنشاء جدول الأنشطة إذا لم يكن موجوداً
        create_activity_logs_table($pdo);
        
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (
                category, action, description, user_id, ip_address, user_agent, 
                additional_data, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $category,
            $action,
            $description,
            $user_id,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null,
            $additional_data ? json_encode($additional_data) : null
        ]);
        
        return true;
    } catch (PDOException $e) {
        error_log("Activity logging failed: " . $e->getMessage());
        return false;
    }
}

/**
 * إنشاء جدول سجلات الأنشطة
 */
function create_activity_logs_table($pdo) {
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS activity_logs (
                log_id INT AUTO_INCREMENT PRIMARY KEY,
                category VARCHAR(50) NOT NULL,
                action VARCHAR(100) NOT NULL,
                description TEXT NOT NULL,
                user_id INT NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                additional_data JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
                INDEX idx_category (category),
                INDEX idx_action (action),
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            )
        ");
        return true;
    } catch (PDOException $e) {
        error_log("Failed to create activity_logs table: " . $e->getMessage());
        return false;
    }
}

/**
 * تسجيل نشاط تسجيل الدخول
 */
function log_login_activity($pdo, $user_id, $username, $success = true, $error_message = null) {
    $action = $success ? 'login_success' : 'login_failed';
    $description = $success 
        ? "تسجيل دخول ناجح للمستخدم: {$username}"
        : "محاولة تسجيل دخول فاشلة للمستخدم: {$username}" . ($error_message ? " - {$error_message}" : "");
    
    $additional_data = [
        'username' => $username,
        'success' => $success,
        'error_message' => $error_message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'AUTH', $action, $description, $success ? $user_id : null, $additional_data);
}

/**
 * تسجيل نشاط تسجيل الخروج
 */
function log_logout_activity($pdo, $user_id, $username) {
    $description = "تسجيل خروج للمستخدم: {$username}";
    
    $additional_data = [
        'username' => $username,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'AUTH', 'logout', $description, $user_id, $additional_data);
}

/**
 * تسجيل نشاط إنشاء مستخدم
 */
function log_user_creation($pdo, $created_user_id, $created_username, $creator_user_id, $role_name) {
    $description = "تم إنشاء مستخدم جديد: {$created_username} بدور {$role_name}";
    
    $additional_data = [
        'created_user_id' => $created_user_id,
        'created_username' => $created_username,
        'role_name' => $role_name,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'USER', 'user_created', $description, $creator_user_id, $additional_data);
}

/**
 * تسجيل نشاط تحديث مستخدم
 */
function log_user_update($pdo, $updated_user_id, $updated_username, $updater_user_id, $changes) {
    $description = "تم تحديث بيانات المستخدم: {$updated_username}";
    
    $additional_data = [
        'updated_user_id' => $updated_user_id,
        'updated_username' => $updated_username,
        'changes' => $changes,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'USER', 'user_updated', $description, $updater_user_id, $additional_data);
}

/**
 * تسجيل نشاط الحضور
 */
function log_attendance_activity($pdo, $student_name, $status, $teacher_user_id, $date) {
    $status_text = [
        'present' => 'حاضر',
        'absent_excused' => 'غائب بعذر',
        'absent_unexcused' => 'غائب بدون عذر',
        'late' => 'متأخر'
    ];
    
    $description = "تسجيل حضور: {$student_name} - " . ($status_text[$status] ?? $status) . " - {$date}";
    
    $additional_data = [
        'student_name' => $student_name,
        'status' => $status,
        'date' => $date,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'ATTENDANCE', 'attendance_recorded', $description, $teacher_user_id, $additional_data);
}

/**
 * تسجيل نشاط تقييم الحفظ
 */
function log_memorization_activity($pdo, $student_name, $surah_name, $ayah_from, $ayah_to, $quality, $teacher_user_id) {
    $description = "تقييم حفظ: {$student_name} - سورة {$surah_name} (آية {$ayah_from} إلى {$ayah_to}) - {$quality}";
    
    $additional_data = [
        'student_name' => $student_name,
        'surah_name' => $surah_name,
        'ayah_from' => $ayah_from,
        'ayah_to' => $ayah_to,
        'quality' => $quality,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'MEMORIZATION', 'memorization_evaluated', $description, $teacher_user_id, $additional_data);
}

/**
 * تسجيل نشاط إنشاء إعلان
 */
function log_announcement_activity($pdo, $title, $target_role, $creator_user_id) {
    $description = "تم إنشاء إعلان: {$title} (مستهدف: {$target_role})";
    
    $additional_data = [
        'title' => $title,
        'target_role' => $target_role,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'ANNOUNCEMENT', 'announcement_created', $description, $creator_user_id, $additional_data);
}

/**
 * تسجيل نشاط رسالة واتساب
 */
function log_whatsapp_activity($pdo, $recipient_number, $message_type, $status, $sender_user_id) {
    $description = "رسالة واتساب: {$message_type} إلى {$recipient_number} - {$status}";
    
    $additional_data = [
        'recipient_number' => $recipient_number,
        'message_type' => $message_type,
        'status' => $status,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'WHATSAPP', 'message_sent', $description, $sender_user_id, $additional_data);
}

/**
 * تسجيل نشاط تسجيل في الحلقات
 */
function log_enrollment_activity($pdo, $student_name, $circle_name, $enrolling_user_id) {
    $description = "تسجيل طالب: {$student_name} في حلقة {$circle_name}";
    
    $additional_data = [
        'student_name' => $student_name,
        'circle_name' => $circle_name,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    return log_activity($pdo, 'ENROLLMENT', 'student_enrolled', $description, $enrolling_user_id, $additional_data);
}

/**
 * تسجيل نشاط عام
 */
function log_general_activity($pdo, $category, $action, $description, $user_id = null, $additional_data = null) {
    return log_activity($pdo, $category, $action, $description, $user_id, $additional_data);
}

/**
 * الحصول على آخر الأنشطة
 */
function get_recent_activities($pdo, $limit = 50, $user_id = null, $category = null, $days = 7) {
    try {
        $where_conditions = ["al.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)"];
        $params = [$days];
        
        if ($user_id) {
            $where_conditions[] = "al.user_id = ?";
            $params[] = $user_id;
        }
        
        if ($category) {
            $where_conditions[] = "al.category = ?";
            $params[] = $category;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $stmt = $pdo->prepare("
            SELECT 
                al.log_id,
                al.category,
                al.action,
                al.description,
                al.user_id,
                al.ip_address,
                al.user_agent,
                al.additional_data,
                al.created_at,
                u.full_name as user_name,
                u.username
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.user_id
            WHERE {$where_clause}
            ORDER BY al.created_at DESC
            LIMIT ?
        ");
        
        $params[] = $limit;
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Failed to get recent activities: " . $e->getMessage());
        return [];
    }
}
?>
