    <main class="container py-4">
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>أبنائي</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addChildModal">
                <i class="fas fa-plus me-1"></i> إضافة ابن
            </button>
        </div>
        
        <!-- Children List -->
        <div class="row">
            <?php if (empty($children)): ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا يوجد أبناء مسجلين حالياً. يمكنك إضافة ابن من خلال الزر أعلاه.
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($children as $child): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 <?php echo $child['is_primary'] ? 'border-primary' : ''; ?>">
                            <?php if ($child['is_primary']): ?>
                                <div class="card-header bg-primary text-white">
                                    <span><i class="fas fa-star me-1"></i> الابن الرئيسي</span>
                                </div>
                            <?php endif; ?>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <img src="<?php echo !empty($child['profile_picture_url']) ? '../' . $child['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>" 
                                         class="rounded-circle" width="100" height="100" alt="صورة الطالب">
                                    <h5 class="card-title mt-2"><?php echo $child['full_name']; ?></h5>
                                    <span class="badge bg-info"><?php echo $child['relation_type']; ?></span>
                                </div>
                                
                                <div class="mb-3">
                                    <?php if (!empty($child['circle_name'])): ?>
                                        <p class="mb-1"><strong>الحلقة:</strong> <?php echo $child['circle_name']; ?></p>
                                        <p class="mb-1"><strong>المستوى:</strong> <?php echo $child['level']; ?></p>
                                        <p class="mb-1"><strong>المعلم:</strong> <?php echo $child['teacher_name']; ?></p>
                                        <p class="mb-1"><strong>المركز:</strong> <?php echo $child['center_name']; ?></p>
                                    <?php else: ?>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i> هذا الطالب غير مسجل في أي حلقة حالياً.
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="student_details.php?id=<?php echo $child['user_id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-eye me-1"></i> عرض التفاصيل
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#removeChildModal" 
                                            data-relation-id="<?php echo $child['relation_id']; ?>"
                                            data-student-name="<?php echo $child['full_name']; ?>">
                                        <i class="fas fa-trash-alt me-1"></i> إزالة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>
