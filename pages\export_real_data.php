<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'غير مصرح لك بالوصول إلى هذه البيانات'], JSON_UNESCAPED_UNICODE);
    exit;
}

$format = $_GET['format'] ?? 'json';

try {
    $data = [];
    
    // 1. المستخدمين
    $stmt = $pdo->query("
        SELECT u.user_id, u.username, u.full_name, u.email, u.phone_number, 
               r.role_name, c.center_name, u.is_active, u.created_at
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.role_id 
        LEFT JOIN centers c ON u.center_id = c.center_id 
        ORDER BY u.created_at DESC
    ");
    $data['users'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 2. الأدوار
    $stmt = $pdo->query("SELECT * FROM roles ORDER BY role_id");
    $data['roles'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 3. المراكز
    $stmt = $pdo->query("SELECT * FROM centers ORDER BY center_id");
    $data['centers'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 4. الحلقات
    $stmt = $pdo->query("
        SELECT c.circle_id, c.circle_name, c.description, c.max_students, c.is_active,
               u.full_name as teacher_name, cent.center_name
        FROM circles c 
        LEFT JOIN users u ON c.teacher_user_id = u.user_id 
        LEFT JOIN centers cent ON c.center_id = cent.center_id 
        ORDER BY c.circle_id
    ");
    $data['circles'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 5. تسجيلات الطلاب
    $stmt = $pdo->query("
        SELECT sce.enrollment_id, sce.enrollment_date, sce.is_active,
               s.full_name as student_name, c.circle_name, p.full_name as parent_name
        FROM student_circle_enrollments sce 
        JOIN users s ON sce.student_user_id = s.user_id 
        JOIN circles c ON sce.circle_id = c.circle_id 
        LEFT JOIN users p ON sce.parent_user_id = p.user_id 
        ORDER BY sce.enrollment_date DESC
        LIMIT 100
    ");
    $data['enrollments'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 6. الجداول الاختيارية
    $optional_tables = ['announcements', 'attendance_records', 'memorization_progress', 'whatsapp_logs', 'activity_logs', 'system_logs'];
    
    foreach ($optional_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            try {
                $date_column = 'created_at';
                if ($table == 'attendance_records') $date_column = 'session_date';
                if ($table == 'memorization_progress') $date_column = 'recitation_date';
                
                $stmt = $pdo->query("SELECT * FROM $table ORDER BY $date_column DESC LIMIT 50");
                $data[$table] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                $data[$table] = ['error' => $e->getMessage()];
            }
        } else {
            $data[$table] = ['status' => 'table_not_exists'];
        }
    }
    
    // 7. إحصائيات
    $data['statistics'] = [];
    
    // عدد المستخدمين النشطين
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1");
    $data['statistics']['active_users'] = (int)$stmt->fetchColumn();
    
    // عدد المستخدمين حسب الدور
    $stmt = $pdo->query("
        SELECT r.role_name, COUNT(*) as count 
        FROM users u 
        JOIN roles r ON u.role_id = r.role_id 
        WHERE u.is_active = 1 
        GROUP BY r.role_name
    ");
    $data['statistics']['users_by_role'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // عدد الحلقات النشطة
    $stmt = $pdo->query("SELECT COUNT(*) FROM circles WHERE is_active = 1");
    $data['statistics']['active_circles'] = (int)$stmt->fetchColumn();
    
    // عدد التسجيلات النشطة
    $stmt = $pdo->query("SELECT COUNT(*) FROM student_circle_enrollments WHERE is_active = 1");
    $data['statistics']['active_enrollments'] = (int)$stmt->fetchColumn();
    
    // آخر الأنشطة
    foreach ($optional_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $data['statistics'][$table . '_count'] = (int)$stmt->fetchColumn();
            } catch (PDOException $e) {
                $data['statistics'][$table . '_count'] = 0;
            }
        }
    }
    
    // معلومات إضافية
    $data['metadata'] = [
        'export_time' => date('Y-m-d H:i:s'),
        'database_name' => 'quran_circle_management',
        'exported_by' => $_SESSION['username'] ?? 'unknown',
        'total_tables_checked' => count($optional_tables) + 5,
        'format' => $format
    ];

    if ($format === 'csv') {
        // تصدير CSV للمستخدمين فقط كمثال
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="users_data_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // إضافة BOM للدعم العربي
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // عناوين الأعمدة
        fputcsv($output, ['ID', 'اسم المستخدم', 'الاسم الكامل', 'البريد الإلكتروني', 'الهاتف', 'الدور', 'المركز', 'نشط', 'تاريخ الإنشاء']);
        
        // البيانات
        foreach ($data['users'] as $user) {
            fputcsv($output, [
                $user['user_id'],
                $user['username'],
                $user['full_name'],
                $user['email'],
                $user['phone_number'] ?? '',
                $user['role_name'] ?? '',
                $user['center_name'] ?? '',
                $user['is_active'] ? 'نعم' : 'لا',
                $user['created_at']
            ]);
        }
        
        fclose($output);
        exit;
    } else {
        // تصدير JSON
        header('Content-Type: application/json; charset=utf-8');
        if (isset($_GET['download'])) {
            header('Content-Disposition: attachment; filename="database_export_' . date('Y-m-d') . '.json"');
        }
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'خطأ في قاعدة البيانات',
        'message' => $e->getMessage(),
        'time' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'خطأ عام',
        'message' => $e->getMessage(),
        'time' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
