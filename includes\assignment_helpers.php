<?php
// Helper functions for assignment queries that handle missing is_active column

function get_assignments_for_circle($pdo, $circle_id, $student_id = null) {
    // Check if is_active column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'assignments' 
        AND column_name = 'is_active'
    ");
    $stmt->execute();
    $is_active_exists = (bool)$stmt->fetchColumn();
    
    $where_clause = $is_active_exists ? "AND a.is_active = TRUE" : "";
    
    if ($student_id) {
        // Get assignments with student data
        $stmt = $pdo->prepare("
            SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                   sa.status, sa.submission_date, sa.grade, sa.feedback, sa.submission_text,
                   u.full_name AS created_by_name
            FROM assignments a
            LEFT JOIN student_assignments sa ON a.assignment_id = sa.assignment_id AND sa.student_user_id = ?
            JOIN users u ON a.created_by_user_id = u.user_id
            WHERE a.circle_id = ? $where_clause
            ORDER BY a.due_date DESC
        ");
        $stmt->execute([$student_id, $circle_id]);
    } else {
        // Get assignments without student data
        $stmt = $pdo->prepare("
            SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                   u.full_name AS created_by_name
            FROM assignments a
            JOIN users u ON a.created_by_user_id = u.user_id
            WHERE a.circle_id = ? $where_clause
            ORDER BY a.due_date DESC
        ");
        $stmt->execute([$circle_id]);
    }
    
    return $stmt->fetchAll();
}

function get_assignment_by_id($pdo, $assignment_id) {
    // Check if is_active column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.columns 
        WHERE table_schema = DATABASE() 
        AND table_name = 'assignments' 
        AND column_name = 'is_active'
    ");
    $stmt->execute();
    $is_active_exists = (bool)$stmt->fetchColumn();
    
    $where_clause = $is_active_exists ? "AND a.is_active = TRUE" : "";
    
    $stmt = $pdo->prepare("
        SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
               c.circle_name, c.level,
               u.full_name AS created_by_name,
               cen.center_name
        FROM assignments a
        JOIN circles c ON a.circle_id = c.circle_id
        JOIN users u ON a.created_by_user_id = u.user_id
        JOIN centers cen ON c.center_id = cen.center_id
        WHERE a.assignment_id = ? $where_clause
    ");
    $stmt->execute([$assignment_id]);
    
    return $stmt->fetch();
}
?>