<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/announcement_stats.php';

// Check if user is logged in and has system_owner or center_admin role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';
$is_new = isset($_GET['new']) && $_GET['new'] == 1;
$announcement_id = isset($_GET['id']) ? (int)$_GET['id'] : null;

// Create announcements table if not exists
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS announcements (
            announcement_id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            sender_user_id INT NOT NULL,
            target_role VARCHAR(50) DEFAULT 'all',
            target_center_id INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            media_type VARCHAR(20) DEFAULT 'none',
            media_url VARCHAR(255) NULL,
            background_color VARCHAR(20) DEFAULT '#ffffff',
            text_color VARCHAR(20) DEFAULT '#000000',
            start_date DATE DEFAULT NULL,
            end_date DATE DEFAULT NULL,
            is_public BOOLEAN DEFAULT FALSE,
            is_featured BOOLEAN DEFAULT FALSE,
            FOREIGN KEY (sender_user_id) REFERENCES users(user_id),
            FOREIGN KEY (target_center_id) REFERENCES centers(center_id)
        )
    ");

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS announcement_reads (
            read_id INT AUTO_INCREMENT PRIMARY KEY,
            announcement_id INT NOT NULL,
            user_id INT NOT NULL,
            read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id),
            FOREIGN KEY (user_id) REFERENCES users(user_id),
            UNIQUE KEY (announcement_id, user_id)
        )
    ");

    // Create center_admins table if not exists
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS center_admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            center_id INT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id),
            FOREIGN KEY (center_id) REFERENCES centers(center_id),
            UNIQUE KEY (user_id, center_id)
        )
    ");
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء إنشاء جداول الإعلانات: ' . $e->getMessage();
}

// Get roles for dropdown
try {
    $stmt = $pdo->prepare("SELECT role_id, role_name FROM roles ORDER BY role_id");
    $stmt->execute();
    $roles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الأدوار: ' . $e->getMessage();
}

// Get centers for dropdown
try {
    // Si es administrador del sistema, mostrar todos los centros
    // Si es administrador de centro, mostrar solo su centro
    if (has_role('system_owner')) {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } else if (has_role('center_admin')) {
        // Verificar si existe la tabla center_admins
        $table_exists = false;
        $check_table = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'center_admins'
        ");
        $check_table->execute();
        $table_exists = ($check_table->fetchColumn() > 0);

        if ($table_exists) {
            // Obtener el centro del administrador actual
            $stmt = $pdo->prepare("
                SELECT c.center_id, c.center_name
                FROM centers c
                JOIN center_admins ca ON c.center_id = ca.center_id
                WHERE ca.user_id = ? AND c.is_active = TRUE
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $centers = $stmt->fetchAll();

            // Si el administrador no está asignado a ningún centro, asignar el primer centro disponible
            if (empty($centers)) {
                $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name LIMIT 1");
                $stmt->execute();
                $centers = $stmt->fetchAll();

                if (!empty($centers)) {
                    // Asignar automáticamente el administrador al primer centro
                    $insert_admin = $pdo->prepare("
                        INSERT INTO center_admins (user_id, center_id)
                        VALUES (?, ?)
                    ");
                    $insert_admin->execute([$_SESSION['user_id'], $centers[0]['center_id']]);
                }
            }
        } else {
            // Si la tabla no existe, mostrar todos los centros
            $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
            $stmt->execute();
            $centers = $stmt->fetchAll();
        }
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    // En caso de error, asegurarse de que $centers esté definido
    $centers = [];
}

// Get announcement details if editing
$announcement = null;
if ($announcement_id) {
    try {
        // Si es administrador del sistema, puede ver cualquier anuncio
        // Si es administrador de centro, solo puede ver anuncios de su centro
        if (has_role('system_owner')) {
            $stmt = $pdo->prepare("
                SELECT a.*, u.full_name AS sender_name, c.center_name
                FROM announcements a
                JOIN users u ON a.sender_user_id = u.user_id
                LEFT JOIN centers c ON a.target_center_id = c.center_id
                WHERE a.announcement_id = ?
            ");
            $stmt->execute([$announcement_id]);
        } else if (has_role('center_admin')) {
            // Verificar si existe la tabla center_admins
            $table_exists = false;
            $check_table = $pdo->prepare("
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
                AND table_name = 'center_admins'
            ");
            $check_table->execute();
            $table_exists = ($check_table->fetchColumn() > 0);

            if ($table_exists) {
                // Obtener el centro del administrador actual
                $center_stmt = $pdo->prepare("
                    SELECT center_id FROM center_admins WHERE user_id = ?
                ");
                $center_stmt->execute([$_SESSION['user_id']]);
                $admin_center = $center_stmt->fetch(PDO::FETCH_ASSOC);

                if (!$admin_center) {
                    // Si el administrador no está asignado a ningún centro, asignar el primer centro disponible
                    $center_stmt = $pdo->prepare("SELECT center_id FROM centers WHERE is_active = TRUE ORDER BY center_name LIMIT 1");
                    $center_stmt->execute();
                    $first_center = $center_stmt->fetch(PDO::FETCH_ASSOC);

                    if ($first_center) {
                        // Asignar automáticamente el administrador al primer centro
                        $insert_admin = $pdo->prepare("
                            INSERT INTO center_admins (user_id, center_id)
                            VALUES (?, ?)
                        ");
                        $insert_admin->execute([$_SESSION['user_id'], $first_center['center_id']]);
                        $admin_center = $first_center;
                    } else {
                        $error = 'لم يتم العثور على مراكز نشطة في النظام';
                        redirect('pages/announcements.php');
                    }
                }

                $stmt = $pdo->prepare("
                    SELECT a.*, u.full_name AS sender_name, c.center_name
                    FROM announcements a
                    JOIN users u ON a.sender_user_id = u.user_id
                    LEFT JOIN centers c ON a.target_center_id = c.center_id
                    WHERE a.announcement_id = ? AND (
                        a.sender_user_id = ? OR
                        (a.target_role = 'center_specific' AND a.target_center_id = ?) OR
                        a.target_role = 'all'
                    )
                ");
                $stmt->execute([
                    $announcement_id,
                    $_SESSION['user_id'],
                    $admin_center['center_id']
                ]);
            } else {
                // Si la tabla no existe, permitir ver cualquier anuncio
                $stmt = $pdo->prepare("
                    SELECT a.*, u.full_name AS sender_name, c.center_name
                    FROM announcements a
                    JOIN users u ON a.sender_user_id = u.user_id
                    LEFT JOIN centers c ON a.target_center_id = c.center_id
                    WHERE a.announcement_id = ?
                ");
                $stmt->execute([$announcement_id]);
            }
        }

        $announcement = $stmt->fetch();

        if (!$announcement) {
            set_flash_message('danger', 'الإعلان غير موجود أو ليس لديك صلاحية للوصول إليه');
            redirect('pages/announcements.php');
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الإعلان: ' . $e->getMessage();
    }
}

// Check if columns exist in the announcements table
$is_public_exists = column_exists($pdo, 'announcements', 'is_public');
$is_featured_exists = column_exists($pdo, 'announcements', 'is_featured');
$is_active_exists = column_exists($pdo, 'announcements', 'is_active');
$media_type_exists = column_exists($pdo, 'announcements', 'media_type');
$media_url_exists = column_exists($pdo, 'announcements', 'media_url');
$background_color_exists = column_exists($pdo, 'announcements', 'background_color');
$text_color_exists = column_exists($pdo, 'announcements', 'text_color');
$start_date_exists = column_exists($pdo, 'announcements', 'start_date');
$end_date_exists = column_exists($pdo, 'announcements', 'end_date');

// Add missing columns if they don't exist
try {
    if (!$is_public_exists) {
        $pdo->exec("ALTER TABLE announcements ADD COLUMN is_public BOOLEAN DEFAULT FALSE");
        $is_public_exists = true;
    }
    if (!$is_featured_exists) {
        $pdo->exec("ALTER TABLE announcements ADD COLUMN is_featured BOOLEAN DEFAULT FALSE");
        $is_featured_exists = true;
    }
    if (!$media_type_exists) {
        $pdo->exec("ALTER TABLE announcements ADD COLUMN media_type VARCHAR(20) DEFAULT 'none'");
        $media_type_exists = true;
    }
    if (!$media_url_exists) {
        $pdo->exec("ALTER TABLE announcements ADD COLUMN media_url VARCHAR(255) NULL");
        $media_url_exists = true;
    }
    if (!$background_color_exists) {
        $pdo->exec("ALTER TABLE announcements ADD COLUMN background_color VARCHAR(20) DEFAULT '#ffffff'");
        $background_color_exists = true;
    }
    if (!$text_color_exists) {
        $pdo->exec("ALTER TABLE announcements ADD COLUMN text_color VARCHAR(20) DEFAULT '#000000'");
        $text_color_exists = true;
    }
    if (!$start_date_exists) {
        $pdo->exec("ALTER TABLE announcements ADD COLUMN start_date DATE DEFAULT NULL");
        $start_date_exists = true;
    }
    if (!$end_date_exists) {
        $pdo->exec("ALTER TABLE announcements ADD COLUMN end_date DATE DEFAULT NULL");
        $end_date_exists = true;
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء تحديث جدول الإعلانات: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $title = sanitize_input($_POST['title']);
    $content = $_POST['content']; // Don't sanitize HTML content
    $target_role = sanitize_input($_POST['target_role']);

    // Validar que el rol objetivo sea válido para el tipo de usuario
    if (has_role('center_admin') && !array_key_exists($target_role, $center_admin_roles)) {
        // Si un administrador de centro intenta crear un anuncio para un rol no permitido,
        // forzar a que sea para profesores (un rol permitido)
        $target_role = 'teacher';
    }

    // Si es administrador de centro, asegurarse de que solo pueda crear anuncios para su centro
    if (has_role('center_admin')) {
        // Verificar si existe la tabla center_admins
        $table_exists = false;
        $check_table = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'center_admins'
        ");
        $check_table->execute();
        $table_exists = ($check_table->fetchColumn() > 0);

        if ($table_exists) {
            // Obtener el centro del administrador actual
            $center_stmt = $pdo->prepare("
                SELECT center_id FROM center_admins WHERE user_id = ?
            ");
            $center_stmt->execute([$_SESSION['user_id']]);
            $admin_center = $center_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$admin_center) {
                // Si el administrador no está asignado a ningún centro, asignar el primer centro disponible
                $center_stmt = $pdo->prepare("SELECT center_id FROM centers WHERE is_active = TRUE ORDER BY center_name LIMIT 1");
                $center_stmt->execute();
                $first_center = $center_stmt->fetch(PDO::FETCH_ASSOC);

                if ($first_center) {
                    // Asignar automáticamente el administrador al primer centro
                    $insert_admin = $pdo->prepare("
                        INSERT INTO center_admins (user_id, center_id)
                        VALUES (?, ?)
                    ");
                    $insert_admin->execute([$_SESSION['user_id'], $first_center['center_id']]);
                    $admin_center = $first_center;
                } else {
                    $error = 'لم يتم العثور على مراكز نشطة في النظام';
                }
            }

            if ($admin_center) {
                // Si el rol objetivo es center_specific, usar el centro del administrador
                if ($target_role == 'center_specific') {
                    $target_center_id = $admin_center['center_id'];
                } else {
                    $target_center_id = null;
                }
            }
        } else {
            // Si la tabla no existe, permitir seleccionar cualquier centro
            $target_center_id = ($target_role == 'center_specific') ? (int)$_POST['target_center_id'] : null;
        }
    } else {
        // Para el administrador del sistema, usar el centro seleccionado
        $target_center_id = ($target_role == 'center_specific') ? (int)$_POST['target_center_id'] : null;
    }
    $sender_user_id = $_SESSION['user_id'];
    // Procesar valores de checkboxes
    if (has_role('system_owner')) {
        // Solo el administrador del sistema puede crear anuncios públicos o destacados
        $is_public = ($is_public_exists && isset($_POST['is_public'])) ? 1 : 0;
        $is_featured = ($is_featured_exists && isset($_POST['is_featured'])) ? 1 : 0;
    } else {
        // Para administradores de centros, mantener los valores existentes en caso de edición
        if ($announcement && isset($announcement['is_public'])) {
            $is_public = $announcement['is_public'];
        } else {
            $is_public = 0;
        }

        if ($announcement && isset($announcement['is_featured'])) {
            $is_featured = $announcement['is_featured'];
        } else {
            $is_featured = 0;
        }
    }

    $is_active = ($is_active_exists && isset($_POST['is_active'])) ? 1 : 0;

    // Depuración
    error_log("is_public: " . $is_public . ", is_featured: " . $is_featured . ", is_active: " . $is_active);
    $background_color = $background_color_exists ? sanitize_input($_POST['background_color']) : '#ffffff';
    $text_color = $text_color_exists ? sanitize_input($_POST['text_color']) : '#000000';
    $start_date = $start_date_exists ? sanitize_input($_POST['start_date']) : date('Y-m-d');
    $end_date = $end_date_exists ? sanitize_input($_POST['end_date']) : date('Y-m-d', strtotime('+30 days'));
    $media_type = $media_type_exists ? sanitize_input($_POST['media_type']) : 'none';
    $media_url = $media_url_exists ? sanitize_input($_POST['media_url']) : '';

    // Handle media upload
    $uploaded_media_url = null;
    $upload_dir = '../assets/media/announcements/';

    // Create directory if it doesn't exist
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    if ($media_type === 'image' && isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
        $file_extension = pathinfo($_FILES['image_file']['name'], PATHINFO_EXTENSION);
        $new_filename = 'announcement_image_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;

        if (move_uploaded_file($_FILES['image_file']['tmp_name'], $upload_path)) {
            $uploaded_media_url = 'assets/media/announcements/' . $new_filename;
        } else {
            $error = 'حدث خطأ أثناء رفع الصورة';
        }
    } elseif ($media_type === 'video' && isset($_FILES['video_file']) && $_FILES['video_file']['error'] === UPLOAD_ERR_OK) {
        $file_extension = pathinfo($_FILES['video_file']['name'], PATHINFO_EXTENSION);
        $new_filename = 'announcement_video_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;

        if (move_uploaded_file($_FILES['video_file']['tmp_name'], $upload_path)) {
            $uploaded_media_url = 'assets/media/announcements/' . $new_filename;
        } else {
            $error = 'حدث خطأ أثناء رفع الفيديو';
        }
    } elseif ($media_type === 'audio' && isset($_FILES['audio_file']) && $_FILES['audio_file']['error'] === UPLOAD_ERR_OK) {
        $file_extension = pathinfo($_FILES['audio_file']['name'], PATHINFO_EXTENSION);
        $new_filename = 'announcement_audio_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;

        if (move_uploaded_file($_FILES['audio_file']['tmp_name'], $upload_path)) {
            $uploaded_media_url = 'assets/media/announcements/' . $new_filename;
        } else {
            $error = 'حدث خطأ أثناء رفع الملف الصوتي';
        }
    }

    // Determine final media URL
    $final_media_url = null;
    if (!empty($uploaded_media_url)) {
        $final_media_url = $uploaded_media_url;
    } elseif (!empty($media_url)) {
        $final_media_url = $media_url;
    } elseif ($announcement && !empty($announcement['media_url']) && $media_type === $announcement['media_type']) {
        $final_media_url = $announcement['media_url'];
    }

    // Validate required fields
    if (empty($title)) {
        $error = 'يرجى إدخال عنوان الإعلان';
    } elseif (empty($content)) {
        $error = 'يرجى إدخال محتوى الإعلان';
    } elseif ($target_role == 'center_specific' && empty($target_center_id)) {
        $error = 'يرجى اختيار المركز المستهدف';
    } elseif (empty($start_date)) {
        $error = 'يرجى تحديد تاريخ بداية الإعلان';
    } elseif (empty($end_date)) {
        $error = 'يرجى تحديد تاريخ نهاية الإعلان';
    } elseif (strtotime($end_date) < strtotime($start_date)) {
        $error = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
    } else {
        try {
            if ($announcement_id) {
                // Build dynamic update query based on existing columns
                $update_fields = [
                    "title = ?",
                    "content = ?",
                    "target_role = ?",
                    "target_center_id = ?",
                    "updated_at = NOW()"
                ];
                $params = [
                    $title,
                    $content,
                    $target_role,
                    $target_center_id
                ];

                // Add optional fields if they exist
                if ($is_public_exists) {
                    $update_fields[] = "is_public = ?";
                    $params[] = $is_public;
                }

                if ($is_featured_exists) {
                    $update_fields[] = "is_featured = ?";
                    $params[] = $is_featured;
                }

                if ($is_active_exists) {
                    $update_fields[] = "is_active = ?";
                    $params[] = $is_active;
                }

                if ($media_type_exists) {
                    $update_fields[] = "media_type = ?";
                    $params[] = $media_type;
                }

                if ($media_url_exists) {
                    $update_fields[] = "media_url = ?";
                    $params[] = $final_media_url;
                }

                if ($background_color_exists) {
                    $update_fields[] = "background_color = ?";
                    $params[] = $background_color;
                }

                if ($text_color_exists) {
                    $update_fields[] = "text_color = ?";
                    $params[] = $text_color;
                }

                if ($start_date_exists) {
                    $update_fields[] = "start_date = ?";
                    $params[] = $start_date;
                }

                if ($end_date_exists) {
                    $update_fields[] = "end_date = ?";
                    $params[] = $end_date;
                }

                // Add announcement_id to params
                $params[] = $announcement_id;

                // Prepare and execute the update query
                $update_query = "UPDATE announcements SET " . implode(", ", $update_fields) . " WHERE announcement_id = ?";
                $stmt = $pdo->prepare($update_query);
                $stmt->execute($params);

                $success = 'تم تحديث الإعلان بنجاح';
            } else {
                // Build dynamic insert query based on existing columns
                $insert_fields = [
                    "title",
                    "content",
                    "sender_user_id",
                    "target_role",
                    "target_center_id"
                ];
                $placeholders = ["?", "?", "?", "?", "?"];
                $params = [
                    $title,
                    $content,
                    $sender_user_id,
                    $target_role,
                    $target_center_id
                ];

                // Add optional fields if they exist
                if ($is_public_exists) {
                    $insert_fields[] = "is_public";
                    $placeholders[] = "?";
                    $params[] = $is_public;
                }

                if ($is_featured_exists) {
                    $insert_fields[] = "is_featured";
                    $placeholders[] = "?";
                    $params[] = $is_featured;
                }

                if ($is_active_exists) {
                    $insert_fields[] = "is_active";
                    $placeholders[] = "?";
                    $params[] = $is_active;
                }

                if ($media_type_exists) {
                    $insert_fields[] = "media_type";
                    $placeholders[] = "?";
                    $params[] = $media_type;
                }

                if ($media_url_exists) {
                    $insert_fields[] = "media_url";
                    $placeholders[] = "?";
                    $params[] = $final_media_url;
                }

                if ($background_color_exists) {
                    $insert_fields[] = "background_color";
                    $placeholders[] = "?";
                    $params[] = $background_color;
                }

                if ($text_color_exists) {
                    $insert_fields[] = "text_color";
                    $placeholders[] = "?";
                    $params[] = $text_color;
                }

                if ($start_date_exists) {
                    $insert_fields[] = "start_date";
                    $placeholders[] = "?";
                    $params[] = $start_date;
                }

                if ($end_date_exists) {
                    $insert_fields[] = "end_date";
                    $placeholders[] = "?";
                    $params[] = $end_date;
                }

                // Prepare and execute the insert query
                $insert_query = "INSERT INTO announcements (" . implode(", ", $insert_fields) . ") VALUES (" . implode(", ", $placeholders) . ")";
                $stmt = $pdo->prepare($insert_query);
                $stmt->execute($params);

                $success = 'تم إرسال الإعلان بنجاح';
            }

            // Redirect to announcements list
            set_flash_message('success', $success);
            redirect('pages/announcements.php');
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء ' . ($announcement_id ? 'تحديث' : 'إرسال') . ' الإعلان: ' . $e->getMessage();
        }
    }
}

// Role names in Arabic
$role_names = [
    'all' => 'الجميع',
    'system_owner' => 'مالك النظام',
    'center_admin' => 'مدراء المراكز',
    'teacher' => 'المعلمين',
    'student' => 'الطلاب',
    'parent' => 'أولياء الأمور',
    'center_specific' => 'مركز محدد'
];

// Roles disponibles para administradores de centros
$center_admin_roles = [
    'all' => 'الجميع (أعضاء المركز)',
    'teacher' => 'المعلمين',
    'student' => 'الطلاب',
    'parent' => 'أولياء الأمور'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $announcement_id ? 'تعديل إعلان' : 'إرسال إعلان جديد'; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Summernote CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if (has_role('system_owner')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="system_owner_dashboard.php">لوحة التحكم</a>
                            </li>
                        <?php elseif (has_role('center_admin')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="center_admin_dashboard.php">لوحة التحكم</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="announcements.php">الإعلانات</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container py-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <?php if (has_role('system_owner')): ?>
                    <li class="breadcrumb-item"><a href="system_owner_dashboard.php">لوحة التحكم</a></li>
                <?php elseif (has_role('center_admin')): ?>
                    <li class="breadcrumb-item"><a href="center_admin_dashboard.php">لوحة التحكم</a></li>
                <?php endif; ?>
                <li class="breadcrumb-item"><a href="announcements.php">الإعلانات</a></li>
                <li class="breadcrumb-item active" aria-current="page">
                    <?php echo $announcement_id ? 'تعديل إعلان' : 'إرسال إعلان جديد'; ?>
                </li>
            </ol>
        </nav>

        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullhorn me-2"></i>
                    <?php echo $announcement_id ? 'تعديل إعلان' : 'إرسال إعلان جديد'; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>

                <?php if (has_role('center_admin')): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    كمدير مركز، يمكنك فقط إنشاء إعلانات لأعضاء مركزك (المعلمين، الطلاب، أولياء الأمور). عند اختيار "الجميع"، سيظهر الإعلان لجميع أعضاء مركزك فقط.
                </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان الإعلان <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required
                               value="<?php echo isset($_POST['title']) ? $_POST['title'] : ($announcement ? $announcement['title'] : ''); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">محتوى الإعلان <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content" name="content" rows="10" required><?php
                            echo isset($_POST['content']) ? $_POST['content'] : ($announcement ? $announcement['content'] : '');
                        ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="target_role" class="form-label">الفئة المستهدفة <span class="text-danger">*</span></label>
                            <select class="form-select" id="target_role" name="target_role" required>
                                <?php
                                $selected_role = isset($_POST['target_role']) ? $_POST['target_role'] :
                                                ($announcement ? $announcement['target_role'] : 'all');

                                // Determinar qué roles mostrar según el tipo de usuario
                                $available_roles = has_role('system_owner') ? $role_names : $center_admin_roles;

                                foreach ($available_roles as $role_value => $role_label):
                                ?>
                                    <option value="<?php echo $role_value; ?>" <?php echo $selected_role == $role_value ? 'selected' : ''; ?>>
                                        <?php echo $role_label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3" id="center_selection" style="display: none;">
                            <label for="target_center_id" class="form-label">المركز المستهدف <span class="text-danger">*</span></label>
                            <?php if (has_role('system_owner')): ?>
                            <select class="form-select" id="target_center_id" name="target_center_id">
                                <option value="">-- اختر المركز --</option>
                                <?php
                                $selected_center = isset($_POST['target_center_id']) ? $_POST['target_center_id'] :
                                                  ($announcement ? $announcement['target_center_id'] : '');

                                foreach ($centers as $center):
                                ?>
                                    <option value="<?php echo $center['center_id']; ?>" <?php echo $selected_center == $center['center_id'] ? 'selected' : ''; ?>>
                                        <?php echo $center['center_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php else: ?>
                            <!-- Para administradores de centro, mostrar solo su centro -->
                            <?php foreach ($centers as $center): ?>
                                <input type="hidden" name="target_center_id" value="<?php echo $center['center_id']; ?>">
                                <input type="text" class="form-control" value="<?php echo $center['center_name']; ?>" readonly>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">تاريخ بداية الإعلان <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required
                                   value="<?php echo isset($_POST['start_date']) ? $_POST['start_date'] :
                                          ($announcement && isset($announcement['start_date']) ? $announcement['start_date'] : date('Y-m-d')); ?>">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">تاريخ نهاية الإعلان <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="end_date" name="end_date" required
                                   value="<?php echo isset($_POST['end_date']) ? $_POST['end_date'] :
                                          ($announcement && isset($announcement['end_date']) ? $announcement['end_date'] : date('Y-m-d', strtotime('+30 days'))); ?>">
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">الوسائط المتعددة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="media_type" class="form-label">نوع الوسائط</label>
                                    <select class="form-select" id="media_type" name="media_type">
                                        <option value="none" <?php echo (!$announcement || !isset($announcement['media_type']) || $announcement['media_type'] == 'none') ? 'selected' : ''; ?>>بدون وسائط</option>
                                        <option value="image" <?php echo (isset($_POST['media_type']) && $_POST['media_type'] == 'image') || ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'image') ? 'selected' : ''; ?>>صورة</option>
                                        <option value="video" <?php echo (isset($_POST['media_type']) && $_POST['media_type'] == 'video') || ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'video') ? 'selected' : ''; ?>>فيديو</option>
                                        <option value="audio" <?php echo (isset($_POST['media_type']) && $_POST['media_type'] == 'audio') || ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'audio') ? 'selected' : ''; ?>>صوت</option>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3 media-upload" id="image_upload" style="display: none;">
                                    <label for="image_file" class="form-label">صورة الإعلان</label>
                                    <input type="file" class="form-control" id="image_file" name="image_file" accept="image/*">
                                    <?php if ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'image' && !empty($announcement['media_url'])): ?>
                                        <div class="mt-2">
                                            <img src="<?php echo '../' . $announcement['media_url']; ?>" alt="صورة الإعلان" class="img-thumbnail" style="max-height: 100px;">
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="col-md-6 mb-3 media-upload" id="video_upload" style="display: none;">
                                    <label for="video_file" class="form-label">فيديو الإعلان</label>
                                    <input type="file" class="form-control" id="video_file" name="video_file" accept="video/*">
                                    <?php if ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'video' && !empty($announcement['media_url'])): ?>
                                        <div class="mt-2">
                                            <video controls class="img-thumbnail" style="max-height: 100px;">
                                                <source src="<?php echo '../' . $announcement['media_url']; ?>" type="video/mp4">
                                                متصفحك لا يدعم تشغيل الفيديو.
                                            </video>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="col-md-6 mb-3 media-upload" id="audio_upload" style="display: none;">
                                    <label for="audio_file" class="form-label">ملف صوتي للإعلان</label>
                                    <input type="file" class="form-control" id="audio_file" name="audio_file" accept="audio/*">
                                    <?php if ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'audio' && !empty($announcement['media_url'])): ?>
                                        <div class="mt-2">
                                            <audio controls class="w-100">
                                                <source src="<?php echo '../' . $announcement['media_url']; ?>" type="audio/mpeg">
                                                متصفحك لا يدعم تشغيل الصوت.
                                            </audio>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="col-md-6 mb-3 media-upload" id="media_url_input" style="display: none;">
                                    <label for="media_url" class="form-label">رابط الوسائط (اختياري)</label>
                                    <input type="url" class="form-control" id="media_url" name="media_url" placeholder="https://example.com/media.mp4"
                                           value="<?php echo isset($_POST['media_url']) ? $_POST['media_url'] :
                                                  ($announcement && isset($announcement['media_url']) && (strpos($announcement['media_url'], 'http://') === 0 || strpos($announcement['media_url'], 'https://') === 0) ? $announcement['media_url'] : ''); ?>">
                                    <div class="form-text">يمكنك إدخال رابط مباشر للوسائط بدلاً من رفع ملف</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <label for="background_color" class="form-label">لون الخلفية</label>
                                    <input type="color" class="form-control form-control-color w-100" id="background_color" name="background_color"
                                           value="<?php echo isset($_POST['background_color']) ? $_POST['background_color'] :
                                                  ($announcement && isset($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'); ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="text_color" class="form-label">لون النص</label>
                                    <input type="color" class="form-control form-control-color w-100" id="text_color" name="text_color"
                                           value="<?php echo isset($_POST['text_color']) ? $_POST['text_color'] :
                                                  ($announcement && isset($announcement['text_color']) ? $announcement['text_color'] : '#000000'); ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <?php if (has_role('system_owner')): ?>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_public" name="is_public" value="1"
                                       <?php echo (isset($_POST['is_public']) || ($announcement && isset($announcement['is_public']) && $announcement['is_public'] == 1)) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_public">
                                    إظهار للزوار (غير المسجلين)
                                </label>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1"
                                       <?php echo (isset($_POST['is_featured']) || ($announcement && isset($announcement['is_featured']) && $announcement['is_featured'] == 1)) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_featured">
                                    إعلان مميز (يظهر في الصفحة الرئيسية)
                                </label>
                            </div>
                        </div>
                        <?php else: ?>
                        <!-- Campos ocultos para mantener los valores existentes en caso de edición -->
                        <?php if ($announcement && isset($announcement['is_public']) && $announcement['is_public'] == 1): ?>
                            <input type="hidden" name="is_public" value="1">
                        <?php endif; ?>
                        <?php if ($announcement && isset($announcement['is_featured']) && $announcement['is_featured'] == 1): ?>
                            <input type="hidden" name="is_featured" value="1">
                        <?php endif; ?>
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                فقط مدير النظام يمكنه إنشاء إعلانات للزوار أو إعلانات مميزة في الصفحة الرئيسية.
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                       <?php echo (!isset($_POST['is_active']) && (!$announcement || !isset($announcement['is_active']))) ||
                                                 (isset($_POST['is_active'])) ||
                                                 ($announcement && isset($announcement['is_active']) && $announcement['is_active'] == 1) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">
                                    نشط
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">معاينة الإعلان</h6>
                        </div>
                        <div class="card-body" id="announcement_preview" style="background-color: #ffffff; color: #000000;">
                            <div class="text-center mb-3" id="preview_media_container" style="display: none;">
                                <div id="preview_image_container" style="display: none;">
                                    <img id="preview_image" src="" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 200px;">
                                </div>
                                <div id="preview_video_container" style="display: none;">
                                    <video id="preview_video" controls class="img-fluid rounded" style="max-height: 200px;">
                                        <source src="" type="video/mp4">
                                        متصفحك لا يدعم تشغيل الفيديو.
                                    </video>
                                </div>
                                <div id="preview_audio_container" style="display: none;">
                                    <audio id="preview_audio" controls class="w-100">
                                        <source src="" type="audio/mpeg">
                                        متصفحك لا يدعم تشغيل الصوت.
                                    </audio>
                                </div>
                            </div>
                            <h4 id="preview_title">عنوان الإعلان</h4>
                            <div id="preview_content">محتوى الإعلان</div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="announcements.php" class="btn btn-secondary">إلغاء</a>
                        <button type="submit" class="btn btn-primary">
                            <?php echo $announcement_id ? 'تحديث الإعلان' : 'إرسال الإعلان'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Summernote JS -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize rich text editor
            $('#content').summernote({
                height: 300,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                lang: 'ar-AR',
                direction: 'rtl',
                callbacks: {
                    onChange: function(contents) {
                        updatePreview();
                    }
                }
            });

            // Show/hide center selection based on target role
            function toggleCenterSelection() {
                if ($('#target_role').val() === 'center_specific') {
                    $('#center_selection').show();
                    $('#target_center_id').prop('required', true);
                } else {
                    $('#center_selection').hide();
                    $('#target_center_id').prop('required', false);
                }
            }

            // Show/hide media upload fields based on media type
            function toggleMediaUpload() {
                const mediaType = $('#media_type').val();

                // Hide all media upload fields
                $('.media-upload').hide();

                // Show the appropriate media upload field
                if (mediaType === 'image') {
                    $('#image_upload').show();
                    $('#media_url_input').show();
                } else if (mediaType === 'video') {
                    $('#video_upload').show();
                    $('#media_url_input').show();
                } else if (mediaType === 'audio') {
                    $('#audio_upload').show();
                    $('#media_url_input').show();
                }

                // Log for debugging
                console.log('Media type selected:', mediaType);
                console.log('Image upload display:', $('#image_upload').css('display'));
                console.log('Media URL input display:', $('#media_url_input').css('display'));

                updateMediaPreview();
            }

            // Initial toggles
            toggleCenterSelection();

            // Show media upload fields on page load based on selected media type
            $(document).ready(function() {
                const mediaType = $('#media_type').val();
                console.log('Initial media type:', mediaType);

                // Show the appropriate media upload field
                if (mediaType === 'image') {
                    $('#image_upload').show();
                    $('#media_url_input').show();
                } else if (mediaType === 'video') {
                    $('#video_upload').show();
                    $('#media_url_input').show();
                } else if (mediaType === 'audio') {
                    $('#audio_upload').show();
                    $('#media_url_input').show();
                }
            });

            toggleMediaUpload();

            // Toggle on change
            $('#target_role').change(toggleCenterSelection);
            $('#media_type').change(toggleMediaUpload);

            // Update preview when inputs change
            $('#title, #content, #background_color, #text_color').on('input', function() {
                updatePreview();
            });

            // Handle media file previews
            $('#image_file').change(function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#preview_image').attr('src', e.target.result);
                        updateMediaPreview();
                    }
                    reader.readAsDataURL(file);
                }
            });

            $('#video_file').change(function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#preview_video').attr('src', e.target.result);
                        updateMediaPreview();
                    }
                    reader.readAsDataURL(file);
                }
            });

            $('#audio_file').change(function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#preview_audio').attr('src', e.target.result);
                        updateMediaPreview();
                    }
                    reader.readAsDataURL(file);
                }
            });

            $('#media_url').on('input', function() {
                updateMediaPreview();
            });

            // Update colors when color inputs change
            $('#background_color, #text_color').change(function() {
                updatePreview();
            });

            // Function to update the media preview
            function updateMediaPreview() {
                const mediaType = $('#media_type').val();
                const mediaUrl = $('#media_url').val();

                // Hide all media preview containers
                $('#preview_media_container').hide();
                $('#preview_image_container').hide();
                $('#preview_video_container').hide();
                $('#preview_audio_container').hide();

                if (mediaType === 'none') {
                    return;
                }

                $('#preview_media_container').show();

                if (mediaType === 'image') {
                    const file = $('#image_file')[0].files[0];
                    if (file) {
                        $('#preview_image_container').show();
                    } else if (mediaUrl) {
                        $('#preview_image').attr('src', mediaUrl);
                        $('#preview_image_container').show();
                    } else {
                        <?php if ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'image' && !empty($announcement['media_url'])): ?>
                        $('#preview_image').attr('src', '<?php echo "../" . $announcement['media_url']; ?>');
                        $('#preview_image_container').show();
                        <?php endif; ?>
                    }
                } else if (mediaType === 'video') {
                    const file = $('#video_file')[0].files[0];
                    if (file) {
                        $('#preview_video_container').show();
                    } else if (mediaUrl) {
                        $('#preview_video source').attr('src', mediaUrl);
                        $('#preview_video')[0].load();
                        $('#preview_video_container').show();
                    } else {
                        <?php if ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'video' && !empty($announcement['media_url'])): ?>
                        $('#preview_video source').attr('src', '<?php echo "../" . $announcement['media_url']; ?>');
                        $('#preview_video')[0].load();
                        $('#preview_video_container').show();
                        <?php endif; ?>
                    }
                } else if (mediaType === 'audio') {
                    const file = $('#audio_file')[0].files[0];
                    if (file) {
                        $('#preview_audio_container').show();
                    } else if (mediaUrl) {
                        $('#preview_audio source').attr('src', mediaUrl);
                        $('#preview_audio')[0].load();
                        $('#preview_audio_container').show();
                    } else {
                        <?php if ($announcement && isset($announcement['media_type']) && $announcement['media_type'] == 'audio' && !empty($announcement['media_url'])): ?>
                        $('#preview_audio source').attr('src', '<?php echo "../" . $announcement['media_url']; ?>');
                        $('#preview_audio')[0].load();
                        $('#preview_audio_container').show();
                        <?php endif; ?>
                    }
                }
            }

            // Function to update the preview
            function updatePreview() {
                const title = $('#title').val() || 'عنوان الإعلان';
                const content = $('#content').summernote('code') || 'محتوى الإعلان';
                const bgColor = $('#background_color').val();
                const textColor = $('#text_color').val();

                $('#preview_title').text(title);
                $('#preview_content').html(content);
                $('#announcement_preview').css({
                    'background-color': bgColor,
                    'color': textColor
                });

                updateMediaPreview();
            }

            // Initialize preview
            updatePreview();
        });
    </script>
</body>
</html>
