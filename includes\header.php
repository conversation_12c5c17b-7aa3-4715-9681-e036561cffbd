<?php
// Include common functions and definitions
require_once dirname(__FILE__) . '/common.php';

// Determinar si estamos en la raíz o en una subcarpeta
$is_root = !strpos($_SERVER['PHP_SELF'], '/pages/') && !strpos($_SERVER['PHP_SELF'], '/auth/');

// Función para determinar si un enlace está activo
function is_active($page_name) {
    return strpos($_SERVER['PHP_SELF'], $page_name) !== false ? 'active' : '';
}

// Preparar las rutas base para los enlaces
$root_url = get_root_url();
$pages_url = $root_url . 'pages/';
$auth_url = $root_url . 'auth/';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo $root_url; ?>css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="<?php echo $root_url; ?>index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if (is_logged_in()): ?>
                            <!-- Enlaces comunes para todos los usuarios logueados -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('index.php'); ?>" href="<?php echo $root_url; ?>index.php">الرئيسية</a>
                            </li>

                            <!-- Enlaces específicos según el rol -->
                            <?php if (has_role('system_owner')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('system_owner_dashboard.php'); ?>" href="<?php echo $pages_url; ?>system_owner_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('centers.php'); ?>" href="<?php echo $pages_url; ?>centers.php">المراكز</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('users.php'); ?>" href="<?php echo $pages_url; ?>users.php">المستخدمين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('system_announcements.php'); ?>" href="<?php echo $pages_url; ?>system_announcements.php">الإعلانات</a>
                                </li>
                            <?php elseif (has_role('center_admin')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('center_admin_dashboard.php'); ?>" href="<?php echo $pages_url; ?>center_admin_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('teachers.php'); ?>" href="<?php echo $pages_url; ?>teachers.php">المعلمين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('students.php'); ?>" href="<?php echo $pages_url; ?>students.php">الطلاب</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('circles.php'); ?>" href="<?php echo $pages_url; ?>circles.php">الحلقات</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('system_announcements.php'); ?>" href="<?php echo $pages_url; ?>system_announcements.php">الإعلانات</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('parents.php'); ?>" href="<?php echo $pages_url; ?>parents.php">أولياء الأمور</a>
                                </li>
                            <?php elseif (has_role('teacher')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('teacher_dashboard.php'); ?>" href="<?php echo $pages_url; ?>teacher_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('my_circles.php'); ?>" href="<?php echo $pages_url; ?>my_circles.php">حلقاتي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('teacher_students.php'); ?>" href="<?php echo $pages_url; ?>teacher_students.php">طلابي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('attendance.php'); ?>" href="<?php echo $pages_url; ?>attendance.php">الحضور</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('memorization.php'); ?>" href="<?php echo $pages_url; ?>memorization.php">متابعة الحفظ</a>
                                </li>
                            <?php elseif (has_role('student')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('student_dashboard.php'); ?>" href="<?php echo $pages_url; ?>student_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('my_progress.php'); ?>" href="<?php echo $pages_url; ?>my_progress.php">تقدمي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('student_assignments.php'); ?>" href="<?php echo $pages_url; ?>student_assignments.php">الواجبات</a>
                                </li>
                            <?php elseif (has_role('parent')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('parent_dashboard.php'); ?>" href="<?php echo $pages_url; ?>parent_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('my_children.php'); ?>" href="<?php echo $pages_url; ?>my_children.php">أبنائي</a>
                                </li>
                            <?php endif; ?>

                            <!-- Enlaces comunes para todos los usuarios logueados -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('announcements.php'); ?>" href="<?php echo $pages_url; ?>announcements.php">الإعلانات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('messages.php'); ?>" href="<?php echo $pages_url; ?>messages.php">الرسائل</a>
                            </li>
                        <?php else: ?>
                            <!-- Enlaces para usuarios no logueados -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('index.php'); ?>" href="<?php echo $root_url; ?>index.php">الرئيسية</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('public_announcements.php'); ?>" href="<?php echo $pages_url; ?>public_announcements.php">الإعلانات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('about.php'); ?>" href="<?php echo $pages_url; ?>about.php">عن النظام</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('contact.php'); ?>" href="<?php echo $pages_url; ?>contact.php">اتصل بنا</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    <div class="d-flex">
                        <?php if (is_logged_in()): ?>
                            <div class="dropdown">
                                <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="<?php echo $pages_url; ?>profile.php">الملف الشخصي</a></li>
                                    <li><a class="dropdown-item" href="<?php echo $pages_url; ?>settings.php">الإعدادات</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo $auth_url; ?>logout.php">تسجيل الخروج</a></li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <a href="<?php echo $auth_url; ?>login.php" class="btn btn-outline-light me-2">تسجيل الدخول</a>
                            <a href="<?php echo $auth_url; ?>register.php" class="btn btn-light">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <main class="container py-4">
        <?php if (isset($_SESSION['flash_message']) && isset($_SESSION['flash_type'])): ?>
            <div class="alert alert-<?php echo $_SESSION['flash_type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['flash_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
        <?php endif; ?>
