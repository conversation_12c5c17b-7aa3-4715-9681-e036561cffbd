<?php
// Include common functions and definitions
require_once dirname(__FILE__) . '/common.php';

// Determinar si estamos en la raíz o en una subcarpeta
$is_root = !strpos($_SERVER['PHP_SELF'], '/pages/') && !strpos($_SERVER['PHP_SELF'], '/auth/');

// Función para determinar si un enlace está activo
function is_active($page_name) {
    return strpos($_SERVER['PHP_SELF'], $page_name) !== false ? 'active' : '';
}

// Preparar las rutas base para los enlaces
$root_url = get_root_url();
$pages_url = $root_url . 'pages/';
$auth_url = $root_url . 'auth/';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo $root_url; ?>css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="<?php echo $root_url; ?>index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if (is_logged_in()): ?>
                            <!-- Enlaces comunes para todos los usuarios logueados -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('index.php'); ?>" href="<?php echo $root_url; ?>index.php">الرئيسية</a>
                            </li>

                            <!-- Enlaces específicos según el rol -->
                            <?php if (has_role('system_owner')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('system_owner_dashboard.php'); ?>" href="<?php echo $pages_url; ?>system_owner_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('centers.php'); ?>" href="<?php echo $pages_url; ?>centers.php">المراكز</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('users.php'); ?>" href="<?php echo $pages_url; ?>users.php">المستخدمين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('system_announcements.php'); ?>" href="<?php echo $pages_url; ?>system_announcements.php">الإعلانات</a>
                                </li>
                            <?php elseif (has_role('center_admin')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('center_admin_dashboard.php'); ?>" href="<?php echo $pages_url; ?>center_admin_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('teachers.php'); ?>" href="<?php echo $pages_url; ?>teachers.php">المعلمين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('students.php'); ?>" href="<?php echo $pages_url; ?>students.php">الطلاب</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('circles.php'); ?>" href="<?php echo $pages_url; ?>circles.php">الحلقات</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('system_announcements.php'); ?>" href="<?php echo $pages_url; ?>system_announcements.php">الإعلانات</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('parents.php'); ?>" href="<?php echo $pages_url; ?>parents.php">أولياء الأمور</a>
                                </li>
                            <?php elseif (has_role('teacher')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('teacher_dashboard.php'); ?>" href="<?php echo $pages_url; ?>teacher_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('my_circles.php'); ?>" href="<?php echo $pages_url; ?>my_circles.php">حلقاتي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('teacher_students.php'); ?>" href="<?php echo $pages_url; ?>teacher_students.php">طلابي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('attendance.php'); ?>" href="<?php echo $pages_url; ?>attendance.php">الحضور</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('memorization.php'); ?>" href="<?php echo $pages_url; ?>memorization.php">متابعة الحفظ</a>
                                </li>
                            <?php elseif (has_role('student')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('student_dashboard.php'); ?>" href="<?php echo $pages_url; ?>student_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('my_progress.php'); ?>" href="<?php echo $pages_url; ?>my_progress.php">تقدمي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('student_assignments.php'); ?>" href="<?php echo $pages_url; ?>student_assignments.php">الواجبات</a>
                                </li>
                            <?php elseif (has_role('parent')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('parent_dashboard.php'); ?>" href="<?php echo $pages_url; ?>parent_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo is_active('my_children.php'); ?>" href="<?php echo $pages_url; ?>my_children.php">أبنائي</a>
                                </li>
                            <?php endif; ?>

                            <!-- Enlaces comunes para todos los usuarios logueados -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('announcements.php'); ?>" href="<?php echo $pages_url; ?>announcements.php">الإعلانات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('messages.php'); ?>" href="<?php echo $pages_url; ?>messages.php">الرسائل</a>
                            </li>
                        <?php else: ?>
                            <!-- Enlaces para usuarios no logueados -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('index.php'); ?>" href="<?php echo $root_url; ?>index.php">الرئيسية</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('public_announcements.php'); ?>" href="<?php echo $pages_url; ?>public_announcements.php">الإعلانات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('about.php'); ?>" href="<?php echo $pages_url; ?>about.php">عن النظام</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo is_active('contact.php'); ?>" href="<?php echo $pages_url; ?>contact.php">اتصل بنا</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    <div class="d-flex align-items-center">
                        <?php if (is_logged_in()): ?>
                            <!-- Quick Access Menu for System Owner -->
                            <?php if (has_role('system_owner')): ?>
                                <div class="dropdown me-3">
                                    <button class="btn btn-outline-light btn-sm dropdown-toggle btn-quick-access" type="button" id="quickAccessDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="الوصول السريع">
                                        <i class="fas fa-bolt"></i>
                                        <span class="d-none d-md-inline ms-1">الوصول السريع</span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end quick-access-menu" aria-labelledby="quickAccessDropdown">
                                        <!-- إدارة المراكز -->
                                        <li><h6 class="dropdown-header"><i class="fas fa-building me-2"></i>إدارة المراكز</h6></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>centers.php"><i class="fas fa-list me-2"></i>عرض جميع المراكز</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>add_center.php"><i class="fas fa-plus me-2"></i>إضافة مركز جديد</a></li>
                                        <li><hr class="dropdown-divider"></li>

                                        <!-- إدارة المستخدمين -->
                                        <li><h6 class="dropdown-header"><i class="fas fa-users me-2"></i>إدارة المستخدمين</h6></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>users.php"><i class="fas fa-users me-2"></i>جميع المستخدمين</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>add_admin.php"><i class="fas fa-user-shield me-2"></i>إضافة مدير مركز</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i>المعلمين</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>students.php"><i class="fas fa-user-graduate me-2"></i>الطلاب</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>parents.php"><i class="fas fa-user-friends me-2"></i>أولياء الأمور</a></li>
                                        <li><hr class="dropdown-divider"></li>

                                        <!-- إدارة المحتوى -->
                                        <li><h6 class="dropdown-header"><i class="fas fa-bullhorn me-2"></i>إدارة المحتوى</h6></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>system_announcements.php"><i class="fas fa-bullhorn me-2"></i>الإعلانات العامة</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>system_announcements.php?new=1"><i class="fas fa-plus me-2"></i>إرسال إعلان جديد</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>system_home_image.php"><i class="fas fa-image me-2"></i>تغيير الصورة الرئيسية</a></li>
                                        <li><hr class="dropdown-divider"></li>

                                        <!-- إعدادات النظام -->
                                        <li><h6 class="dropdown-header"><i class="fas fa-cogs me-2"></i>إعدادات النظام</h6></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>system_settings.php"><i class="fas fa-cogs me-2"></i>إعدادات عامة</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>whatsapp_settings.php"><i class="fab fa-whatsapp me-2"></i>إعدادات الواتساب</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>backup_restore.php"><i class="fas fa-database me-2"></i>النسخ الاحتياطي</a></li>
                                        <li><hr class="dropdown-divider"></li>

                                        <!-- التقارير والإحصائيات -->
                                        <li><h6 class="dropdown-header"><i class="fas fa-chart-bar me-2"></i>التقارير</h6></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>reports.php"><i class="fas fa-chart-line me-2"></i>تقارير شاملة</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>system_logs.php"><i class="fas fa-file-alt me-2"></i>سجلات النظام</a></li>
                                        <li><a class="dropdown-item" href="<?php echo $pages_url; ?>user_activity.php"><i class="fas fa-history me-2"></i>نشاط المستخدمين</a></li>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- WhatsApp Settings Icon for Admins -->
                            <?php if (has_any_role(['system_owner', 'center_admin'])): ?>
                                <div class="me-3">
                                    <a href="<?php echo $pages_url; ?>whatsapp_settings.php"
                                       class="btn btn-outline-light btn-sm position-relative"
                                       title="إعدادات الواتساب">
                                        <i class="fab fa-whatsapp"></i>
                                        <?php
                                        // Check WhatsApp configuration status
                                        $config_errors = validate_whatsapp_config();
                                        $is_whatsapp_configured = empty($config_errors);
                                        ?>
                                        <?php if (!$is_whatsapp_configured): ?>
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning">
                                                <i class="fas fa-exclamation-triangle" style="font-size: 8px;"></i>
                                                <span class="visually-hidden">إعدادات غير مكتملة</span>
                                            </span>
                                        <?php elseif (is_whatsapp_enabled()): ?>
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success">
                                                <i class="fas fa-check" style="font-size: 8px;"></i>
                                                <span class="visually-hidden">مفعل</span>
                                            </span>
                                        <?php endif; ?>
                                    </a>
                                </div>
                            <?php endif; ?>

                            <div class="dropdown">
                                <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="<?php echo $pages_url; ?>profile.php">الملف الشخصي</a></li>
                                    <li><a class="dropdown-item" href="<?php echo $pages_url; ?>settings.php">الإعدادات</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo $auth_url; ?>logout.php">تسجيل الخروج</a></li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <a href="<?php echo $auth_url; ?>login.php" class="btn btn-outline-light me-2">تسجيل الدخول</a>
                            <a href="<?php echo $auth_url; ?>register.php" class="btn btn-light">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <main class="container py-4">
        <?php if (isset($_SESSION['flash_message']) && isset($_SESSION['flash_type'])): ?>
            <div class="alert alert-<?php echo $_SESSION['flash_type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['flash_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
        <?php endif; ?>
