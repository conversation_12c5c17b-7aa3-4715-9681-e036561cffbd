<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success_messages = [];
$error_messages = [];

try {
    // 1. Update some users' created_at to recent dates for testing
    $stmt = $pdo->prepare("
        UPDATE users
        SET created_at = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY)
        WHERE user_id IN (
            SELECT * FROM (
                SELECT user_id FROM users
                WHERE role_id != (SELECT role_id FROM roles WHERE role_name = 'system_owner')
                ORDER BY user_id DESC
                LIMIT 5
            ) as temp
        )
    ");
    if ($stmt->execute()) {
        $success_messages[] = "تم تحديث تواريخ تسجيل 5 مستخدمين لتظهر كأنشطة حديثة";
    }

    // 2. Update some enrollments' enrollment_date to recent dates
    $stmt = $pdo->prepare("
        UPDATE student_circle_enrollments
        SET enrollment_date = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY)
        WHERE enrollment_id IN (
            SELECT * FROM (
                SELECT enrollment_id FROM student_circle_enrollments
                ORDER BY enrollment_id DESC
                LIMIT 3
            ) as temp
        )
    ");
    if ($stmt->execute()) {
        $success_messages[] = "تم تحديث تواريخ 3 تسجيلات في الحلقات لتظهر كأنشطة حديثة";
    }

    // 3. Create announcements table if it doesn't exist and add sample data
    $stmt = $pdo->query("SHOW TABLES LIKE 'announcements'");
    if ($stmt->rowCount() == 0) {
        // Create announcements table
        $pdo->exec("
            CREATE TABLE announcements (
                announcement_id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                created_by_user_id INT NOT NULL,
                center_id INT NULL,
                target_role VARCHAR(50) NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                is_active BOOLEAN NOT NULL DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by_user_id) REFERENCES users(user_id),
                FOREIGN KEY (center_id) REFERENCES centers(center_id)
            )
        ");
        $success_messages[] = "تم إنشاء جدول الإعلانات";
    }

    // Add sample announcements
    $announcements = [
        ['إعلان مهم للطلاب', 'يرجى الحضور في الموعد المحدد', 'student'],
        ['إشعار للمعلمين', 'اجتماع المعلمين يوم الأحد', 'teacher'],
        ['إعلان عام', 'تحديث في نظام الحضور', 'all']
    ];

    foreach ($announcements as $announcement) {
        // Check which column exists for the user who created the announcement
        $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'created_by_user_id'");
        $stmt->execute();
        $has_created_by = $stmt->rowCount() > 0;

        $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'sender_user_id'");
        $stmt->execute();
        $has_sender = $stmt->rowCount() > 0;

        $user_id_column = $has_sender ? 'sender_user_id' : 'created_by_user_id';

        $stmt = $pdo->prepare("
            INSERT INTO announcements (title, content, {$user_id_column}, target_role, start_date, end_date, created_at)
            VALUES (?, ?, ?, ?, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 5) DAY))
        ");
        $stmt->execute([
            $announcement[0],
            $announcement[1],
            $_SESSION['user_id'],
            $announcement[2]
        ]);
    }
    $success_messages[] = "تم إضافة 3 إعلانات تجريبية";

    // 4. Create WhatsApp logs table if it doesn't exist and add sample data
    $stmt = $pdo->query("SHOW TABLES LIKE 'whatsapp_logs'");
    if ($stmt->rowCount() == 0) {
        // Create whatsapp_logs table
        $pdo->exec("
            CREATE TABLE whatsapp_logs (
                log_id INT AUTO_INCREMENT PRIMARY KEY,
                recipient_number VARCHAR(20) NOT NULL,
                message_type VARCHAR(50) NOT NULL,
                message_content TEXT,
                status ENUM('sent', 'failed', 'pending') DEFAULT 'pending',
                sent_by_user_id INT NULL,
                error_message TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sent_by_user_id) REFERENCES users(user_id)
            )
        ");
        $success_messages[] = "تم إنشاء جدول سجلات الواتساب";
    }

    // Add sample WhatsApp logs
    $whatsapp_logs = [
        ['0501234567', 'absence_notification', 'إشعار غياب الطالب', 'sent'],
        ['0551234568', 'assignment_reminder', 'تذكير بالواجب', 'sent'],
        ['0561234569', 'general_announcement', 'إعلان عام', 'failed'],
        ['0571234570', 'attendance_summary', 'ملخص الحضور', 'sent']
    ];

    foreach ($whatsapp_logs as $log) {
        $stmt = $pdo->prepare("
            INSERT INTO whatsapp_logs (recipient_number, message_type, message_content, status, sent_by_user_id, created_at)
            VALUES (?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 5) DAY))
        ");
        $stmt->execute([
            $log[0],
            $log[1],
            $log[2],
            $log[3],
            $_SESSION['user_id']
        ]);
    }
    $success_messages[] = "تم إضافة 4 سجلات واتساب تجريبية";

    // 5. Create attendance_records table if it doesn't exist and add sample data
    $stmt = $pdo->query("SHOW TABLES LIKE 'attendance_records'");
    if ($stmt->rowCount() == 0) {
        // Create attendance_records table
        $pdo->exec("
            CREATE TABLE attendance_records (
                attendance_id INT AUTO_INCREMENT PRIMARY KEY,
                enrollment_id INT NOT NULL,
                session_date DATE NOT NULL,
                status ENUM('present', 'absent_excused', 'absent_unexcused', 'late') NOT NULL,
                notes TEXT,
                recorded_by_user_id INT NOT NULL,
                recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (enrollment_id) REFERENCES student_circle_enrollments(enrollment_id),
                FOREIGN KEY (recorded_by_user_id) REFERENCES users(user_id),
                UNIQUE KEY unique_attendance (enrollment_id, session_date)
            )
        ");
        $success_messages[] = "تم إنشاء جدول سجلات الحضور";
    }

    // Add sample attendance records
    $stmt = $pdo->query("
        SELECT sce.enrollment_id, u.full_name as student_name
        FROM student_circle_enrollments sce
        JOIN users u ON sce.student_user_id = u.user_id
        LIMIT 5
    ");
    $enrollments = $stmt->fetchAll();

    $stmt = $pdo->query("
        SELECT user_id FROM users
        WHERE role_id = (SELECT role_id FROM roles WHERE role_name = 'teacher')
        LIMIT 1
    ");
    $teacher_id = $stmt->fetchColumn();

    if ($teacher_id && !empty($enrollments)) {
        $statuses = ['present', 'absent_excused', 'absent_unexcused', 'late'];
        $attendance_count = 0;

        foreach ($enrollments as $enrollment) {
            for ($i = 1; $i <= 3; $i++) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $status = $statuses[array_rand($statuses)];

                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO attendance_records (enrollment_id, session_date, status, recorded_by_user_id, recorded_at)
                        VALUES (?, ?, ?, ?, ?)
                    ");

                    if ($stmt->execute([
                        $enrollment['enrollment_id'],
                        $date,
                        $status,
                        $teacher_id,
                        date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(28800, 64800))
                    ])) {
                        $attendance_count++;
                    }
                } catch (PDOException $e) {
                    // Skip duplicate entries
                }
            }
        }
        $success_messages[] = "تم إضافة {$attendance_count} سجل حضور تجريبي";
    }

    // 6. Create memorization_progress table if it doesn't exist and add sample data
    $stmt = $pdo->query("SHOW TABLES LIKE 'memorization_progress'");
    if ($stmt->rowCount() == 0) {
        // Create memorization_progress table
        $pdo->exec("
            CREATE TABLE memorization_progress (
                progress_id INT AUTO_INCREMENT PRIMARY KEY,
                enrollment_id INT NOT NULL,
                surah_name VARCHAR(100) NOT NULL,
                ayah_from INT NOT NULL,
                ayah_to INT NOT NULL,
                memorization_quality ENUM('excellent', 'very_good', 'good', 'fair', 'poor'),
                tajweed_application ENUM('excellent', 'very_good', 'good', 'fair', 'poor'),
                fluency ENUM('excellent', 'very_good', 'good', 'fair', 'poor'),
                teacher_notes TEXT,
                recorded_by_user_id INT NOT NULL,
                recitation_date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (enrollment_id) REFERENCES student_circle_enrollments(enrollment_id),
                FOREIGN KEY (recorded_by_user_id) REFERENCES users(user_id)
            )
        ");
        $success_messages[] = "تم إنشاء جدول تقدم الحفظ";
    }

    // Add sample memorization progress
    if ($teacher_id && !empty($enrollments)) {
        $surahs = [
            ['الفاتحة', 1, 7],
            ['البقرة', 1, 50],
            ['آل عمران', 1, 30],
            ['النساء', 1, 25],
            ['المائدة', 1, 20]
        ];
        $qualities = ['excellent', 'very_good', 'good', 'fair', 'poor'];
        $memorization_count = 0;

        foreach (array_slice($enrollments, 0, 3) as $enrollment) {
            for ($i = 1; $i <= 2; $i++) {
                $surah = $surahs[array_rand($surahs)];
                $quality = $qualities[array_rand($qualities)];

                $stmt = $pdo->prepare("
                    INSERT INTO memorization_progress (
                        enrollment_id, surah_name, ayah_from, ayah_to,
                        memorization_quality, recorded_by_user_id, recitation_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ");

                if ($stmt->execute([
                    $enrollment['enrollment_id'],
                    $surah[0],
                    $surah[1],
                    $surah[2],
                    $quality,
                    $teacher_id,
                    date('Y-m-d', strtotime("-{$i} days"))
                ])) {
                    $memorization_count++;
                }
            }
        }
        $success_messages[] = "تم إضافة {$memorization_count} سجل تقييم حفظ تجريبي";
    }

    // 7. Create system_logs table if it doesn't exist and add sample data
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_logs'");
    if ($stmt->rowCount() == 0) {
        // Create system_logs table
        $pdo->exec("
            CREATE TABLE system_logs (
                log_id INT AUTO_INCREMENT PRIMARY KEY,
                log_category VARCHAR(50) NOT NULL,
                message TEXT NOT NULL,
                log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
                user_id INT NULL,
                ip_address VARCHAR(45) NULL,
                user_agent TEXT NULL,
                additional_data JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id),
                INDEX idx_category (log_category),
                INDEX idx_level (log_level),
                INDEX idx_created_at (created_at)
            )
        ");
        $success_messages[] = "تم إنشاء جدول سجلات النظام";
    }

    // Add sample system logs
    $system_logs = [
        ['AUTH', 'تسجيل دخول ناجح للمستخدم: admin', 'INFO'],
        ['AUTH', 'تسجيل خروج للمستخدم: admin', 'INFO'],
        ['USER', 'تحديث بيانات المستخدم: أحمد محمد', 'INFO'],
        ['CONFIG', 'تغيير إعداد النظام: site_name', 'INFO'],
        ['SYSTEM', 'تم تشغيل النظام بنجاح', 'INFO'],
        ['AUTH', 'محاولة تسجيل دخول فاشلة: wrong_user', 'WARNING'],
        ['SYSTEM', 'خطأ في الاتصال بقاعدة البيانات', 'ERROR']
    ];

    $log_count = 0;
    foreach ($system_logs as $log) {
        for ($i = 1; $i <= 2; $i++) {
            $stmt = $pdo->prepare("
                INSERT INTO system_logs (log_category, message, log_level, user_id, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            if ($stmt->execute([
                $log[0],
                $log[1],
                $log[2],
                $_SESSION['user_id'],
                $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                $_SERVER['HTTP_USER_AGENT'] ?? 'System Init Script',
                date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(0, 86400))
            ])) {
                $log_count++;
            }
        }
    }
    $success_messages[] = "تم إضافة {$log_count} سجل نظام تجريبي";

} catch (PDOException $e) {
    $error_messages[] = 'حدث خطأ أثناء إضافة البيانات: ' . $e->getMessage();
}

// Redirect back to user activity page with messages
if (!empty($success_messages)) {
    set_flash_message('success', implode('<br>', $success_messages));
}
if (!empty($error_messages)) {
    set_flash_message('danger', implode('<br>', $error_messages));
}

redirect('user_activity.php');
?>
