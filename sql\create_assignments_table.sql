-- إن<PERSON>اء جدول الواجبات
CREATE TABLE IF NOT EXISTS assignments (
    assignment_id INT AUTO_INCREMENT PRIMARY KEY,
    circle_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    surah_id INT NULL,
    start_verse INT NULL,
    end_verse INT NULL,
    due_date DATE NOT NULL,
    created_by_user_id INT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (circle_id) REFERENCES circles(circle_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- إضافة فهارس للتحسين الأداء
CREATE INDEX idx_assignments_circle_id ON assignments(circle_id);
CREATE INDEX idx_assignments_created_by_user_id ON assignments(created_by_user_id);
CREATE INDEX idx_assignments_due_date ON assignments(due_date);
CREATE INDEX idx_assignments_is_active ON assignments(is_active);

-- إنشاء جدول سور القرآن إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS quran_surahs (
  surah_id INT AUTO_INCREMENT PRIMARY KEY,
  surah_number INT NOT NULL,
  name_arabic VARCHAR(100) NOT NULL,
  name_english VARCHAR(100) NOT NULL,
  total_verses INT NOT NULL,
  revelation_type ENUM('meccan', 'medinan') NOT NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- إدخال بيانات السور إذا كان الجدول فارغاً
INSERT INTO quran_surahs (surah_number, name_arabic, name_english, total_verses, revelation_type)
SELECT * FROM (
  SELECT 1, 'الفاتحة', 'Al-Fatiha', 7, 'meccan' UNION
  SELECT 2, 'البقرة', 'Al-Baqarah', 286, 'medinan' UNION
  SELECT 3, 'آل عمران', 'Aal-Imran', 200, 'medinan' UNION
  SELECT 4, 'النساء', 'An-Nisa', 176, 'medinan' UNION
  SELECT 5, 'المائدة', 'Al-Ma\'idah', 120, 'medinan' UNION
  SELECT 6, 'الأنعام', 'Al-An\'am', 165, 'meccan' UNION
  SELECT 7, 'الأعراف', 'Al-A\'raf', 206, 'meccan' UNION
  SELECT 8, 'الأنفال', 'Al-Anfal', 75, 'medinan' UNION
  SELECT 9, 'التوبة', 'At-Tawbah', 129, 'medinan' UNION
  SELECT 10, 'يونس', 'Yunus', 109, 'meccan' UNION
  SELECT 11, 'هود', 'Hud', 123, 'meccan' UNION
  SELECT 12, 'يوسف', 'Yusuf', 111, 'meccan' UNION
  SELECT 13, 'الرعد', 'Ar-Ra\'d', 43, 'medinan' UNION
  SELECT 14, 'إبراهيم', 'Ibrahim', 52, 'meccan' UNION
  SELECT 15, 'الحجر', 'Al-Hijr', 99, 'meccan' UNION
  SELECT 16, 'النحل', 'An-Nahl', 128, 'meccan' UNION
  SELECT 17, 'الإسراء', 'Al-Isra', 111, 'meccan' UNION
  SELECT 18, 'الكهف', 'Al-Kahf', 110, 'meccan' UNION
  SELECT 19, 'مريم', 'Maryam', 98, 'meccan' UNION
  SELECT 20, 'طه', 'Ta-Ha', 135, 'meccan' UNION
  SELECT 21, 'الأنبياء', 'Al-Anbiya', 112, 'meccan' UNION
  SELECT 22, 'الحج', 'Al-Hajj', 78, 'medinan' UNION
  SELECT 23, 'المؤمنون', 'Al-Mu\'minun', 118, 'meccan' UNION
  SELECT 24, 'النور', 'An-Nur', 64, 'medinan' UNION
  SELECT 25, 'الفرقان', 'Al-Furqan', 77, 'meccan' UNION
  SELECT 26, 'الشعراء', 'Ash-Shu\'ara', 227, 'meccan' UNION
  SELECT 27, 'النمل', 'An-Naml', 93, 'meccan' UNION
  SELECT 28, 'القصص', 'Al-Qasas', 88, 'meccan' UNION
  SELECT 29, 'العنكبوت', 'Al-Ankabut', 69, 'meccan' UNION
  SELECT 30, 'الروم', 'Ar-Rum', 60, 'meccan'
) AS temp
WHERE NOT EXISTS (SELECT 1 FROM quran_surahs LIMIT 1);
