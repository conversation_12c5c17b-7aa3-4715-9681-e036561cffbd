<?php
// Include common functions and definitions
require_once 'includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
    exit;
}

$success = '';
$error = '';

try {
    // Start transaction
    $pdo->beginTransaction();

    // Check if announcements table exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'announcements'
    ");
    $stmt->execute();
    $table_exists = (bool)$stmt->fetchColumn();

    if (!$table_exists) {
        // Create the announcements table with the enhanced structure
        $pdo->exec("
            CREATE TABLE announcements (
                announcement_id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                sender_user_id INT NOT NULL,
                target_role VARCHAR(50) DEFAULT 'all',
                target_center_id INT NULL,
                is_public BOOLEAN DEFAULT FALSE,
                is_featured BOOLEAN DEFAULT FALSE,
                media_type ENUM('none', 'image', 'video', 'audio') DEFAULT 'none',
                media_url VARCHAR(255) NULL,
                background_color VARCHAR(20) DEFAULT '#ffffff',
                text_color VARCHAR(20) DEFAULT '#000000',
                view_count INT DEFAULT 0,
                click_count INT DEFAULT 0,
                start_date DATE NOT NULL DEFAULT CURRENT_DATE,
                end_date DATE NOT NULL DEFAULT DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY),
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (sender_user_id) REFERENCES users(user_id),
                FOREIGN KEY (target_center_id) REFERENCES centers(center_id)
            )
        ");

        $success = 'تم إنشاء جدول الإعلانات المحسن بنجاح.';
    } else {
        // Check and add new columns if they don't exist

        // Check if is_public column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'is_public'
        ");
        $stmt->execute();
        $is_public_exists = (bool)$stmt->fetchColumn();

        if (!$is_public_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN is_public BOOLEAN DEFAULT FALSE");
            $success .= 'تم إضافة عمود is_public بنجاح. ';
        }

        // Check if is_featured column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'is_featured'
        ");
        $stmt->execute();
        $is_featured_exists = (bool)$stmt->fetchColumn();

        if (!$is_featured_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN is_featured BOOLEAN DEFAULT FALSE");
            $success .= 'تم إضافة عمود is_featured بنجاح. ';
        }

        // Check if media_type column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'media_type'
        ");
        $stmt->execute();
        $media_type_exists = (bool)$stmt->fetchColumn();

        if (!$media_type_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN media_type ENUM('none', 'image', 'video', 'audio') DEFAULT 'none'");
            $success .= 'تم إضافة عمود media_type بنجاح. ';
        }

        // Check if media_url column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'media_url'
        ");
        $stmt->execute();
        $media_url_exists = (bool)$stmt->fetchColumn();

        if (!$media_url_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN media_url VARCHAR(255) NULL");
            $success .= 'تم إضافة عمود media_url بنجاح. ';
        }

        // Check if image_url column exists and migrate to media_url if needed
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'image_url'
        ");
        $stmt->execute();
        $image_url_exists = (bool)$stmt->fetchColumn();

        if ($image_url_exists && $media_url_exists) {
            // Migrate data from image_url to media_url and set media_type to 'image'
            $pdo->exec("
                UPDATE announcements
                SET media_url = image_url, media_type = 'image'
                WHERE image_url IS NOT NULL AND image_url != ''
            ");
            $success .= 'تم ترحيل البيانات من عمود image_url إلى media_url بنجاح. ';
        } elseif ($image_url_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN media_url VARCHAR(255) NULL");
            $pdo->exec("
                UPDATE announcements
                SET media_url = image_url, media_type = 'image'
                WHERE image_url IS NOT NULL AND image_url != ''
            ");
            $success .= 'تم إضافة عمود media_url وترحيل البيانات من image_url بنجاح. ';
        }

        // Check if background_color column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'background_color'
        ");
        $stmt->execute();
        $background_color_exists = (bool)$stmt->fetchColumn();

        if (!$background_color_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN background_color VARCHAR(20) NULL");
            $success .= 'تم إضافة عمود background_color بنجاح. ';
        }

        // Check if text_color column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'text_color'
        ");
        $stmt->execute();
        $text_color_exists = (bool)$stmt->fetchColumn();

        if (!$text_color_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN text_color VARCHAR(20) NULL");
            $success .= 'تم إضافة عمود text_color بنجاح. ';
        }

        // Check if start_date column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'start_date'
        ");
        $stmt->execute();
        $start_date_exists = (bool)$stmt->fetchColumn();

        if (!$start_date_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN start_date DATE NOT NULL DEFAULT CURRENT_DATE");
            $success .= 'تم إضافة عمود start_date بنجاح. ';
        }

        // Check if end_date column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'end_date'
        ");
        $stmt->execute();
        $end_date_exists = (bool)$stmt->fetchColumn();

        if (!$end_date_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN end_date DATE NOT NULL DEFAULT DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)");
            $success .= 'تم إضافة عمود end_date بنجاح. ';
        }

        // Check if view_count column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'view_count'
        ");
        $stmt->execute();
        $view_count_exists = (bool)$stmt->fetchColumn();

        if (!$view_count_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN view_count INT DEFAULT 0");
            $success .= 'تم إضافة عمود view_count بنجاح. ';
        }

        // Check if click_count column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcements'
            AND COLUMN_NAME = 'click_count'
        ");
        $stmt->execute();
        $click_count_exists = (bool)$stmt->fetchColumn();

        if (!$click_count_exists) {
            $pdo->exec("ALTER TABLE announcements ADD COLUMN click_count INT DEFAULT 0");
            $success .= 'تم إضافة عمود click_count بنجاح. ';
        }

        // Create announcement_views table if it doesn't exist
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcement_views'
        ");
        $stmt->execute();
        $table_exists = (bool)$stmt->fetchColumn();

        if (!$table_exists) {
            $pdo->exec("
                CREATE TABLE announcement_views (
                    view_id INT AUTO_INCREMENT PRIMARY KEY,
                    announcement_id INT NOT NULL,
                    user_id INT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent TEXT NULL,
                    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
                )
            ");
            $success .= 'تم إنشاء جدول announcement_views بنجاح. ';
        }

        // Create announcement_clicks table if it doesn't exist
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcement_clicks'
        ");
        $stmt->execute();
        $table_exists = (bool)$stmt->fetchColumn();

        if (!$table_exists) {
            $pdo->exec("
                CREATE TABLE announcement_clicks (
                    click_id INT AUTO_INCREMENT PRIMARY KEY,
                    announcement_id INT NOT NULL,
                    user_id INT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent TEXT NULL,
                    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
                )
            ");
            $success .= 'تم إنشاء جدول announcement_clicks بنجاح. ';
        }

        if (empty($success)) {
            $success = 'جدول الإعلانات بالفعل يحتوي على الهيكل المحسن.';
        }
    }

    // Commit transaction
    $pdo->commit();

    // Set flash message
    set_flash_message('success', $success);

} catch (PDOException $e) {
    // Rollback transaction
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    $error = 'حدث خطأ أثناء تحديث جدول الإعلانات: ' . $e->getMessage();
    set_flash_message('danger', $error);
}

// Redirect to announcements page
redirect('pages/system_announcements.php');
?>
