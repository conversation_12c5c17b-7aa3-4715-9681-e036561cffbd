<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('../auth/login.php');
}

// Allow access for students and for testing purposes
if (!has_role('student')) {
    set_flash_message('warning', 'هذه الصفحة مخصصة للطلاب. أنت تشاهدها حالياً في وضع المعاينة.');
}

// Get user information
$user_id = $_SESSION['user_id'];
$error = '';
$success = '';

// For testing purposes, if the user is not a student, we'll use a sample student ID
if (!has_role('student')) {
    // Try to find a student to use for demonstration
    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE r.role_name = 'student'
            LIMIT 1
        ");
        $stmt->execute();
        $demo_student = $stmt->fetch();

        if ($demo_student) {
            $student_id = $demo_student['user_id'];
            $success = 'تم تحميل بيانات طالب للعرض التوضيحي.';
        } else {
            $student_id = $user_id; // Fallback to current user
            $error = 'لم يتم العثور على أي طالب في النظام للعرض التوضيحي.';
        }
    } catch (PDOException $e) {
        $student_id = $user_id; // Fallback to current user
        $error = 'حدث خطأ أثناء البحث عن طالب للعرض التوضيحي: ' . $e->getMessage();
    }
} else {
    $student_id = $user_id;
}

// Get student's circle enrollment
try {
    $stmt = $pdo->prepare("
        SELECT sce.enrollment_id, sce.circle_id, c.circle_name, c.level,
               t.user_id AS teacher_id, t.full_name AS teacher_name,
               cen.center_name
        FROM student_circle_enrollments sce
        JOIN circles c ON sce.circle_id = c.circle_id
        JOIN users t ON c.teacher_user_id = t.user_id
        JOIN centers cen ON c.center_id = cen.center_id
        WHERE sce.student_user_id = ? AND sce.status = 'approved'
        ORDER BY sce.enrollment_date DESC
        LIMIT 1
    ");
    $stmt->execute([$student_id]);
    $enrollment = $stmt->fetch();

    if ($enrollment) {
        $enrollment_id = $enrollment['enrollment_id'];
        $circle_id = $enrollment['circle_id'];
    } else {
        // If no enrollment found, try to get any circle for demonstration
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name, c.level,
                   t.user_id AS teacher_id, t.full_name AS teacher_name,
                   cen.center_name,
                   NULL AS enrollment_id
            FROM circles c
            JOIN users t ON c.teacher_user_id = t.user_id
            JOIN centers cen ON c.center_id = cen.center_id
            WHERE c.is_active = TRUE
            LIMIT 1
        ");
        $stmt->execute();
        $enrollment = $stmt->fetch();

        if ($enrollment) {
            $circle_id = $enrollment['circle_id'];
            $enrollment_id = null;
            if (empty($error)) {
                $error = 'أنت غير مسجل في أي حلقة حالياً. يتم عرض واجبات تجريبية.';
            }
        } else {
            $error = 'لا توجد حلقات متاحة في النظام.';
        }
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
}

// Check if student_assignments table exists
$student_assignments_exists = false;
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'student_assignments'
    ");
    $stmt->execute();
    $student_assignments_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من وجود جدول واجبات الطلاب: ' . $e->getMessage();
}

// Get student's assignments
if (isset($circle_id)) {
    try {
        // Check if is_active column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'assignments'
            AND column_name = 'is_active'
        ");
        $stmt->execute();
        $is_active_exists = (bool)$stmt->fetchColumn();

        $where_clause = $is_active_exists ? "AND a.is_active = TRUE" : "";

        if ($student_assignments_exists) {
            // If table exists, get assignments with student data
            $stmt = $pdo->prepare("
                SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                       sa.status, sa.submission_date, sa.grade, sa.feedback, sa.submission_text,
                       u.full_name AS created_by_name
                FROM assignments a
                LEFT JOIN student_assignments sa ON a.assignment_id = sa.assignment_id AND sa.student_user_id = ?
                JOIN users u ON a.created_by_user_id = u.user_id
                WHERE a.circle_id = ? $where_clause
                ORDER BY a.due_date DESC
            ");
            $stmt->execute([$student_id, $circle_id]);
        } else {
            // If table doesn't exist, just get assignments for the circle
            $stmt = $pdo->prepare("
                SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                       'pending' AS status, NULL AS submission_date, NULL AS grade, NULL AS feedback, NULL AS submission_text,
                       u.full_name AS created_by_name
                FROM assignments a
                JOIN users u ON a.created_by_user_id = u.user_id
                WHERE a.circle_id = ? $where_clause
                ORDER BY a.due_date DESC
            ");
            $stmt->execute([$circle_id]);
        }
        $assignments = $stmt->fetchAll();

        // If no assignments found, show a helpful message
        if (empty($assignments) && empty($error)) {
            $error = 'لا توجد واجبات متاحة لهذه الحلقة حالياً.';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الواجبات: ' . $e->getMessage();
    }
}

// Process assignment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_assignment']) && $student_assignments_exists) {
    $assignment_id = (int)$_POST['assignment_id'];
    $submission_text = sanitize_input($_POST['submission_text']);

    if (empty($submission_text)) {
        $error = 'يرجى كتابة إجابة الواجب';
    } else {
        try {
            // Check if assignment exists and belongs to student's circle
            $stmt = $pdo->prepare("
                SELECT a.assignment_id, a.due_date
                FROM assignments a
                WHERE a.assignment_id = ? AND a.circle_id = ?
            ");
            $stmt->execute([$assignment_id, $circle_id]);
            $assignment = $stmt->fetch();

            if (!$assignment) {
                $error = 'الواجب غير موجود أو غير مصرح لك بالوصول إليه';
            } else {
                // Check if submission already exists
                $stmt = $pdo->prepare("
                    SELECT student_assignment_id, status
                    FROM student_assignments
                    WHERE assignment_id = ? AND student_user_id = ?
                ");
                $stmt->execute([$assignment_id, $student_id]);
                $existing_submission = $stmt->fetch();

                $due_date = strtotime($assignment['due_date']);
                $now = time();
                $status = ($now > $due_date) ? 'late_submission' : 'submitted';

                if ($existing_submission) {
                    // Update existing submission
                    $stmt = $pdo->prepare("
                        UPDATE student_assignments
                        SET submission_text = ?, submission_date = NOW(), status = ?
                        WHERE assignment_id = ? AND student_user_id = ?
                    ");
                    $stmt->execute([$submission_text, $status, $assignment_id, $student_id]);
                } else {
                    // Insert new submission
                    $stmt = $pdo->prepare("
                        INSERT INTO student_assignments (assignment_id, student_user_id, submission_text, submission_date, status)
                        VALUES (?, ?, ?, NOW(), ?)
                    ");
                    $stmt->execute([$assignment_id, $student_id, $submission_text, $status]);
                }

                $success = 'تم تسليم الواجب بنجاح';

                // Refresh assignments data
                $stmt = $pdo->prepare("
                    SELECT a.assignment_id, a.title, a.description, a.due_date, a.created_at,
                           sa.status, sa.submission_date, sa.grade, sa.feedback, sa.submission_text,
                           u.full_name AS created_by_name
                    FROM assignments a
                    LEFT JOIN student_assignments sa ON a.assignment_id = sa.assignment_id AND sa.student_user_id = ?
                    JOIN users u ON a.created_by_user_id = u.user_id
                    WHERE a.circle_id = ?
                    ORDER BY a.due_date DESC
                ");
                $stmt->execute([$student_id, $circle_id]);
                $assignments = $stmt->fetchAll();
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تسليم الواجب: ' . $e->getMessage();
        }
    }
}

// Page variables
$page_title = 'واجباتي';
$active_page = 'assignments';

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true
]);
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0"><i class="fas fa-tasks me-2"></i> واجباتي</h1>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <?php if (isset($enrollment) && !empty($enrollment)): ?>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> معلومات الحلقة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم الحلقة:</strong> <?php echo $enrollment['circle_name']; ?></p>
                        <p><strong>المستوى:</strong> <?php echo $enrollment['level'] ?? 'غير محدد'; ?></p>
                        <?php if (isset($enrollment['center_name'])): ?>
                        <p><strong>المركز:</strong> <?php echo $enrollment['center_name']; ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المعلم:</strong> <?php echo $enrollment['teacher_name']; ?></p>
                        <?php if (isset($enrollment['teacher_id'])): ?>
                        <p>
                            <a href="<?php echo get_root_url(); ?>pages/teacher_contact.php?id=<?php echo $enrollment['teacher_id']; ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-envelope me-1"></i> التواصل مع المعلم
                            </a>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!$student_assignments_exists): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> جدول واجبات الطلاب غير موجود. لن تتمكن من تسليم الواجبات حالياً.
                يرجى التواصل مع إدارة النظام.
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i> قائمة الواجبات</h5>
            </div>
            <div class="card-body">
                <?php if (isset($assignments) && !empty($assignments)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover datatable">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>الوصف</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الحالة</th>
                                    <th>التقييم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($assignments as $assignment): ?>
                                    <tr>
                                        <td><?php echo $assignment['title']; ?></td>
                                        <td><?php echo substr($assignment['description'], 0, 100) . (strlen($assignment['description']) > 100 ? '...' : ''); ?></td>
                                        <td>
                                            <?php
                                            $due_date = strtotime($assignment['due_date']);
                                            $now = time();
                                            $days_diff = round(($due_date - $now) / (60 * 60 * 24));

                                            echo date('Y-m-d', $due_date);

                                            if ($days_diff < 0) {
                                                echo ' <span class="badge bg-danger">انتهى</span>';
                                            } elseif ($days_diff == 0) {
                                                echo ' <span class="badge bg-warning">اليوم</span>';
                                            } else {
                                                echo ' <span class="badge bg-info">متبقي ' . $days_diff . ' يوم</span>';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status = $assignment['status'] ?? 'pending';
                                            switch ($status) {
                                                case 'submitted':
                                                    echo '<span class="badge bg-success">تم التسليم</span>';
                                                    break;
                                                case 'late_submission':
                                                    echo '<span class="badge bg-warning">تسليم متأخر</span>';
                                                    break;
                                                case 'graded':
                                                    echo '<span class="badge bg-primary">تم التقييم</span>';
                                                    break;
                                                case 'not_submitted':
                                                    echo '<span class="badge bg-danger">لم يتم التسليم</span>';
                                                    break;
                                                default:
                                                    echo '<span class="badge bg-secondary">معلق</span>';
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($assignment['grade'])): ?>
                                                <span class="badge bg-success"><?php echo $assignment['grade']; ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">لم يتم التقييم</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo get_root_url(); ?>pages/assignment_details.php?id=<?php echo $assignment['assignment_id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <?php if ($student_assignments_exists && ($status === 'pending' || $status === 'submitted' || $status === 'late_submission')): ?>
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#submitModal<?php echo $assignment['assignment_id']; ?>">
                                                    <i class="fas fa-upload"></i>
                                                    <?php echo ($status === 'pending') ? 'تسليم' : 'تعديل التسليم'; ?>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا توجد واجبات متاحة حالياً.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <h4 class="alert-heading">لم يتم تسجيلك في أي حلقة بعد!</h4>
            <p>يبدو أنك لم تنضم إلى أي حلقة تحفيظ حتى الآن. يرجى التواصل مع إدارة المركز للتسجيل في إحدى الحلقات.</p>
        </div>
    <?php endif; ?>
</div>

<?php
// Include footer template
include_template('footer');
?>
