/* Custom styles for Quran Circle Management System */

/* General styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Arabic font - Import Cairo font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

body, h1, h2, h3, h4, h5, h6, p, a, button, input, select, textarea {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard cards */
.dashboard-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-bottom: 20px;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card .card-header {
    border-radius: 10px 10px 0 0;
    font-weight: bold;
}

.dashboard-card .card-body {
    padding: 20px;
}

.dashboard-card .card-footer {
    border-radius: 0 0 10px 10px;
    background-color: rgba(0, 0, 0, 0.03);
}

/* Dashboard stats */
.stats-card {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.stats-card i {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.stats-card .stats-value {
    font-size: 1.8rem;
    font-weight: bold;
}

.stats-card .stats-label {
    font-size: 1rem;
    color: #6c757d;
}

/* Colors for different stats cards */
.stats-primary {
    background-color: #cfe2ff;
    color: #084298;
}

.stats-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.stats-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.stats-danger {
    background-color: #f8d7da;
    color: #842029;
}

.stats-info {
    background-color: #cff4fc;
    color: #055160;
}

/* Tables */
.table-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.table-container .table {
    margin-bottom: 0;
}

/* Forms */
.form-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.form-label {
    font-weight: 600;
}

/* Login and registration forms */
.auth-form {
    max-width: 500px;
    margin: 50px auto;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.auth-form .form-title {
    text-align: center;
    margin-bottom: 30px;
    color: #0d6efd;
}

.auth-form .form-footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

/* Profile page */
.profile-header {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.profile-picture {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Progress bars */
.progress {
    height: 20px;
    margin-bottom: 10px;
}

.progress-label {
    font-weight: 600;
    margin-bottom: 5px;
}

/* Attendance status indicators */
.attendance-present {
    color: #198754;
}

.attendance-absent {
    color: #dc3545;
}

.attendance-excused {
    color: #fd7e14;
}

.attendance-late {
    color: #ffc107;
}

/* WhatsApp icon styles */
.whatsapp-icon {
    color: #25D366 !important;
    transition: all 0.3s ease;
}

.whatsapp-icon:hover {
    color: #128C7E !important;
    transform: scale(1.1);
}

.btn-outline-light .fab.fa-whatsapp {
    color: #25D366;
    font-size: 1.1rem;
}

.btn-outline-light:hover .fab.fa-whatsapp {
    color: #fff;
}

/* WhatsApp status badges */
.whatsapp-status-badge {
    font-size: 6px !important;
    padding: 2px 4px;
}

/* WhatsApp settings page styles */
.whatsapp-config-card {
    border-left: 4px solid #25D366;
}

.whatsapp-test-card {
    border-left: 4px solid #0d6efd;
}

.whatsapp-logs-card {
    border-left: 4px solid #6c757d;
    max-height: 400px;
    overflow-y: auto;
}

/* Quick Access Menu Styles */
.quick-access-menu {
    min-width: 280px;
}

.quick-access-menu .dropdown-header {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 8px 16px;
    margin: 0;
    border-bottom: 1px solid #dee2e6;
}

.quick-access-menu .dropdown-item {
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.quick-access-menu .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(-2px);
}

.quick-access-menu .dropdown-item i {
    width: 20px;
    text-align: center;
    color: #6c757d;
}

.quick-access-menu .dropdown-divider {
    margin: 4px 0;
}

/* Quick Access Button */
.btn-quick-access {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    transition: all 0.3s ease;
}

.btn-quick-access:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-quick-access i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .quick-access-menu {
        min-width: 250px;
    }

    .quick-access-menu .dropdown-item {
        font-size: 14px;
        padding: 6px 12px;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-picture {
        width: 100px;
        height: 100px;
    }

    .stats-card i {
        font-size: 2rem;
    }

    .stats-card .stats-value {
        font-size: 1.5rem;
    }
}
