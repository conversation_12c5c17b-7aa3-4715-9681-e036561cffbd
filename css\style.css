/* Custom styles for Quran Circle Management System */

/* General styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Arabic font */
@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
}

body, h1, h2, h3, h4, h5, h6, p, a, button, input, select, textarea {
    font-family: 'Amir<PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard cards */
.dashboard-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-bottom: 20px;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card .card-header {
    border-radius: 10px 10px 0 0;
    font-weight: bold;
}

.dashboard-card .card-body {
    padding: 20px;
}

.dashboard-card .card-footer {
    border-radius: 0 0 10px 10px;
    background-color: rgba(0, 0, 0, 0.03);
}

/* Dashboard stats */
.stats-card {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.stats-card i {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.stats-card .stats-value {
    font-size: 1.8rem;
    font-weight: bold;
}

.stats-card .stats-label {
    font-size: 1rem;
    color: #6c757d;
}

/* Colors for different stats cards */
.stats-primary {
    background-color: #cfe2ff;
    color: #084298;
}

.stats-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.stats-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.stats-danger {
    background-color: #f8d7da;
    color: #842029;
}

.stats-info {
    background-color: #cff4fc;
    color: #055160;
}

/* Tables */
.table-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.table-container .table {
    margin-bottom: 0;
}

/* Forms */
.form-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.form-label {
    font-weight: 600;
}

/* Login and registration forms */
.auth-form {
    max-width: 500px;
    margin: 50px auto;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.auth-form .form-title {
    text-align: center;
    margin-bottom: 30px;
    color: #0d6efd;
}

.auth-form .form-footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

/* Profile page */
.profile-header {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.profile-picture {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Progress bars */
.progress {
    height: 20px;
    margin-bottom: 10px;
}

.progress-label {
    font-weight: 600;
    margin-bottom: 5px;
}

/* Attendance status indicators */
.attendance-present {
    color: #198754;
}

.attendance-absent {
    color: #dc3545;
}

.attendance-excused {
    color: #fd7e14;
}

.attendance-late {
    color: #ffc107;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-picture {
        width: 100px;
        height: 100px;
    }
    
    .stats-card i {
        font-size: 2rem;
    }
    
    .stats-card .stats-value {
        font-size: 1.5rem;
    }
}
