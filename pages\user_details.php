<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Check if user ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف المستخدم غير صحيح');
    redirect('pages/users.php');
}

$user_id = (int)$_GET['id'];
$error = '';
$success = '';

// Get user information
try {
    $stmt = $pdo->prepare("
        SELECT u.*, c.center_name, r.role_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        set_flash_message('danger', 'المستخدم غير موجود');
        redirect('pages/users.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المستخدم: ' . $e->getMessage();
}

// Check if columns exist
try {
    // Check if specialization column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'specialization'
    ");
    $stmt->execute();
    $specialization_exists = (bool)$stmt->fetchColumn();
    
    // Check if qualifications column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'qualifications'
    ");
    $stmt->execute();
    $qualifications_exists = (bool)$stmt->fetchColumn();
    
    // Check if birth_date column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'birth_date'
    ");
    $stmt->execute();
    $birth_date_exists = (bool)$stmt->fetchColumn();
    
    // Check if gender column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'gender'
    ");
    $stmt->execute();
    $gender_exists = (bool)$stmt->fetchColumn();
    
    // Check if address column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'address'
    ");
    $stmt->execute();
    $address_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من أعمدة الجدول: ' . $e->getMessage();
}

// Get additional information based on role
$additional_info = [];

if ($user['role_name'] === 'teacher') {
    // Get teacher's circles
    try {
        $stmt = $pdo->prepare("
            SELECT c.*, 
                   (SELECT COUNT(*) FROM student_circle_enrollments sce WHERE sce.circle_id = c.circle_id) AS student_count
            FROM circles c
            WHERE c.teacher_user_id = ?
            ORDER BY c.circle_name
        ");
        $stmt->execute([$user_id]);
        $additional_info['circles'] = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
    }
} elseif ($user['role_name'] === 'student') {
    // Get student's enrollments
    try {
        $stmt = $pdo->prepare("
            SELECT sce.*, c.circle_name, c.level, u.full_name AS teacher_name
            FROM student_circle_enrollments sce
            JOIN circles c ON sce.circle_id = c.circle_id
            JOIN users u ON c.teacher_user_id = u.user_id
            WHERE sce.student_user_id = ?
            ORDER BY sce.enrollment_date DESC
        ");
        $stmt->execute([$user_id]);
        $additional_info['enrollments'] = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات التسجيلات: ' . $e->getMessage();
    }
    
    // Get student's parents
    try {
        $stmt = $pdo->prepare("
            SELECT psr.*, u.full_name, u.email, u.phone_number, u.is_active
            FROM parent_student_relations psr
            JOIN users u ON psr.parent_user_id = u.user_id
            WHERE psr.student_user_id = ?
        ");
        $stmt->execute([$user_id]);
        $additional_info['parents'] = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات أولياء الأمور: ' . $e->getMessage();
    }
} elseif ($user['role_name'] === 'parent') {
    // Get parent's children
    try {
        $stmt = $pdo->prepare("
            SELECT psr.*, u.full_name, u.email, u.is_active
            FROM parent_student_relations psr
            JOIN users u ON psr.student_user_id = u.user_id
            WHERE psr.parent_user_id = ?
        ");
        $stmt->execute([$user_id]);
        $additional_info['children'] = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الأبناء: ' . $e->getMessage();
    }
} elseif ($user['role_name'] === 'center_admin') {
    // Get center statistics
    try {
        // Count teachers
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS count
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE r.role_name = 'teacher' AND u.center_id = ?
        ");
        $stmt->execute([$user['center_id']]);
        $additional_info['teacher_count'] = $stmt->fetch()['count'];
        
        // Count students
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS count
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE r.role_name = 'student' AND u.center_id = ?
        ");
        $stmt->execute([$user['center_id']]);
        $additional_info['student_count'] = $stmt->fetch()['count'];
        
        // Count circles
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS count
            FROM circles
            WHERE center_id = ?
        ");
        $stmt->execute([$user['center_id']]);
        $additional_info['circle_count'] = $stmt->fetch()['count'];
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع إحصائيات المركز: ' . $e->getMessage();
    }
}

// Page variables
$page_title = 'تفاصيل المستخدم: ' . $user['full_name'];
$active_page = 'users';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => 'system_owner_dashboard.php'],
    ['title' => 'المستخدمين', 'url' => 'users.php'],
    ['title' => $user['full_name']]
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="fas fa-user me-2"></i>
        <?php echo $user['full_name']; ?>
    </h1>
    <div>
        <a href="edit_user.php?id=<?php echo $user_id; ?>" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i> تعديل
        </a>
        <a href="users.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <!-- User Information -->
    <div class="col-md-4 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> معلومات المستخدم</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <?php if (!empty($user['profile_picture_url'])): ?>
                        <img src="<?php echo get_root_url() . $user['profile_picture_url']; ?>" alt="صورة المستخدم" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    <?php else: ?>
                        <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                            <i class="fas fa-user fa-5x text-secondary"></i>
                        </div>
                    <?php endif; ?>
                    <h4><?php echo $user['full_name']; ?></h4>
                    <p class="text-muted">@<?php echo $user['username']; ?></p>
                    <span class="badge <?php echo $user['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                        <?php echo $user['is_active'] ? 'نشط' : 'غير نشط'; ?>
                    </span>
                    <span class="badge bg-info">
                        <?php echo $user['role_name']; ?>
                    </span>
                </div>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-building me-2"></i> المركز</h6>
                    <p><?php echo $user['center_name'] ?? 'غير محدد'; ?></p>
                </div>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-envelope me-2"></i> البريد الإلكتروني</h6>
                    <p><?php echo $user['email']; ?></p>
                </div>
                
                <?php if (!empty($user['phone_number'])): ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-phone me-2"></i> رقم الهاتف</h6>
                    <p><?php echo $user['phone_number']; ?></p>
                </div>
                <?php endif; ?>
                
                <?php if ($specialization_exists && !empty($user['specialization'])): ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-graduation-cap me-2"></i> التخصص</h6>
                    <p><?php echo $user['specialization']; ?></p>
                </div>
                <?php endif; ?>
                
                <?php if ($qualifications_exists && !empty($user['qualifications'])): ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-certificate me-2"></i> المؤهلات والخبرات</h6>
                    <p><?php echo nl2br($user['qualifications']); ?></p>
                </div>
                <?php endif; ?>
                
                <?php if ($birth_date_exists && !empty($user['birth_date'])): ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-birthday-cake me-2"></i> تاريخ الميلاد</h6>
                    <p><?php echo date('Y-m-d', strtotime($user['birth_date'])); ?></p>
                </div>
                <?php endif; ?>
                
                <?php if ($gender_exists && !empty($user['gender'])): ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-venus-mars me-2"></i> الجنس</h6>
                    <p><?php echo $user['gender']; ?></p>
                </div>
                <?php endif; ?>
                
                <?php if ($address_exists && !empty($user['address'])): ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-map-marker-alt me-2"></i> العنوان</h6>
                    <p><?php echo nl2br($user['address']); ?></p>
                </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-calendar-alt me-2"></i> تاريخ التسجيل</h6>
                    <p><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Additional Information -->
    <div class="col-md-8 mb-4">
        <?php if ($user['role_name'] === 'teacher'): ?>
            <!-- Teacher's Circles -->
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> الحلقات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($additional_info['circles'])): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا توجد حلقات مسجلة لهذا المعلم.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover datatable">
                                <thead>
                                    <tr>
                                        <th>اسم الحلقة</th>
                                        <th>المستوى</th>
                                        <th>عدد الطلاب</th>
                                        <th>مواعيد الحلقة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($additional_info['circles'] as $circle): ?>
                                        <tr>
                                            <td><?php echo $circle['circle_name']; ?></td>
                                            <td><?php echo $circle['level']; ?></td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo $circle['student_count']; ?> / <?php echo $circle['max_students']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $circle['schedule_details']; ?></td>
                                            <td>
                                                <span class="badge <?php echo $circle['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $circle['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="circle_details.php?id=<?php echo $circle['circle_id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php elseif ($user['role_name'] === 'student'): ?>
            <!-- Student's Enrollments -->
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> الحلقات المسجل فيها</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($additional_info['enrollments'])): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا توجد حلقات مسجل فيها هذا الطالب.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover datatable">
                                <thead>
                                    <tr>
                                        <th>اسم الحلقة</th>
                                        <th>المستوى</th>
                                        <th>المعلم</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($additional_info['enrollments'] as $enrollment): ?>
                                        <tr>
                                            <td><?php echo $enrollment['circle_name']; ?></td>
                                            <td><?php echo $enrollment['level']; ?></td>
                                            <td><?php echo $enrollment['teacher_name']; ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($enrollment['enrollment_date'])); ?></td>
                                            <td>
                                                <?php
                                                $status = $enrollment['status'];
                                                $badge_class = '';
                                                $status_text = '';

                                                switch ($status) {
                                                    case 'approved':
                                                        $badge_class = 'bg-success';
                                                        $status_text = 'مقبول';
                                                        break;
                                                    case 'pending':
                                                        $badge_class = 'bg-warning text-dark';
                                                        $status_text = 'قيد الانتظار';
                                                        break;
                                                    case 'rejected':
                                                        $badge_class = 'bg-danger';
                                                        $status_text = 'مرفوض';
                                                        break;
                                                    case 'withdrawn':
                                                        $badge_class = 'bg-secondary';
                                                        $status_text = 'منسحب';
                                                        break;
                                                    case 'completed':
                                                        $badge_class = 'bg-info';
                                                        $status_text = 'مكتمل';
                                                        break;
                                                    default:
                                                        $badge_class = 'bg-secondary';
                                                        $status_text = $status;
                                                }
                                                ?>
                                                <span class="badge <?php echo $badge_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                            <td>
                                                <a href="circle_details.php?id=<?php echo $enrollment['circle_id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض الحلقة
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Student's Parents -->
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-user-friends me-2"></i> أولياء الأمور</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($additional_info['parents'])): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا يوجد أولياء أمور مرتبطين بهذا الطالب.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم ولي الأمر</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>نوع العلاقة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($additional_info['parents'] as $parent): ?>
                                        <tr>
                                            <td><?php echo $parent['full_name']; ?></td>
                                            <td><?php echo $parent['email']; ?></td>
                                            <td><?php echo $parent['phone_number']; ?></td>
                                            <td><?php echo $parent['relation_type']; ?></td>
                                            <td>
                                                <span class="badge <?php echo $parent['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $parent['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="user_details.php?id=<?php echo $parent['parent_user_id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php elseif ($user['role_name'] === 'parent'): ?>
            <!-- Parent's Children -->
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-child me-2"></i> الأبناء</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($additional_info['children'])): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا يوجد أبناء مرتبطين بهذا المستخدم.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الطالب</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>نوع العلاقة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($additional_info['children'] as $child): ?>
                                        <tr>
                                            <td><?php echo $child['full_name']; ?></td>
                                            <td><?php echo $child['email']; ?></td>
                                            <td><?php echo $child['relation_type']; ?></td>
                                            <td>
                                                <span class="badge <?php echo $child['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $child['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="user_details.php?id=<?php echo $child['student_user_id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php elseif ($user['role_name'] === 'center_admin'): ?>
            <!-- Center Admin Statistics -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card bg-primary text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-user-tie fa-3x mb-3"></i>
                            <h2 class="card-title"><?php echo $additional_info['teacher_count'] ?? '0'; ?></h2>
                            <p class="card-text">المعلمين</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <div class="card bg-success text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-user-graduate fa-3x mb-3"></i>
                            <h2 class="card-title"><?php echo $additional_info['student_count'] ?? '0'; ?></h2>
                            <p class="card-text">الطلاب</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-3">
                    <div class="card bg-warning text-dark h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-circle fa-3x mb-3"></i>
                            <h2 class="card-title"><?php echo $additional_info['circle_count'] ?? '0'; ?></h2>
                            <p class="card-text">الحلقات</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-building me-2"></i> معلومات المركز</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> هذا المستخدم هو مدير لمركز <strong><?php echo $user['center_name']; ?></strong>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-3">
                        <a href="center_details.php?id=<?php echo $user['center_id']; ?>" class="btn btn-primary">
                            <i class="fas fa-building me-1"></i> عرض تفاصيل المركز
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer template
include_template('footer');
?>
