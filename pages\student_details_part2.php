<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطالب - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .profile-header {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .profile-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .attendance-badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
        .quality-badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if ($role_name === 'parent'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="parent_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="my_children.php">أبنائي</a>
                            </li>
                        <?php elseif ($role_name === 'teacher'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="teacher_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="my_circles.php">حلقاتي</a>
                            </li>
                        <?php elseif ($role_name === 'center_admin'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="center_admin_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="students.php">الطلاب</a>
                            </li>
                        <?php elseif ($role_name === 'system_owner'): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="system_owner_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="users.php">المستخدمين</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="messages.php">الرسائل</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
