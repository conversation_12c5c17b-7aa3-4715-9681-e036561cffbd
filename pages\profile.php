<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('auth/login.php');
}

$user_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Get user information
try {
    $stmt = $pdo->prepare("
        SELECT u.*, r.role_name, c.center_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    if (!$user) {
        set_flash_message('danger', 'لم يتم العثور على المستخدم');
        redirect('index.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المستخدم: ' . $e->getMessage();
}

// Process profile update form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($full_name) || empty($email)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صالح';
    } else {
        try {
            // Check if email already exists (for another user)
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
            $stmt->execute([$email, $user_id]);
            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد إلكتروني آخر';
            } else {
                // Start transaction
                $pdo->beginTransaction();

                // Update basic information
                $stmt = $pdo->prepare("
                    UPDATE users
                    SET full_name = ?, email = ?, phone_number = ?
                    WHERE user_id = ?
                ");
                $stmt->execute([$full_name, $email, $phone_number, $user_id]);

                // Update password if provided
                if (!empty($current_password) && !empty($new_password)) {
                    if ($new_password !== $confirm_password) {
                        $error = 'كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقين';
                    } elseif (strlen($new_password) < 6) {
                        $error = 'يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل';
                    } elseif (!password_verify($current_password, $user['password_hash'])) {
                        $error = 'كلمة المرور الحالية غير صحيحة';
                    } else {
                        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);

                        $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE user_id = ?");
                        $stmt->execute([$password_hash, $user_id]);
                    }
                }

                // Handle profile picture upload
                if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                    $max_size = 2 * 1024 * 1024; // 2MB

                    $file_type = $_FILES['profile_picture']['type'];
                    $file_size = $_FILES['profile_picture']['size'];

                    if (!in_array($file_type, $allowed_types)) {
                        $error = 'نوع الملف غير مدعوم. يرجى استخدام JPEG أو PNG أو GIF';
                    } elseif ($file_size > $max_size) {
                        $error = 'حجم الملف كبير جدًا. الحد الأقصى هو 2 ميجابايت';
                    } else {
                        $upload_dir = '../' . PROFILE_PICS_DIR;

                        // Create directory if it doesn't exist
                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0777, true);
                        }

                        $file_name = $user_id . '_' . time() . '_' . basename($_FILES['profile_picture']['name']);
                        $file_path = $upload_dir . $file_name;

                        if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $file_path)) {
                            // Delete old profile picture if exists
                            if (!empty($user['profile_picture_url'])) {
                                $old_file = '../' . $user['profile_picture_url'];
                                if (file_exists($old_file)) {
                                    unlink($old_file);
                                }
                            }

                            $profile_picture_url = PROFILE_PICS_DIR . $file_name;

                            $stmt = $pdo->prepare("UPDATE users SET profile_picture_url = ? WHERE user_id = ?");
                            $stmt->execute([$profile_picture_url, $user_id]);
                        } else {
                            $error = 'حدث خطأ أثناء رفع الصورة';
                        }
                    }
                }

                if (empty($error)) {
                    // Commit transaction
                    $pdo->commit();

                    // Update session variables
                    $_SESSION['full_name'] = $full_name;
                    $_SESSION['email'] = $email;

                    $success = 'تم تحديث الملف الشخصي بنجاح';

                    // Refresh user data
                    $stmt = $pdo->prepare("
                        SELECT u.*, r.role_name, c.center_name
                        FROM users u
                        LEFT JOIN roles r ON u.role_id = r.role_id
                        LEFT JOIN centers c ON u.center_id = c.center_id
                        WHERE u.user_id = ?
                    ");
                    $stmt->execute([$user_id]);
                    $user = $stmt->fetch();
                } else {
                    // Rollback transaction
                    $pdo->rollBack();
                }
            }
        } catch (PDOException $e) {
            // Rollback transaction
            $pdo->rollBack();

            $error = 'حدث خطأ أثناء تحديث الملف الشخصي: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <?php include_once '../includes/header.php'; ?>

    <div class="container py-5">
        <div class="row">
            <div class="col-lg-4">
                <div class="card profile-header mb-4">
                    <div class="card-body text-center">
                        <img src="<?php echo !empty($user['profile_picture_url']) ? '../' . $user['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>"
                             class="profile-picture mb-3" alt="صورة الملف الشخصي">
                        <h4><?php echo $user['full_name']; ?></h4>
                        <p class="text-muted">
                            <?php
                            $role_display = '';
                            switch ($user['role_name']) {
                                case 'system_owner':
                                    $role_display = 'صاحب النظام';
                                    break;
                                case 'center_admin':
                                    $role_display = 'مدير مركز';
                                    break;
                                case 'teacher':
                                    $role_display = 'معلم';
                                    break;
                                case 'student':
                                    $role_display = 'طالب';
                                    break;
                                case 'parent':
                                    $role_display = 'ولي أمر';
                                    break;
                                default:
                                    $role_display = $user['role_name'];
                            }
                            echo $role_display;
                            ?>
                        </p>
                        <?php if (!empty($user['center_name'])): ?>
                            <p><i class="fas fa-building me-2"></i> <?php echo $user['center_name']; ?></p>
                        <?php endif; ?>
                        <div class="d-flex justify-content-center">
                            <a href="messages.php" class="btn btn-outline-primary me-2">
                                <i class="fas fa-envelope me-1"></i> الرسائل
                            </a>
                            <a href="../auth/logout.php" class="btn btn-outline-danger">
                                <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">معلومات الاتصال</h5>
                    </div>
                    <div class="card-body">
                        <p><i class="fas fa-envelope me-2"></i> <?php echo $user['email']; ?></p>
                        <p><i class="fas fa-phone me-2"></i> <?php echo !empty($user['phone_number']) ? $user['phone_number'] : 'غير متوفر'; ?></p>
                    </div>
                </div>
            </div>

            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">تعديل الملف الشخصي</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>

                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php endif; ?>

                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo $user['full_name']; ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo $user['email']; ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="phone_number" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="phone_number" name="phone_number" value="<?php echo $user['phone_number']; ?>">
                            </div>

                            <div class="mb-3">
                                <label for="profile_picture" class="form-label">صورة الملف الشخصي</label>
                                <input type="file" class="form-control" id="profile_picture" name="profile_picture">
                                <small class="text-muted">الحد الأقصى للحجم: 2 ميجابايت. الأنواع المدعومة: JPEG، PNG، GIF</small>
                            </div>

                            <hr>

                            <h5 class="mb-3">تغيير كلمة المرور</h5>

                            <div class="mb-3">
                                <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                            </div>

                            <div class="mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                                <small class="text-muted">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include_once '../includes/footer.php'; ?>
</body>
</html>
