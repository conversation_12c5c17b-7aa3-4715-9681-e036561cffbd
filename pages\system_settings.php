<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';

// Create settings table and initialize default settings
create_settings_table($pdo);
initialize_default_settings($pdo);

// Get all settings grouped by category
$grouped_settings = get_all_settings($pdo);

// Handle logo upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
    try {
        $upload_dir = '../assets/images/';

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $file_extension = pathinfo($_FILES['logo_file']['name'], PATHINFO_EXTENSION);
        $new_filename = 'logo_' . time() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;
        $logo_url = 'assets/images/' . $new_filename;

        if (move_uploaded_file($_FILES['logo_file']['tmp_name'], $upload_path)) {
            // Update setting in database
            update_setting($pdo, 'logo_url', $logo_url);
            $success = 'تم تحميل الشعار وحفظ الإعدادات بنجاح';

            // Refresh settings
            $grouped_settings = get_all_settings($pdo);
        } else {
            $error = 'حدث خطأ أثناء تحميل الشعار';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء تحديث إعدادات الشعار: ' . $e->getMessage();
    }
}

// Handle home image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['home_image_file']) && $_FILES['home_image_file']['error'] === UPLOAD_ERR_OK) {
    try {
        $upload_dir = '../assets/images/';

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $file_extension = pathinfo($_FILES['home_image_file']['name'], PATHINFO_EXTENSION);
        $new_filename = 'home_image_' . time() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;
        $home_image_url = 'assets/images/' . $new_filename;

        if (move_uploaded_file($_FILES['home_image_file']['tmp_name'], $upload_path)) {
            // Update setting in database
            update_setting($pdo, 'home_image_url', $home_image_url);
            $success = 'تم تحميل الصورة الرئيسية وحفظ الإعدادات بنجاح';

            // Refresh settings
            $grouped_settings = get_all_settings($pdo);
        } else {
            $error = 'حدث خطأ أثناء تحميل الصورة الرئيسية';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء تحديث إعدادات الصورة الرئيسية: ' . $e->getMessage();
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    try {
        $pdo->beginTransaction();

        foreach ($_POST as $key => $value) {
            // Skip non-setting fields and submit button
            if (strpos($key, 'setting_') !== 0 || $key === 'save_settings') {
                continue;
            }

            $setting_key = substr($key, 8); // Remove 'setting_' prefix
            update_setting($pdo, $setting_key, $value);
        }

        $pdo->commit();
        $success = 'تم حفظ الإعدادات بنجاح';

        // Refresh settings after update
        $grouped_settings = get_all_settings($pdo);
    } catch (PDOException $e) {
        $pdo->rollBack();
        $error = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// Group names in Arabic
$group_names = [
    'general' => 'إعدادات عامة',
    'registration' => 'إعدادات التسجيل',
    'memorization' => 'إعدادات الحفظ',
    'appearance' => 'إعدادات المظهر',
    'system' => 'إعدادات النظام',
];

// Helper function to get setting value
function get_setting_value($settings, $group, $key_index, $default = '') {
    if (!isset($settings[$group]) || !isset($settings[$group][$key_index])) {
        return $default;
    }
    return $settings[$group][$key_index]['setting_value'];
}

// Helper function to find setting by key
function find_setting_by_key($settings, $group, $key) {
    if (!isset($settings[$group])) {
        return null;
    }

    foreach ($settings[$group] as $setting) {
        if ($setting['setting_key'] === $key) {
            return $setting['setting_value'];
        }
    }

    return null;
}

// Get specific settings
$logo_url = find_setting_by_key($grouped_settings, 'appearance', 'logo_url') ?? 'assets/images/logo.png';
$primary_color = find_setting_by_key($grouped_settings, 'appearance', 'primary_color') ?? '#0d6efd';
$home_image_url = find_setting_by_key($grouped_settings, 'appearance', 'home_image_url') ?? 'assets/images/quran-study.jpg';

// Page title
$page_title = 'إعدادات النظام';

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-cogs me-2"></i> إعدادات النظام</h1>
        <a href="system_owner_dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
        </a>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0"><i class="fas fa-cogs me-2"></i> إعدادات النظام</h5>
        </div>
        <div class="card-body">
            <ul class="nav nav-tabs mb-3" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="appearance-tab" data-bs-toggle="tab"
                            data-bs-target="#appearance" type="button" role="tab"
                            aria-controls="appearance" aria-selected="true">
                        <i class="fas fa-palette me-1"></i> إعدادات المظهر
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="general-tab" data-bs-toggle="tab"
                            data-bs-target="#general" type="button" role="tab"
                            aria-controls="general" aria-selected="false">
                        <i class="fas fa-cog me-1"></i> إعدادات عامة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="memorization-tab" data-bs-toggle="tab"
                            data-bs-target="#memorization" type="button" role="tab"
                            aria-controls="memorization" aria-selected="false">
                        <i class="fas fa-book-quran me-1"></i> إعدادات الحفظ
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="registration-tab" data-bs-toggle="tab"
                            data-bs-target="#registration" type="button" role="tab"
                            aria-controls="registration" aria-selected="false">
                        <i class="fas fa-user-plus me-1"></i> إعدادات التسجيل
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab"
                            data-bs-target="#system" type="button" role="tab"
                            aria-controls="system" aria-selected="false">
                        <i class="fas fa-server me-1"></i> إعدادات النظام
                    </button>
                </li>
            </ul>

            <form method="POST" enctype="multipart/form-data">
                <div class="tab-content" id="settingsTabsContent">
                    <!-- Appearance Tab -->
                    <div class="tab-pane fade show active" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="setting_logo_url" class="form-label fw-bold">رابط الشعار</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-image"></i></span>
                                        <input type="text" class="form-control" id="setting_logo_url" name="setting_logo_url"
                                            value="<?php echo $logo_url; ?>" readonly>
                                    </div>
                                    <div class="form-text">مسار الشعار بالنسبة لمجلد الموقع الرئيسي</div>
                                </div>

                                <div class="mb-4">
                                    <label for="setting_primary_color" class="form-label fw-bold">اللون الرئيسي</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="setting_primary_color" name="setting_primary_color"
                                            value="<?php echo $primary_color; ?>">
                                        <input type="text" class="form-control" value="<?php echo $primary_color; ?>"
                                            id="color_text_primary_color" readonly>
                                    </div>
                                    <div class="form-text">اللون الرئيسي المستخدم في الموقع</div>
                                </div>

                                <div class="mb-4">
                                    <label for="setting_home_image_url" class="form-label fw-bold">رابط الصورة الرئيسية</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-image"></i></span>
                                        <input type="text" class="form-control" id="setting_home_image_url" name="setting_home_image_url"
                                            value="<?php echo $home_image_url; ?>" readonly>
                                    </div>
                                    <div class="form-text">مسار الصورة الرئيسية بالنسبة لمجلد الموقع الرئيسي</div>
                                </div>

                                <div class="mb-4">
                                    <label for="setting_footer_text" class="form-label fw-bold">نص التذييل</label>
                                    <textarea class="form-control" id="setting_footer_text" name="setting_footer_text" rows="3"><?php echo find_setting_by_key($grouped_settings, 'appearance', 'footer_text') ?? 'جميع الحقوق محفوظة © نظام إدارة حلقات تحفيظ القرآن'; ?></textarea>
                                    <div class="form-text">النص الذي يظهر في تذييل الصفحة</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">معاينة الشعار</h6>
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="../<?php echo $logo_url; ?>"
                                            alt="شعار الموقع" class="img-fluid mb-2" style="max-height: 100px;" id="logo_preview">
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('logo_file').click()">
                                                <i class="fas fa-upload me-1"></i> تحميل شعار جديد
                                            </button>
                                            <input type="file" id="logo_file" name="logo_file" style="display: none;" accept="image/*" onchange="previewImage(this, 'logo_preview')">
                                        </div>
                                    </div>
                                </div>

                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">معاينة الصورة الرئيسية</h6>
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="../<?php echo $home_image_url; ?>"
                                            alt="الصورة الرئيسية" class="img-fluid mb-2" style="max-height: 150px;" id="home_image_preview">
                                        <div class="mt-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('home_image_file').click()">
                                                <i class="fas fa-upload me-1"></i> تحميل صورة جديدة
                                            </button>
                                            <input type="file" id="home_image_file" name="home_image_file" style="display: none;" accept="image/*" onchange="previewImage(this, 'home_image_preview')">
                                        </div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="card-title mb-0">معاينة اللون</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex flex-column gap-2">
                                            <div class="p-3 rounded text-white" id="color_preview"
                                                style="background-color: <?php echo $primary_color; ?>">
                                                عنوان بلون النظام الرئيسي
                                            </div>
                                            <button type="button" class="btn" id="btn_preview"
                                                style="background-color: <?php echo $primary_color; ?>; color: white;">
                                                زر بلون النظام الرئيسي
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- General Tab -->
                    <div class="tab-pane fade" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="setting_site_name" class="form-label fw-bold">اسم الموقع</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                    <input type="text" class="form-control" id="setting_site_name" name="setting_site_name"
                                        value="<?php echo find_setting_by_key($grouped_settings, 'general', 'site_name') ?? 'نظام إدارة حلقات تحفيظ القرآن'; ?>">
                                </div>
                                <div class="form-text">اسم الموقع الذي يظهر في العنوان والترويسة</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_admin_email" class="form-label fw-bold">البريد الإلكتروني للمسؤول</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="setting_admin_email" name="setting_admin_email"
                                        value="<?php echo find_setting_by_key($grouped_settings, 'general', 'admin_email') ?? '<EMAIL>'; ?>">
                                </div>
                                <div class="form-text">البريد الإلكتروني الرسمي لمسؤول النظام</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_contact_phone" class="form-label fw-bold">رقم الهاتف للتواصل</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    <input type="text" class="form-control" id="setting_contact_phone" name="setting_contact_phone"
                                        value="<?php echo find_setting_by_key($grouped_settings, 'general', 'contact_phone') ?? '+966 12 345 6789'; ?>">
                                </div>
                                <div class="form-text">رقم الهاتف الذي يظهر في صفحة التواصل</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_contact_email" class="form-label fw-bold">البريد الإلكتروني للتواصل</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="setting_contact_email" name="setting_contact_email"
                                        value="<?php echo find_setting_by_key($grouped_settings, 'general', 'contact_email') ?? '<EMAIL>'; ?>">
                                </div>
                                <div class="form-text">البريد الإلكتروني الذي يظهر في صفحة التواصل</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_contact_address" class="form-label fw-bold">عنوان التواصل</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                    <input type="text" class="form-control" id="setting_contact_address" name="setting_contact_address"
                                        value="<?php echo find_setting_by_key($grouped_settings, 'general', 'contact_address') ?? 'المملكة العربية السعودية'; ?>">
                                </div>
                                <div class="form-text">العنوان الذي يظهر في صفحة التواصل</div>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label for="setting_site_description" class="form-label fw-bold">وصف الموقع</label>
                                <textarea class="form-control" id="setting_site_description" name="setting_site_description" rows="3"><?php echo find_setting_by_key($grouped_settings, 'general', 'site_description') ?? 'نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم'; ?></textarea>
                                <div class="form-text">نص الترحيب الذي يظهر في الصفحة الرئيسية</div>
                            </div>
                        </div>
                    </div>

                    <!-- Memorization Tab -->
                    <div class="tab-pane fade" id="memorization" role="tabpanel" aria-labelledby="memorization-tab">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="setting_default_memorization_levels" class="form-label fw-bold">مستويات الحفظ الافتراضية</label>
                                <textarea class="form-control" id="setting_default_memorization_levels" name="setting_default_memorization_levels" rows="3"><?php echo find_setting_by_key($grouped_settings, 'memorization', 'default_memorization_levels') ?? 'مبتدئ,متوسط,متقدم'; ?></textarea>
                                <div class="form-text">قم بفصل القيم بفاصلة (,)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_quality_ratings" class="form-label fw-bold">تقييمات جودة الحفظ</label>
                                <textarea class="form-control" id="setting_quality_ratings" name="setting_quality_ratings" rows="3"><?php echo find_setting_by_key($grouped_settings, 'memorization', 'quality_ratings') ?? 'ممتاز,جيد جداً,جيد,مقبول,ضعيف'; ?></textarea>
                                <div class="form-text">قم بفصل القيم بفاصلة (,)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_tajweed_ratings" class="form-label fw-bold">تقييمات التجويد</label>
                                <textarea class="form-control" id="setting_tajweed_ratings" name="setting_tajweed_ratings" rows="3"><?php echo find_setting_by_key($grouped_settings, 'memorization', 'tajweed_ratings') ?? 'ممتاز,جيد جداً,جيد,مقبول,ضعيف'; ?></textarea>
                                <div class="form-text">قم بفصل القيم بفاصلة (,)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_fluency_ratings" class="form-label fw-bold">تقييمات الطلاقة</label>
                                <textarea class="form-control" id="setting_fluency_ratings" name="setting_fluency_ratings" rows="3"><?php echo find_setting_by_key($grouped_settings, 'memorization', 'fluency_ratings') ?? 'ممتاز,جيد جداً,جيد,مقبول,ضعيف'; ?></textarea>
                                <div class="form-text">قم بفصل القيم بفاصلة (,)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_default_assignment_days" class="form-label fw-bold">عدد أيام الواجب الافتراضي</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar-day"></i></span>
                                    <input type="number" class="form-control" id="setting_default_assignment_days" name="setting_default_assignment_days"
                                        value="<?php echo find_setting_by_key($grouped_settings, 'memorization', 'default_assignment_days') ?? '7'; ?>" min="1" max="30">
                                </div>
                                <div class="form-text">عدد الأيام الافتراضي لإكمال الواجب</div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">معاينة مستويات الحفظ</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex flex-wrap gap-2" id="levels_preview">
                                    <?php
                                    $levels = explode(',', find_setting_by_key($grouped_settings, 'memorization', 'default_memorization_levels') ?? 'مبتدئ,متوسط,متقدم');
                                    foreach ($levels as $level):
                                        if (trim($level)):
                                    ?>
                                        <span class="badge bg-primary"><?php echo trim($level); ?></span>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Registration Tab -->
                    <div class="tab-pane fade" id="registration" role="tabpanel" aria-labelledby="registration-tab">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="setting_allow_registration" class="form-label fw-bold">السماح بالتسجيل في الموقع</label>
                                <select class="form-select" id="setting_allow_registration" name="setting_allow_registration">
                                    <option value="1" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'allow_registration') ?? '1') == '1' ? 'selected' : ''; ?>>نعم</option>
                                    <option value="0" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'allow_registration') ?? '1') == '0' ? 'selected' : ''; ?>>لا</option>
                                </select>
                                <div class="form-text">السماح للمستخدمين بالتسجيل في الموقع</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_require_email_verification" class="form-label fw-bold">طلب تأكيد البريد الإلكتروني</label>
                                <select class="form-select" id="setting_require_email_verification" name="setting_require_email_verification">
                                    <option value="1" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'require_email_verification') ?? '1') == '1' ? 'selected' : ''; ?>>نعم</option>
                                    <option value="0" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'require_email_verification') ?? '1') == '0' ? 'selected' : ''; ?>>لا</option>
                                </select>
                                <div class="form-text">طلب تأكيد البريد الإلكتروني عند التسجيل</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_default_role" class="form-label fw-bold">الدور الافتراضي للمستخدمين الجدد</label>
                                <select class="form-select" id="setting_default_role" name="setting_default_role">
                                    <option value="student" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'default_role') ?? 'student') == 'student' ? 'selected' : ''; ?>>طالب</option>
                                    <option value="parent" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'default_role') ?? 'student') == 'parent' ? 'selected' : ''; ?>>ولي أمر</option>
                                    <option value="teacher" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'default_role') ?? 'student') == 'teacher' ? 'selected' : ''; ?>>معلم</option>
                                </select>
                                <div class="form-text">الدور الافتراضي للمستخدمين الجدد</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_allow_public_registration" class="form-label fw-bold">السماح بالتسجيل العام</label>
                                <select class="form-select" id="setting_allow_public_registration" name="setting_allow_public_registration">
                                    <option value="1" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'allow_public_registration') ?? '1') == '1' ? 'selected' : ''; ?>>نعم</option>
                                    <option value="0" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'allow_public_registration') ?? '1') == '0' ? 'selected' : ''; ?>>لا</option>
                                </select>
                                <div class="form-text">السماح للزوار بالتسجيل في الموقع دون دعوة</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_registration_approval" class="form-label fw-bold">تفعيل موافقة المشرف على التسجيل</label>
                                <select class="form-select" id="setting_registration_approval" name="setting_registration_approval">
                                    <option value="1" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'registration_approval') ?? '0') == '1' ? 'selected' : ''; ?>>نعم</option>
                                    <option value="0" <?php echo (find_setting_by_key($grouped_settings, 'registration', 'registration_approval') ?? '0') == '0' ? 'selected' : ''; ?>>لا</option>
                                </select>
                                <div class="form-text">يتطلب موافقة المشرف على طلبات التسجيل الجديدة</div>
                            </div>
                        </div>
                    </div>

                    <!-- System Tab -->
                    <div class="tab-pane fade" id="system" role="tabpanel" aria-labelledby="system-tab">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="setting_maintenance_mode" class="form-label fw-bold">وضع الصيانة</label>
                                <select class="form-select" id="setting_maintenance_mode" name="setting_maintenance_mode">
                                    <option value="1" <?php echo (find_setting_by_key($grouped_settings, 'system', 'maintenance_mode') ?? '0') == '1' ? 'selected' : ''; ?>>مفعل</option>
                                    <option value="0" <?php echo (find_setting_by_key($grouped_settings, 'system', 'maintenance_mode') ?? '0') == '0' ? 'selected' : ''; ?>>غير مفعل</option>
                                </select>
                                <div class="form-text">تفعيل وضع الصيانة (سيتم منع الوصول للموقع)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_debug_mode" class="form-label fw-bold">وضع التصحيح</label>
                                <select class="form-select" id="setting_debug_mode" name="setting_debug_mode">
                                    <option value="1" <?php echo (find_setting_by_key($grouped_settings, 'system', 'debug_mode') ?? '0') == '1' ? 'selected' : ''; ?>>مفعل</option>
                                    <option value="0" <?php echo (find_setting_by_key($grouped_settings, 'system', 'debug_mode') ?? '0') == '0' ? 'selected' : ''; ?>>غير مفعل</option>
                                </select>
                                <div class="form-text">تفعيل وضع التصحيح (عرض الأخطاء)</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_items_per_page" class="form-label fw-bold">عدد العناصر في الصفحة</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-list-ol"></i></span>
                                    <input type="number" class="form-control" id="setting_items_per_page" name="setting_items_per_page"
                                        value="<?php echo find_setting_by_key($grouped_settings, 'system', 'items_per_page') ?? '20'; ?>" min="5" max="100">
                                </div>
                                <div class="form-text">عدد العناصر التي تظهر في كل صفحة</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_timezone" class="form-label fw-bold">المنطقة الزمنية</label>
                                <select class="form-select" id="setting_timezone" name="setting_timezone">
                                    <option value="Asia/Riyadh" <?php echo (find_setting_by_key($grouped_settings, 'system', 'timezone') ?? 'Asia/Riyadh') == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (توقيت السعودية)</option>
                                    <option value="Asia/Dubai" <?php echo (find_setting_by_key($grouped_settings, 'system', 'timezone') ?? 'Asia/Riyadh') == 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (توقيت الإمارات)</option>
                                    <option value="Asia/Kuwait" <?php echo (find_setting_by_key($grouped_settings, 'system', 'timezone') ?? 'Asia/Riyadh') == 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (توقيت الكويت)</option>
                                    <option value="Asia/Amman" <?php echo (find_setting_by_key($grouped_settings, 'system', 'timezone') ?? 'Asia/Riyadh') == 'Asia/Amman' ? 'selected' : ''; ?>>عمان (توقيت الأردن)</option>
                                    <option value="Africa/Cairo" <?php echo (find_setting_by_key($grouped_settings, 'system', 'timezone') ?? 'Asia/Riyadh') == 'Africa/Cairo' ? 'selected' : ''; ?>>القاهرة (توقيت مصر)</option>
                                </select>
                                <div class="form-text">المنطقة الزمنية المستخدمة في النظام</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="setting_maintenance_message" class="form-label fw-bold">رسالة الصيانة</label>
                                <textarea class="form-control" id="setting_maintenance_message" name="setting_maintenance_message" rows="3"><?php echo find_setting_by_key($grouped_settings, 'system', 'maintenance_message') ?? 'الموقع قيد الصيانة حالياً، يرجى المحاولة لاحقاً'; ?></textarea>
                                <div class="form-text">الرسالة التي تظهر للمستخدمين أثناء وضع الصيانة</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <button type="submit" name="save_settings" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ الإعدادات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

<?php
// Include footer
include_once '../includes/footer_inner.php';
?>

<script>
    // Preview image before upload
    function previewImage(input, previewId) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();

            reader.onload = function(e) {
                document.getElementById(previewId).src = e.target.result;
            }

            reader.readAsDataURL(input.files[0]);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Update color text input when color picker changes
        document.getElementById('setting_primary_color').addEventListener('input', function() {
            const colorValue = this.value;
            document.getElementById('color_text_primary_color').value = colorValue;

            // Update preview elements
            document.getElementById('color_preview').style.backgroundColor = colorValue;
            document.getElementById('btn_preview').style.backgroundColor = colorValue;
        });

        // Update levels preview when textarea changes
        document.getElementById('setting_default_memorization_levels').addEventListener('input', function() {
            const levelsText = this.value;
            const levelsArray = levelsText.split(',');
            const previewDiv = document.getElementById('levels_preview');

            // Clear previous content
            previewDiv.innerHTML = '';

            // Add new badges
            levelsArray.forEach(function(level) {
                if (level.trim()) {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-primary me-1 mb-1';
                    badge.textContent = level.trim();
                    previewDiv.appendChild(badge);
                }
            });
        });

        // Add confirmation before form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            if (!confirm('هل أنت متأكد من حفظ التغييرات؟')) {
                e.preventDefault();
            }
        });

        // Auto-submit form when logo file is selected
        document.getElementById('logo_file').addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Show loading message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info';
                alertDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري تحميل الشعار...';
                document.querySelector('.card-body').prepend(alertDiv);

                // Submit the form
                document.querySelector('form').submit();
            }
        });

        // Auto-submit form when home image file is selected
        document.getElementById('home_image_file').addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Show loading message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info';
                alertDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري تحميل الصورة الرئيسية...';
                document.querySelector('.card-body').prepend(alertDiv);

                // Submit the form
                document.querySelector('form').submit();
            }
        });
    });
</script>