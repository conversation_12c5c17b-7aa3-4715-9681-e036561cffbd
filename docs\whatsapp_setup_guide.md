# دليل إعداد إشعارات الواتساب

## نظرة عامة
يوفر نظام إدارة حلقات تحفيظ القرآن الكريم إمكانية إرسال إشعارات الواتساب لأولياء الأمور عند غياب أو تأخر الطلاب.

## الخدمات المدعومة

### 1. <PERSON><PERSON><PERSON> (موصى به)
**المميزات:**
- خدمة موثوقة ومستقرة
- دعم فني ممتاز
- معدل تسليم عالي

**التكلفة:** مدفوعة (حوالي $0.005 لكل رسالة)

**خطوات الإعداد:**
1. إنشاء حساب في [Twilio](https://www.twilio.com)
2. تفعيل WhatsApp Business API
3. الحصول على Account SID و Auth Token
4. تحديث ملف `includes/whatsapp_config.php`:
```php
define('WHATSAPP_SERVICE', 'twilio');
define('TWILIO_ACCOUNT_SID', 'your_account_sid_here');
define('TWILIO_AUTH_TOKEN', 'your_auth_token_here');
define('TWILIO_WHATSAPP_NUMBER', 'whatsapp:+***********');
```

### 2. Ultramsg (خدمة عربية)
**المميزات:**
- خدمة عربية
- سهولة الإعداد
- أسعار مناسبة

**التكلفة:** مدفوعة (حوالي $0.01 لكل رسالة)

**خطوات الإعداد:**
1. إنشاء حساب في [Ultramsg](https://ultramsg.com)
2. إنشاء Instance جديد
3. الحصول على Token و Instance ID
4. تحديث ملف `includes/whatsapp_config.php`:
```php
define('WHATSAPP_SERVICE', 'ultramsg');
define('ULTRAMSG_TOKEN', 'your_token_here');
define('ULTRAMSG_INSTANCE_ID', 'your_instance_id_here');
```

### 3. WATI
**المميزات:**
- واجهة سهلة الاستخدام
- ميزات إضافية للتسويق
- دعم للقوالب

**التكلفة:** مدفوعة (خطط شهرية)

**خطوات الإعداد:**
1. إنشاء حساب في [WATI](https://wati.io)
2. الحصول على API Key
3. تحديث ملف `includes/whatsapp_config.php`:
```php
define('WHATSAPP_SERVICE', 'wati');
define('WATI_API_KEY', 'your_api_key_here');
define('WATI_API_URL', 'https://live-server.wati.io/api/v1/');
```

### 4. 360Dialog
**المميزات:**
- خدمة أوروبية موثوقة
- امتثال كامل لقوانين GDPR
- دعم فني ممتاز

**التكلفة:** مدفوعة

**خطوات الإعداد:**
1. إنشاء حساب في [360Dialog](https://www.360dialog.com)
2. الحصول على API Key و Partner ID
3. تحديث ملف `includes/whatsapp_config.php`:
```php
define('WHATSAPP_SERVICE', '360dialog');
define('DIALOG_360_API_KEY', 'your_api_key_here');
define('DIALOG_360_PARTNER_ID', 'your_partner_id_here');
```

## خطوات التفعيل

### 1. تحديث ملف الإعدادات
قم بتحديث الملف `includes/whatsapp_config.php` بالمعلومات الخاصة بالخدمة المختارة.

### 2. تفعيل الإشعارات
```php
define('WHATSAPP_NOTIFICATIONS_ENABLED', true);
define('WHATSAPP_ATTENDANCE_NOTIFICATIONS', true);
```

### 3. إضافة أرقام هواتف أولياء الأمور
تأكد من إضافة أرقام هواتف أولياء الأمور في قاعدة البيانات بالصيغة الدولية:
- مثال: +966501234567

### 4. اختبار الإعدادات
1. اذهب إلى صفحة "إعدادات الواتساب" في النظام
2. أدخل رقم هاتف للاختبار
3. اضغط على "إرسال رسالة تجريبية"

## تخصيص الرسائل

يمكنك تخصيص نصوص الرسائل في ملف `includes/whatsapp_config.php`:

### رسالة الغياب:
```php
define('ATTENDANCE_ABSENT_MESSAGE_TEMPLATE', 'السلام عليكم ورحمة الله وبركاته

عزيزي ولي الأمر،

نود إعلامكم بأن الطالب/ة: {student_name}
كان غائباً عن حلقة: {circle_name}
بتاريخ: {date}

{absence_reason}

للاستفسار يرجى التواصل مع المعلم: {teacher_name}

مركز تحفيظ القرآن الكريم
{center_name}');
```

### رسالة التأخير:
```php
define('ATTENDANCE_LATE_MESSAGE_TEMPLATE', 'السلام عليكم ورحمة الله وبركاته

عزيزي ولي الأمر،

نود إعلامكم بأن الطالب/ة: {student_name}
تأخر عن حلقة: {circle_name}
بتاريخ: {date}

{notes}

نرجو الحرص على الحضور في الوقت المحدد.

مركز تحفيظ القرآن الكريم
{center_name}');
```

## المتغيرات المتاحة في الرسائل

- `{student_name}`: اسم الطالب
- `{circle_name}`: اسم الحلقة
- `{date}`: تاريخ الجلسة
- `{teacher_name}`: اسم المعلم
- `{teacher_phone}`: رقم هاتف المعلم
- `{center_name}`: اسم المركز
- `{absence_reason}`: سبب الغياب
- `{notes}`: ملاحظات إضافية

## استكشاف الأخطاء

### 1. فحص سجل الأنشطة
يمكنك مراجعة ملف `logs/whatsapp.log` لمعرفة تفاصيل الأخطاء.

### 2. الأخطاء الشائعة

**خطأ: "Invalid phone number format"**
- تأكد من أن رقم الهاتف بالصيغة الدولية (+966xxxxxxxxx)

**خطأ: "WhatsApp configuration error"**
- تأكد من صحة مفاتيح API في ملف الإعدادات

**خطأ: "No parent phone found"**
- تأكد من ربط الطلاب بأولياء أمورهم وإضافة أرقام الهواتف

### 3. اختبار الاتصال
استخدم صفحة "إعدادات الواتساب" لاختبار إرسال رسالة تجريبية.

## الأمان والخصوصية

1. **حماية مفاتيح API**: لا تشارك مفاتيح API مع أي شخص
2. **تشفير البيانات**: تأكد من تشفير الاتصال (HTTPS)
3. **صلاحيات المستخدمين**: فقط المدراء يمكنهم الوصول لإعدادات الواتساب
4. **سجل الأنشطة**: يتم تسجيل جميع الأنشطة لأغراض المراجعة

## الدعم الفني

في حالة مواجهة مشاكل:
1. راجع سجل الأنشطة في `logs/whatsapp.log`
2. تأكد من صحة الإعدادات
3. اختبر الاتصال مع مزود الخدمة
4. راجع وثائق مزود الخدمة المختار

## التكاليف المتوقعة

- **Twilio**: $0.005 لكل رسالة
- **Ultramsg**: $0.01 لكل رسالة  
- **WATI**: خطط شهرية تبدأ من $39
- **360Dialog**: حسب الاستخدام

**مثال للتكلفة الشهرية:**
- 100 طالب × 20 يوم دراسي × 10% معدل غياب = 200 رسالة شهرياً
- التكلفة مع Twilio: $1 شهرياً
- التكلفة مع Ultramsg: $2 شهرياً
