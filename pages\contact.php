<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Initialize variables
$success = '';
$error = '';
$name = '';
$email = '';
$subject = '';
$message = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $name = sanitize_input($_POST['name']);
    $email = sanitize_input($_POST['email']);
    $subject = sanitize_input($_POST['subject']);
    $message = sanitize_input($_POST['message']);

    // Simple validation
    if (empty($name)) {
        $error = 'يرجى إدخال الاسم';
    } elseif (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'يرجى إدخال بريد إلكتروني صحيح';
    } elseif (empty($subject)) {
        $error = 'يرجى إدخال الموضوع';
    } elseif (empty($message)) {
        $error = 'يرجى إدخال الرسالة';
    } else {
        // In a real application, you would send an email here
        // For now, we'll just simulate success
        $success = 'تم إرسال رسالتك بنجاح. سنتواصل معك قريبًا.';
        
        // Clear form fields after successful submission
        $name = '';
        $email = '';
        $subject = '';
        $message = '';
    }
}

// Page title
$page_title = 'اتصل بنا';

// Include header
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h1 class="display-4 fw-bold">اتصل بنا</h1>
            <p class="lead">نحن هنا للإجابة على استفساراتك ومساعدتك في كل ما تحتاجه</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-4 mb-lg-0">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body p-4">
                    <h2 class="mb-4">معلومات الاتصال</h2>
                    
                    <div class="d-flex mb-4">
                        <div class="flex-shrink-0">
                            <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5>العنوان</h5>
                            <p class="mb-0">المملكة العربية السعودية، الرياض، حي النخيل، شارع الأمير سعود بن محمد</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-4">
                        <div class="flex-shrink-0">
                            <i class="fas fa-phone fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5>الهاتف</h5>
                            <p class="mb-0">+966 12 345 6789</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-4">
                        <div class="flex-shrink-0">
                            <i class="fas fa-envelope fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5>البريد الإلكتروني</h5>
                            <p class="mb-0"><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="d-flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock fa-2x text-primary"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5>ساعات العمل</h5>
                            <p class="mb-0">الأحد - الخميس: 8:00 صباحًا - 4:00 مساءً</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h2 class="mb-4">أرسل لنا رسالة</h2>
                    
                    <?php if (!empty($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">الاسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required value="<?php echo $name; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required value="<?php echo $email; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">الموضوع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" required value="<?php echo $subject; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="5" required><?php echo $message; ?></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h2 class="mb-4 text-center">الأسئلة الشائعة</h2>
                    
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse1" aria-expanded="true" aria-controls="faqCollapse1">
                                    كيف يمكنني التسجيل في النظام؟
                                </button>
                            </h2>
                            <div id="faqCollapse1" class="accordion-collapse collapse show" aria-labelledby="faq1" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكنك التسجيل في النظام من خلال النقر على زر "إنشاء حساب" في الصفحة الرئيسية، ثم اتباع الخطوات المطلوبة لإكمال عملية التسجيل.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse2" aria-expanded="false" aria-controls="faqCollapse2">
                                    هل النظام مجاني؟
                                </button>
                            </h2>
                            <div id="faqCollapse2" class="accordion-collapse collapse" aria-labelledby="faq2" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، النظام مجاني بالكامل لجميع المستخدمين، سواء كانوا مدراء مراكز أو معلمين أو طلاب أو أولياء أمور.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse3" aria-expanded="false" aria-controls="faqCollapse3">
                                    كيف يمكنني إضافة طلاب إلى الحلقة؟
                                </button>
                            </h2>
                            <div id="faqCollapse3" class="accordion-collapse collapse" aria-labelledby="faq3" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكن للمعلم أو مدير المركز إضافة طلاب إلى الحلقة من خلال الذهاب إلى صفحة "إدارة الحلقات" ثم اختيار الحلقة المطلوبة والنقر على "إضافة طالب".
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
