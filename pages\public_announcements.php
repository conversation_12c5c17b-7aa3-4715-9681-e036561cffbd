<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/announcement_stats.php';

$success = '';
$error = '';

// Check if columns exist in the announcements table
$media_type_exists = column_exists($pdo, 'announcements', 'media_type');
$media_url_exists = column_exists($pdo, 'announcements', 'media_url');
$image_url_exists = column_exists($pdo, 'announcements', 'image_url');
$background_color_exists = column_exists($pdo, 'announcements', 'background_color');
$text_color_exists = column_exists($pdo, 'announcements', 'text_color');
$start_date_exists = column_exists($pdo, 'announcements', 'start_date');
$end_date_exists = column_exists($pdo, 'announcements', 'end_date');
$is_public_exists = column_exists($pdo, 'announcements', 'is_public');
$is_featured_exists = column_exists($pdo, 'announcements', 'is_featured');

// Get public announcements
try {
    $stmt = $pdo->prepare("
        SELECT a.announcement_id, a.title, a.content, a.sender_user_id, a.target_role, a.target_center_id,
               a.is_active, a.created_at, a.updated_at,
               " . ($media_type_exists ? "a.media_type" : "'none'") . " AS media_type,
               " . ($media_url_exists ? "a.media_url" : "NULL") . " AS media_url, 
               " . ($image_url_exists ? "a.image_url" : "NULL") . " AS image_url,
               " . ($background_color_exists ? "a.background_color" : "'#ffffff'") . " AS background_color, 
               " . ($text_color_exists ? "a.text_color" : "'#000000'") . " AS text_color,
               " . ($start_date_exists ? "a.start_date" : "a.created_at") . " AS start_date, 
               " . ($end_date_exists ? "a.end_date" : "DATE_ADD(a.created_at, INTERVAL 30 DAY)") . " AS end_date,
               " . ($is_public_exists ? "a.is_public" : "0") . " AS is_public,
               " . ($is_featured_exists ? "a.is_featured" : "0") . " AS is_featured,
               u.full_name AS sender_name, c.center_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        LEFT JOIN centers c ON a.target_center_id = c.center_id
        WHERE a.is_active = TRUE AND " . ($is_public_exists ? "a.is_public = TRUE" : "1=1") . "
        ORDER BY a.created_at DESC
    ");
    $stmt->execute();
    $announcements = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الإعلانات: ' . $e->getMessage();
}

// Role names in Arabic
$role_names = [
    'all' => 'الجميع',
    'system_owner' => 'مالك النظام',
    'center_admin' => 'مدراء المراكز',
    'teacher' => 'المعلمين',
    'student' => 'الطلاب',
    'parent' => 'أولياء الأمور',
    'center_specific' => 'مركز محدد'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعلانات العامة - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="../index.php">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="public_announcements.php">الإعلانات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="about.php">عن النظام</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="contact.php">اتصل بنا</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <a href="../auth/login.php" class="btn btn-outline-light me-2">تسجيل الدخول</a>
                        <a href="../auth/register.php" class="btn btn-light">إنشاء حساب</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-bullhorn me-2"></i> الإعلانات العامة</h1>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (empty($announcements)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد إعلانات عامة متاحة حالياً.
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($announcements as $announcement): ?>
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <?php echo $announcement['title']; ?>
                                </h5>
                            </div>
                            <div class="card-body" style="background-color: <?php echo isset($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>; color: <?php echo isset($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>;">
                                <?php if (isset($announcement['media_type']) && $announcement['media_type'] !== 'none' && !empty($announcement['media_url'])): ?>
                                    <div class="text-center mb-3">
                                        <?php if ($announcement['media_type'] === 'image'): ?>
                                            <img src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 300px;">
                                        <?php elseif ($announcement['media_type'] === 'video'): ?>
                                            <video controls class="img-fluid rounded" style="max-height: 300px;">
                                                <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="video/mp4">
                                                متصفحك لا يدعم تشغيل الفيديو.
                                            </video>
                                        <?php elseif ($announcement['media_type'] === 'audio'): ?>
                                            <audio controls class="w-100">
                                                <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="audio/mpeg">
                                                متصفحك لا يدعم تشغيل الصوت.
                                            </audio>
                                        <?php endif; ?>
                                    </div>
                                <?php elseif (isset($announcement['image_url']) && !empty($announcement['image_url'])): ?>
                                    <div class="text-center mb-3">
                                        <img src="<?php echo strpos($announcement['image_url'], 'http') === 0 ? $announcement['image_url'] : '../' . $announcement['image_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 300px;">
                                    </div>
                                <?php endif; ?>
                                <div class="mb-3">
                                    <?php echo $announcement['content']; ?>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="text-muted">
                                        <small>
                                            <i class="fas fa-user me-1"></i> <?php echo $announcement['sender_name']; ?> |
                                            <i class="fas fa-calendar-alt me-1"></i> <?php echo date('Y-m-d H:i', strtotime($announcement['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </main>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
