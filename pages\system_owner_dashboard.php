<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Get system owner information
$owner_id = $_SESSION['user_id'];

// Get system statistics
try {
    // Count centers
    $stmt = $pdo->prepare("SELECT COUNT(*) AS center_count FROM centers WHERE is_active = TRUE");
    $stmt->execute();
    $center_count = $stmt->fetch()['center_count'];

    // Count users by role
    $stmt = $pdo->prepare("
        SELECT r.role_name, COUNT(u.user_id) AS user_count
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE u.is_active = TRUE
        GROUP BY r.role_name
    ");
    $stmt->execute();
    $users_by_role = $stmt->fetchAll();

    // Convert to associative array for easier access
    $user_counts = [];
    foreach ($users_by_role as $role) {
        $user_counts[$role['role_name']] = $role['user_count'];
    }

    // Count circles
    $stmt = $pdo->prepare("SELECT COUNT(*) AS circle_count FROM circles WHERE is_active = TRUE");
    $stmt->execute();
    $circle_count = $stmt->fetch()['circle_count'];

    // Count memorization progress entries
    $stmt = $pdo->prepare("SELECT COUNT(*) AS progress_count FROM memorization_progress");
    $stmt->execute();
    $progress_count = $stmt->fetch()['progress_count'];
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع إحصائيات النظام: ' . $e->getMessage();
}

// Get recent centers
try {
    $stmt = $pdo->prepare("
        SELECT c.center_id, c.center_name, c.created_at, c.is_active,
               u.full_name AS director_name
        FROM centers c
        LEFT JOIN users u ON c.director_user_id = u.user_id
        ORDER BY c.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recent_centers = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
}

// Get recent user registrations
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name, u.email, u.created_at,
               r.role_name, c.center_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        ORDER BY u.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recent_users = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المستخدمين: ' . $e->getMessage();
}

// Get system activity (audit logs would be ideal, but using a simple query for now)
try {
    $stmt = $pdo->prepare("
        (SELECT 'New User Registration' AS activity_type,
               u.full_name AS subject,
               u.created_at AS timestamp,
               r.role_name AS details
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        ORDER BY u.created_at DESC
        LIMIT 5)

        UNION

        (SELECT 'New Center Created' AS activity_type,
               c.center_name AS subject,
               c.created_at AS timestamp,
               CONCAT('Director: ', COALESCE(u.full_name, 'Not assigned')) AS details
        FROM centers c
        LEFT JOIN users u ON c.director_user_id = u.user_id
        ORDER BY c.created_at DESC
        LIMIT 5)

        UNION

        (SELECT 'New Circle Created' AS activity_type,
               ci.circle_name AS subject,
               ci.created_at AS timestamp,
               CONCAT('Center: ', c.center_name) AS details
        FROM circles ci
        JOIN centers c ON ci.center_id = c.center_id
        ORDER BY ci.created_at DESC
        LIMIT 5)

        ORDER BY timestamp DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recent_activities = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات النشاط: ' . $e->getMessage();
}

// Get unread messages
try {
    $stmt = $pdo->prepare("
        SELECT m.message_id, m.subject, m.sent_at, u.full_name AS sender_name
        FROM messages m
        JOIN users u ON m.sender_user_id = u.user_id
        WHERE m.recipient_user_id = ? AND m.read_at IS NULL
        ORDER BY m.sent_at DESC
        LIMIT 5
    ");
    $stmt->execute([$owner_id]);
    $unread_messages = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الرسائل: ' . $e->getMessage();
}
?>

<?php
// Set page title
$page_title = 'لوحة تحكم صاحب النظام';

// Include header
include_once '../includes/header_inner.php';

// Get current date in Arabic format
$current_date = date('Y-m-d');
$arabic_day = date('l');
$arabic_month = date('F');
$arabic_year = date('Y');

// Convert to Arabic names
$days_ar = [
    'Saturday' => 'السبت',
    'Sunday' => 'الأحد',
    'Monday' => 'الإثنين',
    'Tuesday' => 'الثلاثاء',
    'Wednesday' => 'الأربعاء',
    'Thursday' => 'الخميس',
    'Friday' => 'الجمعة'
];

$months_ar = [
    'January' => 'يناير',
    'February' => 'فبراير',
    'March' => 'مارس',
    'April' => 'أبريل',
    'May' => 'مايو',
    'June' => 'يونيو',
    'July' => 'يوليو',
    'August' => 'أغسطس',
    'September' => 'سبتمبر',
    'October' => 'أكتوبر',
    'November' => 'نوفمبر',
    'December' => 'ديسمبر'
];

$arabic_day = $days_ar[$arabic_day] ?? $arabic_day;
$arabic_month = $months_ar[$arabic_month] ?? $arabic_month;
$formatted_date = $arabic_day . '، ' . date('d') . ' ' . $arabic_month . ' ' . $arabic_year;

// Add custom CSS for dashboard
echo '<link rel="stylesheet" href="../css/dashboard.css">';

// Display any errors
if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

        <!-- Dashboard Header -->
        <div class="dashboard-header animate-fade-in">
            <div class="content">
                <h1>مرحباً، <?php echo $_SESSION['full_name']; ?></h1>
                <p>مرحباً بك في لوحة تحكم مدير النظام. هنا يمكنك إدارة جميع جوانب نظام حلقات تحفيظ القرآن الكريم.</p>
                <div class="date"><i class="fas fa-calendar-alt me-2"></i><?php echo $formatted_date; ?></div>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="row mb-4">
            <div class="col-md-6 col-lg-3 mb-3 animate-fade-in delay-1">
                <div class="stats-card-enhanced stats-primary-enhanced">
                    <div class="icon-wrapper">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stats-value"><?php echo isset($center_count) ? $center_count : '0'; ?></div>
                    <div class="stats-label">المراكز</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i> 5%
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-3 animate-fade-in delay-2">
                <div class="stats-card-enhanced stats-success-enhanced">
                    <div class="icon-wrapper">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-value"><?php echo isset($user_counts) ? array_sum($user_counts) : '0'; ?></div>
                    <div class="stats-label">المستخدمين</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i> 12%
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-3 animate-fade-in delay-3">
                <div class="stats-card-enhanced stats-warning-enhanced">
                    <div class="icon-wrapper">
                        <i class="fas fa-circle"></i>
                    </div>
                    <div class="stats-value"><?php echo isset($circle_count) ? $circle_count : '0'; ?></div>
                    <div class="stats-label">الحلقات</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i> 8%
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3 mb-3 animate-fade-in delay-4">
                <div class="stats-card-enhanced stats-danger-enhanced">
                    <div class="icon-wrapper">
                        <i class="fas fa-book-reader"></i>
                    </div>
                    <div class="stats-value"><?php echo isset($progress_count) ? $progress_count : '0'; ?></div>
                    <div class="stats-label">تسميعات</div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i> 15%
                    </div>
                </div>
            </div>
        </div>

        <!-- User Statistics -->
        <div class="row mb-4">
            <div class="col-md-12 animate-fade-in delay-5">
                <div class="enhanced-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i> إحصائيات المستخدمين</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <canvas id="userRolesChart"></canvas>
                            </div>
                            <div class="col-md-4">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>نوع المستخدم</th>
                                                <th>العدد</th>
                                                <th>النسبة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $total_users = array_sum($user_counts);
                                            $roles_ar = [
                                                'center_admin' => 'مدراء المراكز',
                                                'teacher' => 'المعلمين',
                                                'student' => 'الطلاب',
                                                'parent' => 'أولياء الأمور',
                                                'system_owner' => 'صاحب النظام'
                                            ];
                                            $colors = [
                                                'center_admin' => 'primary',
                                                'teacher' => 'success',
                                                'student' => 'info',
                                                'parent' => 'warning',
                                                'system_owner' => 'danger'
                                            ];

                                            foreach ($roles_ar as $role_key => $role_name):
                                                $count = isset($user_counts[$role_key]) ? $user_counts[$role_key] : 0;
                                                $percentage = $total_users > 0 ? round(($count / $total_users) * 100, 1) : 0;
                                            ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-<?php echo $colors[$role_key]; ?> me-2">&nbsp;</span>
                                                    <?php echo $role_name; ?>
                                                </td>
                                                <td><?php echo $count; ?></td>
                                                <td>
                                                    <div class="progress" style="height: 6px; width: 60px;">
                                                        <div class="progress-bar bg-<?php echo $colors[$role_key]; ?>" role="progressbar" style="width: <?php echo $percentage; ?>%" aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                    <small><?php echo $percentage; ?>%</small>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-center">
                        <a href="users.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-users me-1"></i> إدارة المستخدمين
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="enhanced-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i> إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions-grid">
                            <a href="add_center.php" class="action-card action-success animate-fade-in delay-1">
                                <div class="icon-container">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="action-title">إضافة مركز</div>
                                <div class="action-description">إنشاء مركز جديد لتحفيظ القرآن الكريم</div>
                            </a>

                            <a href="add_admin.php" class="action-card action-primary animate-fade-in delay-2">
                                <div class="icon-container">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="action-title">إضافة مدير مركز</div>
                                <div class="action-description">تعيين مدير جديد لأحد المراكز</div>
                            </a>

                            <a href="system_settings_v2.php" class="action-card action-warning animate-fade-in delay-3">
                                <div class="icon-container">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="action-title">إعدادات النظام</div>
                                <div class="action-description">تعديل إعدادات وخصائص النظام</div>
                            </a>

                            <a href="system_announcements.php?new=1" class="action-card action-info animate-fade-in delay-4">
                                <div class="icon-container">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <div class="action-title">إرسال إعلان عام</div>
                                <div class="action-description">نشر إعلان لجميع مستخدمي النظام</div>
                            </a>

                            <a href="system_home_image.php" class="action-card action-danger animate-fade-in delay-5">
                                <div class="icon-container">
                                    <i class="fas fa-image"></i>
                                </div>
                                <div class="action-title">تغيير الصورة الرئيسية</div>
                                <div class="action-description">تحديث صورة الصفحة الرئيسية</div>
                            </a>

                            <a href="users.php" class="action-card action-purple animate-fade-in delay-5">
                                <div class="icon-container">
                                    <i class="fas fa-user-cog"></i>
                                </div>
                                <div class="action-title">إدارة المستخدمين</div>
                                <div class="action-description">عرض وتعديل بيانات المستخدمين</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Centers and Activities -->
        <div class="row mb-4">
            <!-- Recent Centers -->
            <div class="col-md-6 mb-4 animate-fade-in delay-1">
                <div class="enhanced-card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-building me-2"></i> آخر المراكز المضافة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_centers)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                <p>لا توجد مراكز مضافة حديثاً.</p>
                                <a href="add_center.php" class="btn btn-sm btn-info mt-2">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة مركز جديد
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم المركز</th>
                                            <th>مدير المركز</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_centers as $center): ?>
                                            <tr>
                                                <td>
                                                    <a href="center_details.php?id=<?php echo $center['center_id']; ?>" class="fw-bold text-decoration-none">
                                                        <i class="fas fa-building me-1 text-info"></i>
                                                        <?php echo $center['center_name']; ?>
                                                    </a>
                                                </td>
                                                <td>
                                                    <?php if ($center['director_name']): ?>
                                                        <i class="fas fa-user-tie me-1 text-secondary"></i> <?php echo $center['director_name']; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted"><i class="fas fa-user-slash me-1"></i> غير معين</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><i class="fas fa-calendar-alt me-1 text-secondary"></i> <?php echo date('Y-m-d', strtotime($center['created_at'])); ?></td>
                                                <td>
                                                    <?php if ($center['is_active']): ?>
                                                        <span class="badge bg-success"><i class="fas fa-check me-1"></i> نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger"><i class="fas fa-ban me-1"></i> غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center">
                        <a href="centers.php" class="btn btn-sm btn-info me-2">
                            <i class="fas fa-list me-1"></i> عرض كل المراكز
                        </a>
                        <a href="add_center.php" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-plus-circle me-1"></i> إضافة مركز
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="col-md-6 mb-4 animate-fade-in delay-2">
                <div class="enhanced-card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i> آخر الأنشطة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_activities)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <p>لا توجد أنشطة حديثة.</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($recent_activities as $index => $activity):
                                    $icon = '';
                                    $color = '';

                                    switch ($activity['activity_type']) {
                                        case 'New User Registration':
                                            $icon = 'fa-user-plus';
                                            $color = 'success';
                                            break;
                                        case 'New Center Created':
                                            $icon = 'fa-building';
                                            $color = 'info';
                                            break;
                                        case 'New Circle Created':
                                            $icon = 'fa-circle';
                                            $color = 'warning';
                                            break;
                                        default:
                                            $icon = 'fa-bell';
                                            $color = 'secondary';
                                    }
                                ?>
                                    <div class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">
                                                <i class="fas <?php echo $icon; ?> me-2 text-<?php echo $color; ?>"></i>
                                                <?php echo $activity['activity_type']; ?>
                                            </h6>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo date('Y-m-d H:i', strtotime($activity['timestamp'])); ?>
                                            </small>
                                        </div>
                                        <p class="mb-1 fw-bold"><?php echo $activity['subject']; ?></p>
                                        <small class="text-muted"><?php echo $activity['details']; ?></small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center">
                        <a href="system_activity.php" class="btn btn-sm btn-warning">
                            <i class="fas fa-history me-1"></i> عرض كل الأنشطة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="row mb-4">
            <div class="col-md-12 animate-fade-in delay-3">
                <div class="enhanced-card">
                    <div class="card-header bg-purple text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-user-plus me-2"></i> آخر المستخدمين المسجلين</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_users)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p>لا يوجد مستخدمين مسجلين حديثاً.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>نوع المستخدم</th>
                                            <th>المركز</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_users as $user):
                                            $role = $user['role_name'];
                                            $badge_class = '';
                                            $role_text = '';
                                            $role_icon = '';

                                            switch ($role) {
                                                case 'system_owner':
                                                    $badge_class = 'danger';
                                                    $role_text = 'صاحب النظام';
                                                    $role_icon = 'fa-user-shield';
                                                    break;
                                                case 'center_admin':
                                                    $badge_class = 'primary';
                                                    $role_text = 'مدير مركز';
                                                    $role_icon = 'fa-user-tie';
                                                    break;
                                                case 'teacher':
                                                    $badge_class = 'success';
                                                    $role_text = 'معلم';
                                                    $role_icon = 'fa-chalkboard-teacher';
                                                    break;
                                                case 'student':
                                                    $badge_class = 'info';
                                                    $role_text = 'طالب';
                                                    $role_icon = 'fa-user-graduate';
                                                    break;
                                                case 'parent':
                                                    $badge_class = 'warning';
                                                    $role_text = 'ولي أمر';
                                                    $role_icon = 'fa-user-friends';
                                                    break;
                                                default:
                                                    $badge_class = 'secondary';
                                                    $role_text = $role;
                                                    $role_icon = 'fa-user';
                                            }
                                        ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle bg-light me-2">
                                                            <span><?php echo substr($user['full_name'], 0, 1); ?></span>
                                                        </div>
                                                        <a href="user_details.php?id=<?php echo $user['user_id']; ?>" class="fw-bold text-decoration-none">
                                                            <?php echo $user['full_name']; ?>
                                                        </a>
                                                    </div>
                                                </td>
                                                <td><i class="fas fa-envelope me-1 text-muted"></i> <?php echo $user['email']; ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $badge_class; ?>">
                                                        <i class="fas <?php echo $role_icon; ?> me-1"></i>
                                                        <?php echo $role_text; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($user['center_name']): ?>
                                                        <i class="fas fa-building me-1 text-info"></i> <?php echo $user['center_name']; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted"><i class="fas fa-unlink me-1"></i> غير مرتبط بمركز</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><i class="fas fa-calendar-alt me-1 text-secondary"></i> <?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="user_details.php?id=<?php echo $user['user_id']; ?>" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_user.php?id=<?php echo $user['user_id']; ?>" class="btn btn-outline-success" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center">
                        <a href="users.php" class="btn btn-sm btn-purple me-2">
                            <i class="fas fa-users me-1"></i> عرض كل المستخدمين
                        </a>
                        <a href="add_user.php" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-user-plus me-1"></i> إضافة مستخدم جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unread Messages -->
        <div class="row">
            <div class="col-md-12 animate-fade-in delay-4">
                <div class="enhanced-card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-envelope me-2"></i> الرسائل غير المقروءة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($unread_messages)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                                <p>لا توجد رسائل غير مقروءة.</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($unread_messages as $message): ?>
                                    <a href="<?php echo get_root_url(); ?>pages/message_details.php?id=<?php echo $message['message_id']; ?>" class="list-group-item list-group-item-action border-start border-danger border-4">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1 fw-bold">
                                                <i class="fas fa-envelope me-2 text-danger"></i>
                                                <?php echo $message['subject']; ?>
                                            </h6>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?>
                                            </small>
                                        </div>
                                        <p class="mb-1">
                                            <i class="fas fa-user me-1 text-secondary"></i>
                                            <span class="fw-bold">من:</span> <?php echo $message['sender_name']; ?>
                                        </p>
                                        <div class="mt-2 d-flex justify-content-end">
                                            <span class="badge bg-danger">غير مقروءة</span>
                                        </div>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center">
                        <a href="messages.php" class="btn btn-sm btn-danger me-2">
                            <i class="fas fa-envelope me-1"></i> عرض كل الرسائل
                        </a>
                        <a href="compose_message.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-paper-plane me-1"></i> إرسال رسالة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <style>
            /* Avatar circle style */
            .avatar-circle {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                color: #6f42c1;
                background-color: rgba(111, 66, 193, 0.1);
            }

            /* Purple button style */
            .btn-purple {
                background-color: #6f42c1;
                color: white;
            }
            .btn-purple:hover {
                background-color: #5a32a3;
                color: white;
            }
            .btn-outline-purple {
                color: #6f42c1;
                border-color: #6f42c1;
            }
            .btn-outline-purple:hover {
                background-color: #6f42c1;
                color: white;
            }

            /* Background color for purple cards */
            .bg-purple {
                background-color: #6f42c1 !important;
            }
        </style>
    </main>

    <!-- Chart.js and ChartJS-Doughnut-Labels plugin -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>

    <script>
        // User roles chart
        document.addEventListener('DOMContentLoaded', function() {
            var ctx = document.getElementById('userRolesChart').getContext('2d');

            // Define colors
            const colors = {
                primary: '#0d6efd',
                success: '#198754',
                info: '#0dcaf0',
                warning: '#ffc107',
                danger: '#dc3545',
                purple: '#6f42c1'
            };

            // Create gradient backgrounds
            const createGradient = (ctx, colorStart, colorEnd) => {
                const gradient = ctx.createLinearGradient(0, 0, 0, 400);
                gradient.addColorStop(0, colorStart);
                gradient.addColorStop(1, colorEnd);
                return gradient;
            };

            const gradients = [
                createGradient(ctx, colors.primary, shadeColor(colors.primary, -20)),
                createGradient(ctx, colors.success, shadeColor(colors.success, -20)),
                createGradient(ctx, colors.info, shadeColor(colors.info, -20)),
                createGradient(ctx, colors.warning, shadeColor(colors.warning, -20)),
                createGradient(ctx, colors.danger, shadeColor(colors.danger, -20))
            ];

            // Data
            const userData = [
                <?php echo isset($user_counts['center_admin']) ? $user_counts['center_admin'] : '0'; ?>,
                <?php echo isset($user_counts['teacher']) ? $user_counts['teacher'] : '0'; ?>,
                <?php echo isset($user_counts['student']) ? $user_counts['student'] : '0'; ?>,
                <?php echo isset($user_counts['parent']) ? $user_counts['parent'] : '0'; ?>,
                <?php echo isset($user_counts['system_owner']) ? $user_counts['system_owner'] : '0'; ?>
            ];

            // Calculate total
            const total = userData.reduce((a, b) => a + b, 0);

            // Create chart
            var userRolesChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['مدراء المراكز', 'المعلمين', 'الطلاب', 'أولياء الأمور', 'صاحب النظام'],
                    datasets: [{
                        data: userData,
                        backgroundColor: gradients,
                        borderColor: [
                            colors.primary,
                            colors.success,
                            colors.info,
                            colors.warning,
                            colors.danger
                        ],
                        borderWidth: 2,
                        hoverOffset: 15
                    }]
                },
                plugins: [ChartDataLabels],
                options: {
                    cutout: '60%',
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: 20
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        title: {
                            display: true,
                            text: 'توزيع المستخدمين حسب الأدوار',
                            font: {
                                size: 16,
                                weight: 'bold'
                            },
                            padding: {
                                top: 10,
                                bottom: 30
                            }
                        },
                        datalabels: {
                            formatter: (value, ctx) => {
                                const percentage = ((value / total) * 100).toFixed(1) + '%';
                                return percentage;
                            },
                            color: '#fff',
                            font: {
                                weight: 'bold',
                                size: 12
                            },
                            textStrokeColor: '#000',
                            textStrokeWidth: 1,
                            textShadowBlur: 5,
                            textShadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    animation: {
                        animateScale: true,
                        animateRotate: true,
                        duration: 2000,
                        easing: 'easeOutQuart'
                    }
                }
            });
        });

        // Helper function to shade colors
        function shadeColor(color, percent) {
            let R = parseInt(color.substring(1, 3), 16);
            let G = parseInt(color.substring(3, 5), 16);
            let B = parseInt(color.substring(5, 7), 16);

            R = parseInt(R * (100 + percent) / 100);
            G = parseInt(G * (100 + percent) / 100);
            B = parseInt(B * (100 + percent) / 100);

            R = (R < 255) ? R : 255;
            G = (G < 255) ? G : 255;
            B = (B < 255) ? B : 255;

            const RR = ((R.toString(16).length == 1) ? "0" + R.toString(16) : R.toString(16));
            const GG = ((G.toString(16).length == 1) ? "0" + G.toString(16) : G.toString(16));
            const BB = ((B.toString(16).length == 1) ? "0" + B.toString(16) : B.toString(16));

            return "#" + RR + GG + BB;
        }
    </script>

<?php
// Include announcements modal
include_once '../includes/announcement_modal.php';

// Include footer
include_once '../includes/footer_inner.php';
?>
