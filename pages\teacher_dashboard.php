<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has teacher role
if (!is_logged_in() || !has_role('teacher')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Get teacher information
$teacher_id = $_SESSION['user_id'];
$center_id = $_SESSION['center_id'];

// Get teacher's circles
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name, c.level, c.schedule_details,
               COUNT(sce.enrollment_id) AS student_count,
               c.max_students
        FROM circles c
        LEFT JOIN student_circle_enrollments sce ON c.circle_id = sce.circle_id AND sce.status = 'approved'
        WHERE c.teacher_user_id = ? AND c.is_active = TRUE
        GROUP BY c.circle_id
        ORDER BY c.circle_name
    ");
    $stmt->execute([$teacher_id]);
    $circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get today's attendance status
$today = date('Y-m-d');
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name,
               COUNT(sce.enrollment_id) AS total_students,
               COUNT(ar.attendance_id) AS recorded_attendance
        FROM circles c
        LEFT JOIN student_circle_enrollments sce ON c.circle_id = sce.circle_id AND sce.status = 'approved'
        LEFT JOIN attendance_records ar ON sce.enrollment_id = ar.enrollment_id AND ar.session_date = ?
        WHERE c.teacher_user_id = ? AND c.is_active = TRUE
        GROUP BY c.circle_id
    ");
    $stmt->execute([$today, $teacher_id]);
    $attendance_status = $stmt->fetchAll();

    // Convert to associative array for easier access
    $attendance_by_circle = [];
    foreach ($attendance_status as $status) {
        $attendance_by_circle[$status['circle_id']] = [
            'total_students' => $status['total_students'],
            'recorded_attendance' => $status['recorded_attendance']
        ];
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحضور: ' . $e->getMessage();
}

// Get recent memorization progress
try {
    $stmt = $pdo->prepare("
        SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to,
               mp.recitation_date, mp.memorization_quality,
               u.full_name AS student_name, c.circle_name
        FROM memorization_progress mp
        JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
        JOIN users u ON sce.student_user_id = u.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        WHERE c.teacher_user_id = ? AND mp.recorded_by_user_id = ?
        ORDER BY mp.recitation_date DESC
        LIMIT 10
    ");
    $stmt->execute([$teacher_id, $teacher_id]);
    $recent_progress = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات التقدم: ' . $e->getMessage();
}

// Get students with pending memorization schedules
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name, c.circle_name,
               ms.schedule_id, ms.target_surah_start, ms.target_ayah_start,
               ms.target_surah_end, ms.target_ayah_end, ms.due_date
        FROM memorization_schedules ms
        JOIN student_circle_enrollments sce ON ms.enrollment_id = sce.enrollment_id
        JOIN users u ON sce.student_user_id = u.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        WHERE c.teacher_user_id = ? AND ms.is_completed = FALSE AND ms.due_date <= DATE_ADD(CURDATE(), INTERVAL 3 DAY)
        ORDER BY ms.due_date ASC
        LIMIT 10
    ");
    $stmt->execute([$teacher_id]);
    $pending_schedules = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات خطط الحفظ: ' . $e->getMessage();
}

// Get unread messages
try {
    $stmt = $pdo->prepare("
        SELECT m.message_id, m.subject, m.sent_at, u.full_name AS sender_name
        FROM messages m
        JOIN users u ON m.sender_user_id = u.user_id
        WHERE m.recipient_user_id = ? AND m.read_at IS NULL
        ORDER BY m.sent_at DESC
        LIMIT 5
    ");
    $stmt->execute([$teacher_id]);
    $unread_messages = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الرسائل: ' . $e->getMessage();
}

// Get center information
try {
    $stmt = $pdo->prepare("SELECT center_name FROM centers WHERE center_id = ?");
    $stmt->execute([$center_id]);
    $center = $stmt->fetch();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المركز: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المعلم - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link active" href="teacher_dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="my_circles.php">حلقاتي</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="attendance.php">الحضور</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="memorization.php">متابعة الحفظ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="messages.php">الرسائل</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container py-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <h1 class="mb-4">مرحباً، <?php echo $_SESSION['full_name']; ?></h1>

        <?php if (isset($center)): ?>
            <div class="alert alert-info">
                <i class="fas fa-building me-2"></i> أنت تعمل في مركز: <strong><?php echo $center['center_name']; ?></strong>
            </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i> إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="attendance.php" class="btn btn-outline-primary d-block py-3">
                                    <i class="fas fa-calendar-check fa-2x mb-2"></i><br>
                                    تسجيل الحضور
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="memorization.php" class="btn btn-outline-success d-block py-3">
                                    <i class="fas fa-book-reader fa-2x mb-2"></i><br>
                                    تسجيل تسميع
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="assignments.php" class="btn btn-outline-warning d-block py-3">
                                    <i class="fas fa-tasks fa-2x mb-2"></i><br>
                                    إضافة واجب
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="messages.php?new=1" class="btn btn-outline-info d-block py-3">
                                    <i class="fas fa-envelope fa-2x mb-2"></i><br>
                                    إرسال رسالة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Circles Summary -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> حلقاتي</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($circles)): ?>
                            <p class="text-center">لا توجد حلقات مسجلة لك حالياً.</p>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($circles as $circle): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title"><?php echo $circle['circle_name']; ?></h5>
                                                <p class="card-text">
                                                    <span class="d-block"><strong>المستوى:</strong> <?php echo $circle['level']; ?></span>
                                                    <span class="d-block"><strong>المواعيد:</strong> <?php echo $circle['schedule_details']; ?></span>
                                                    <span class="d-block">
                                                        <strong>الطلاب:</strong>
                                                        <?php echo $circle['student_count']; ?> / <?php echo $circle['max_students'] ?: 'غير محدد'; ?>
                                                    </span>
                                                </p>

                                                <?php if (isset($attendance_by_circle[$circle['circle_id']])): ?>
                                                    <?php
                                                    $attendance = $attendance_by_circle[$circle['circle_id']];
                                                    $total = $attendance['total_students'];
                                                    $recorded = $attendance['recorded_attendance'];
                                                    $percentage = $total > 0 ? round(($recorded / $total) * 100) : 0;
                                                    ?>
                                                    <div class="mt-3">
                                                        <p><strong>حضور اليوم:</strong></p>
                                                        <div class="progress">
                                                            <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo $percentage; ?>%"
                                                                 aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                                                <?php echo $percentage; ?>%
                                                            </div>
                                                        </div>
                                                        <small class="text-muted"><?php echo $recorded; ?> من <?php echo $total; ?> طالب</small>
                                                    </div>
                                                <?php endif; ?>

                                                <div class="mt-3">
                                                    <a href="circle_details.php?id=<?php echo $circle['circle_id']; ?>" class="btn btn-sm btn-primary">عرض التفاصيل</a>
                                                    <a href="attendance.php?circle_id=<?php echo $circle['circle_id']; ?>" class="btn btn-sm btn-info">تسجيل الحضور</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <a href="my_circles.php" class="btn btn-sm btn-outline-success">عرض كل الحلقات</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Schedules and Recent Progress -->
        <div class="row mb-4">
            <!-- Pending Memorization Schedules -->
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0"><i class="fas fa-exclamation-triangle me-2"></i> خطط حفظ مستحقة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($pending_schedules)): ?>
                            <p class="text-center">لا توجد خطط حفظ مستحقة حالياً.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الحلقة</th>
                                            <th>المقطع</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>الإجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pending_schedules as $schedule): ?>
                                            <tr>
                                                <td><?php echo $schedule['full_name']; ?></td>
                                                <td><?php echo $schedule['circle_name']; ?></td>
                                                <td>
                                                    <?php echo $schedule['target_surah_start']; ?>
                                                    (<?php echo $schedule['target_ayah_start']; ?>) -
                                                    <?php echo $schedule['target_surah_end']; ?>
                                                    (<?php echo $schedule['target_ayah_end']; ?>)
                                                </td>
                                                <td>
                                                    <?php
                                                    $due_date = strtotime($schedule['due_date']);
                                                    $now = time();
                                                    $days_diff = round(($due_date - $now) / (60 * 60 * 24));

                                                    echo date('Y-m-d', $due_date);

                                                    if ($days_diff < 0) {
                                                        echo ' <span class="badge bg-danger">متأخر ' . abs($days_diff) . ' يوم</span>';
                                                    } elseif ($days_diff == 0) {
                                                        echo ' <span class="badge bg-warning">اليوم</span>';
                                                    } else {
                                                        echo ' <span class="badge bg-info">متبقي ' . $days_diff . ' يوم</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <a href="memorization.php?student_id=<?php echo $schedule['user_id']; ?>&schedule_id=<?php echo $schedule['schedule_id']; ?>"
                                                       class="btn btn-sm btn-success">
                                                        تسميع
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="memorization_schedules.php" class="btn btn-sm btn-outline-warning">عرض الكل</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Memorization Progress -->
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i> آخر تسميعات</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_progress)): ?>
                            <p class="text-center">لا يوجد تسميعات مسجلة حالياً.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الحلقة</th>
                                            <th>السورة</th>
                                            <th>الآيات</th>
                                            <th>التقييم</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_progress as $progress): ?>
                                            <tr>
                                                <td><?php echo $progress['student_name']; ?></td>
                                                <td><?php echo $progress['circle_name']; ?></td>
                                                <td><?php echo $progress['surah_name']; ?></td>
                                                <td><?php echo $progress['ayah_from'] . ' - ' . $progress['ayah_to']; ?></td>
                                                <td>
                                                    <?php
                                                    $quality = $progress['memorization_quality'];
                                                    $color = '';
                                                    switch ($quality) {
                                                        case 'excellent': $color = 'success'; break;
                                                        case 'very_good': $color = 'primary'; break;
                                                        case 'good': $color = 'info'; break;
                                                        case 'fair': $color = 'warning'; break;
                                                        case 'poor': $color = 'danger'; break;
                                                    }
                                                    echo '<span class="badge bg-' . $color . '">' . ucfirst(str_replace('_', ' ', $quality)) . '</span>';
                                                    ?>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($progress['recitation_date'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="memorization_progress.php" class="btn btn-sm btn-outline-info">عرض الكل</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unread Messages -->
        <div class="row">
            <div class="col-md-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-envelope me-2"></i> الرسائل غير المقروءة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($unread_messages)): ?>
                            <p class="text-center">لا توجد رسائل غير مقروءة.</p>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($unread_messages as $message): ?>
                                    <a href="<?php echo get_root_url(); ?>pages/message_details.php?id=<?php echo $message['message_id']; ?>" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?php echo $message['subject']; ?></h6>
                                            <small><?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?></small>
                                        </div>
                                        <p class="mb-1">من: <?php echo $message['sender_name']; ?></p>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="messages.php" class="btn btn-sm btn-outline-secondary">عرض كل الرسائل</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>

    <?php
    // Include announcements modal
    include_once '../includes/announcement_modal.php';
    ?>
</body>
</html>
