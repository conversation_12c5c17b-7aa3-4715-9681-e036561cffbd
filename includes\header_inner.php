<?php
// Include common functions and definitions
require_once dirname(__FILE__) . '/common.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo get_root_url(); ?>css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="<?php echo get_root_url(); ?>index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if (is_logged_in()): ?>
                            <?php if (has_role('system_owner')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'system_owner_dashboard.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/system_owner_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'centers.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/centers.php">المراكز</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/users.php">المستخدمين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'system_settings.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/system_settings.php">إعدادات النظام</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'announcements.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/announcements.php">الإعلانات</a>
                                </li>
                            <?php elseif (has_role('center_admin')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'center_admin_dashboard.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/center_admin_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'teachers.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/teachers.php">المعلمين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'students.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/students.php">الطلاب</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'circles.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/circles.php">الحلقات</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'announcements.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/announcements.php">الإعلانات</a>
                                </li>
                            <?php elseif (has_role('teacher')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'teacher_dashboard.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/teacher_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'my_circles.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/my_circles.php">حلقاتي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'teacher_students.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/teacher_students.php">طلابي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'attendance.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/attendance.php">الحضور</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'memorization.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/memorization.php">متابعة الحفظ</a>
                                </li>
                            <?php elseif (has_role('student')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'student_dashboard.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/student_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'my_progress.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/my_progress.php">تقدمي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'student_assignments.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/student_assignments.php">الواجبات</a>
                                </li>
                            <?php elseif (has_role('parent')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'parent_dashboard.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/parent_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'my_children.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/my_children.php">أبنائي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'parent_page.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/parent_page.php">صفحة الوالدين</a>
                                </li>
                            <?php endif; ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'messages.php' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/messages.php">الرسائل</a>
                            </li>
                        <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo get_root_url(); ?>index.php">الرئيسية</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo get_root_url(); ?>pages/about.php">عن النظام</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo get_root_url(); ?>pages/contact.php">اتصل بنا</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    <div class="d-flex align-items-center">
                        <?php if (is_logged_in()): ?>
                            <!-- WhatsApp Settings Icon for Admins -->
                            <?php if (has_any_role(['system_owner', 'center_admin'])): ?>
                                <div class="me-3">
                                    <a href="<?php echo get_root_url(); ?>pages/whatsapp_settings.php"
                                       class="btn btn-outline-light btn-sm position-relative"
                                       title="إعدادات الواتساب">
                                        <i class="fab fa-whatsapp"></i>
                                        <?php
                                        // Check WhatsApp configuration status
                                        $config_errors = validate_whatsapp_config();
                                        $is_whatsapp_configured = empty($config_errors);
                                        ?>
                                        <?php if (!$is_whatsapp_configured): ?>
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning">
                                                <i class="fas fa-exclamation-triangle" style="font-size: 8px;"></i>
                                                <span class="visually-hidden">إعدادات غير مكتملة</span>
                                            </span>
                                        <?php elseif (is_whatsapp_enabled()): ?>
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success">
                                                <i class="fas fa-check" style="font-size: 8px;"></i>
                                                <span class="visually-hidden">مفعل</span>
                                            </span>
                                        <?php endif; ?>
                                    </a>
                                </div>
                            <?php endif; ?>

                            <div class="dropdown">
                                <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="<?php echo get_root_url(); ?>pages/profile.php">الملف الشخصي</a></li>
                                    <li><a class="dropdown-item" href="<?php echo get_root_url(); ?>pages/settings.php">الإعدادات</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo get_root_url(); ?>auth/logout.php">تسجيل الخروج</a></li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <a href="<?php echo get_root_url(); ?>auth/login.php" class="btn btn-outline-light me-2">تسجيل الدخول</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <main class="container py-4">
        <?php if (isset($_SESSION['flash_message']) && isset($_SESSION['flash_type'])): ?>
            <div class="alert alert-<?php echo $_SESSION['flash_type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['flash_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
        <?php endif; ?>
