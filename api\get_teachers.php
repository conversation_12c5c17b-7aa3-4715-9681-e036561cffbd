<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!is_logged_in()) {
    echo json_encode(['error' => 'غير مصرح لك بالوصول إلى هذه الصفحة']);
    exit;
}

// Check if user has appropriate role
if (!(has_role('system_owner') || has_role('center_admin'))) {
    echo json_encode(['error' => 'غير مصرح لك بالوصول إلى هذه الصفحة']);
    exit;
}

// Get center ID from query string
$center_id = isset($_GET['center_id']) ? (int)$_GET['center_id'] : null;

if (!$center_id) {
    echo json_encode(['error' => 'معرف المركز مطلوب']);
    exit;
}

try {
    // Get teachers for the specified center
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name, u.email
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE r.role_name = 'teacher' AND u.is_active = TRUE AND u.center_id = ?
        ORDER BY u.full_name
    ");
    $stmt->execute([$center_id]);
    $teachers = $stmt->fetchAll();
    
    echo json_encode($teachers);
} catch (PDOException $e) {
    echo json_encode(['error' => 'حدث خطأ أثناء استرجاع بيانات المعلمين: ' . $e->getMessage()]);
}
?>
