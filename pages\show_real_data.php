<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>البيانات الحقيقية من قاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }";
echo "table { border-collapse: collapse; width: 100%; margin: 20px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background-color: #f2f2f2; font-weight: bold; }";
echo "h1, h2 { color: #333; }";
echo ".count { color: #007bff; font-weight: bold; }";
echo ".error { color: #dc3545; }";
echo ".success { color: #28a745; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>📊 البيانات الحقيقية من قاعدة البيانات</h1>";
echo "<p><strong>تاريخ الاستعلام:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    // 1. عرض المستخدمين
    echo "<h2>👥 المستخدمين</h2>";
    $stmt = $pdo->query("
        SELECT u.user_id, u.username, u.full_name, u.email, u.phone_number, 
               r.role_name, c.center_name, u.is_active, u.created_at
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.role_id 
        LEFT JOIN centers c ON u.center_id = c.center_id 
        ORDER BY u.created_at DESC
    ");
    $users = $stmt->fetchAll();
    
    echo "<p class='count'>إجمالي المستخدمين: " . count($users) . "</p>";
    
    if (!empty($users)) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>البريد الإلكتروني</th><th>الهاتف</th><th>الدور</th><th>المركز</th><th>نشط</th><th>تاريخ الإنشاء</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['user_id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['full_name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>" . ($user['phone_number'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($user['role_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($user['center_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($user['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "<td>{$user['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>لا توجد بيانات مستخدمين</p>";
    }

    // 2. عرض الأدوار
    echo "<h2>🎭 الأدوار</h2>";
    $stmt = $pdo->query("SELECT * FROM roles ORDER BY role_id");
    $roles = $stmt->fetchAll();
    
    echo "<p class='count'>إجمالي الأدوار: " . count($roles) . "</p>";
    
    if (!empty($roles)) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم الدور</th><th>الوصف</th></tr>";
        foreach ($roles as $role) {
            echo "<tr>";
            echo "<td>{$role['role_id']}</td>";
            echo "<td>{$role['role_name']}</td>";
            echo "<td>" . ($role['description'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // 3. عرض المراكز
    echo "<h2>🏢 المراكز</h2>";
    $stmt = $pdo->query("SELECT * FROM centers ORDER BY center_id");
    $centers = $stmt->fetchAll();
    
    echo "<p class='count'>إجمالي المراكز: " . count($centers) . "</p>";
    
    if (!empty($centers)) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم المركز</th><th>العنوان</th><th>الهاتف</th><th>البريد الإلكتروني</th><th>نشط</th></tr>";
        foreach ($centers as $center) {
            echo "<tr>";
            echo "<td>{$center['center_id']}</td>";
            echo "<td>{$center['center_name']}</td>";
            echo "<td>" . ($center['address'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($center['phone_number'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($center['email'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($center['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // 4. عرض الحلقات
    echo "<h2>⭕ الحلقات</h2>";
    $stmt = $pdo->query("
        SELECT c.circle_id, c.circle_name, c.description, c.max_students, c.is_active,
               u.full_name as teacher_name, cent.center_name
        FROM circles c 
        LEFT JOIN users u ON c.teacher_user_id = u.user_id 
        LEFT JOIN centers cent ON c.center_id = cent.center_id 
        ORDER BY c.circle_id
    ");
    $circles = $stmt->fetchAll();
    
    echo "<p class='count'>إجمالي الحلقات: " . count($circles) . "</p>";
    
    if (!empty($circles)) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم الحلقة</th><th>الوصف</th><th>المعلم</th><th>المركز</th><th>الحد الأقصى للطلاب</th><th>نشطة</th></tr>";
        foreach ($circles as $circle) {
            echo "<tr>";
            echo "<td>{$circle['circle_id']}</td>";
            echo "<td>{$circle['circle_name']}</td>";
            echo "<td>" . ($circle['description'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($circle['teacher_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($circle['center_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($circle['max_students'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($circle['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // 5. عرض تسجيلات الطلاب في الحلقات
    echo "<h2>📚 تسجيلات الطلاب في الحلقات</h2>";
    $stmt = $pdo->query("
        SELECT sce.enrollment_id, sce.enrollment_date, sce.is_active,
               s.full_name as student_name, c.circle_name, p.full_name as parent_name
        FROM student_circle_enrollments sce 
        JOIN users s ON sce.student_user_id = s.user_id 
        JOIN circles c ON sce.circle_id = c.circle_id 
        LEFT JOIN users p ON sce.parent_user_id = p.user_id 
        ORDER BY sce.enrollment_date DESC
    ");
    $enrollments = $stmt->fetchAll();
    
    echo "<p class='count'>إجمالي التسجيلات: " . count($enrollments) . "</p>";
    
    if (!empty($enrollments)) {
        echo "<table>";
        echo "<tr><th>ID</th><th>اسم الطالب</th><th>اسم الحلقة</th><th>ولي الأمر</th><th>تاريخ التسجيل</th><th>نشط</th></tr>";
        foreach (array_slice($enrollments, 0, 20) as $enrollment) { // عرض أول 20 فقط
            echo "<tr>";
            echo "<td>{$enrollment['enrollment_id']}</td>";
            echo "<td>{$enrollment['student_name']}</td>";
            echo "<td>{$enrollment['circle_name']}</td>";
            echo "<td>" . ($enrollment['parent_name'] ?? 'غير محدد') . "</td>";
            echo "<td>{$enrollment['enrollment_date']}</td>";
            echo "<td>" . ($enrollment['is_active'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        if (count($enrollments) > 20) {
            echo "<p><em>عرض أول 20 تسجيل من أصل " . count($enrollments) . "</em></p>";
        }
    }

    // 6. فحص الجداول الاختيارية
    $optional_tables = ['announcements', 'attendance_records', 'memorization_progress', 'whatsapp_logs', 'activity_logs', 'system_logs'];
    
    foreach ($optional_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<h2>📋 جدول $table</h2>";
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $count = $stmt->fetchColumn();
                echo "<p class='count'>إجمالي السجلات: $count</p>";
                
                if ($count > 0) {
                    // عرض أول 10 سجلات
                    $stmt = $pdo->query("SELECT * FROM $table ORDER BY " . 
                        (in_array($table, ['activity_logs', 'system_logs']) ? 'created_at' : 
                        ($table == 'attendance_records' ? 'session_date' : 
                        ($table == 'memorization_progress' ? 'recitation_date' : 'created_at'))) . 
                        " DESC LIMIT 10");
                    $records = $stmt->fetchAll();
                    
                    if (!empty($records)) {
                        echo "<table>";
                        // عرض أسماء الأعمدة
                        echo "<tr>";
                        foreach (array_keys($records[0]) as $column) {
                            echo "<th>$column</th>";
                        }
                        echo "</tr>";
                        
                        // عرض البيانات
                        foreach ($records as $record) {
                            echo "<tr>";
                            foreach ($record as $value) {
                                $display_value = $value;
                                if (strlen($display_value) > 50) {
                                    $display_value = substr($display_value, 0, 50) . '...';
                                }
                                echo "<td>" . htmlspecialchars($display_value ?? 'NULL') . "</td>";
                            }
                            echo "</tr>";
                        }
                        echo "</table>";
                        
                        if ($count > 10) {
                            echo "<p><em>عرض أول 10 سجلات من أصل $count</em></p>";
                        }
                    }
                }
            } catch (PDOException $e) {
                echo "<p class='error'>خطأ في قراءة جدول $table: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<h2>❌ جدول $table غير موجود</h2>";
        }
    }

    // 7. إحصائيات عامة
    echo "<h2>📊 إحصائيات عامة</h2>";
    echo "<table>";
    echo "<tr><th>البيان</th><th>العدد</th></tr>";
    
    // عدد المستخدمين النشطين
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1");
    echo "<tr><td>المستخدمين النشطين</td><td class='count'>" . $stmt->fetchColumn() . "</td></tr>";
    
    // عدد المستخدمين حسب الدور
    $stmt = $pdo->query("
        SELECT r.role_name, COUNT(*) as count 
        FROM users u 
        JOIN roles r ON u.role_id = r.role_id 
        WHERE u.is_active = 1 
        GROUP BY r.role_name
    ");
    $role_counts = $stmt->fetchAll();
    foreach ($role_counts as $role_count) {
        echo "<tr><td>{$role_count['role_name']}</td><td class='count'>{$role_count['count']}</td></tr>";
    }
    
    // عدد الحلقات النشطة
    $stmt = $pdo->query("SELECT COUNT(*) FROM circles WHERE is_active = 1");
    echo "<tr><td>الحلقات النشطة</td><td class='count'>" . $stmt->fetchColumn() . "</td></tr>";
    
    // عدد التسجيلات النشطة
    $stmt = $pdo->query("SELECT COUNT(*) FROM student_circle_enrollments WHERE is_active = 1");
    echo "<tr><td>التسجيلات النشطة</td><td class='count'>" . $stmt->fetchColumn() . "</td></tr>";
    
    echo "</table>";

} catch (PDOException $e) {
    echo "<p class='error'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p class='error'>خطأ عام: " . $e->getMessage() . "</p>";
}

echo "<br><hr>";
echo "<p><a href='user_activity.php'>العودة لصفحة نشاط المستخدمين</a> | ";
echo "<a href='system_owner_dashboard.php'>العودة للوحة التحكم</a></p>";
echo "</body></html>";
?>
