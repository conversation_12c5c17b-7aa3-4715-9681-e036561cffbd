<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$page_title = 'عينة من البيانات الحقيقية';
include_once '../includes/header_inner.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-database me-2"></i> عينة من البيانات الحقيقية</h1>
        <div>
            <a href="show_real_data.php" class="btn btn-info me-2" target="_blank">
                <i class="fas fa-external-link-alt me-1"></i> عرض كامل
            </a>
            <a href="user_activity.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة
            </a>
        </div>
    </div>

    <?php
    try {
        // عرض آخر 5 مستخدمين
        echo '<div class="card mb-4">';
        echo '<div class="card-header"><h5><i class="fas fa-users me-2"></i>آخر 5 مستخدمين مسجلين</h5></div>';
        echo '<div class="card-body">';
        
        $stmt = $pdo->query("
            SELECT u.username, u.full_name, r.role_name, u.created_at
            FROM users u 
            LEFT JOIN roles r ON u.role_id = r.role_id 
            ORDER BY u.created_at DESC 
            LIMIT 5
        ");
        $recent_users = $stmt->fetchAll();
        
        if (!empty($recent_users)) {
            echo '<div class="table-responsive">';
            echo '<table class="table table-striped">';
            echo '<thead><tr><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الدور</th><th>تاريخ التسجيل</th></tr></thead>';
            echo '<tbody>';
            foreach ($recent_users as $user) {
                echo '<tr>';
                echo '<td><code>' . htmlspecialchars($user['username']) . '</code></td>';
                echo '<td>' . htmlspecialchars($user['full_name']) . '</td>';
                echo '<td><span class="badge bg-primary">' . htmlspecialchars($user['role_name'] ?? 'غير محدد') . '</span></td>';
                echo '<td><small>' . $user['created_at'] . '</small></td>';
                echo '</tr>';
            }
            echo '</tbody></table>';
            echo '</div>';
        } else {
            echo '<p class="text-muted">لا توجد بيانات مستخدمين</p>';
        }
        echo '</div></div>';

        // عرض الحلقات النشطة
        echo '<div class="card mb-4">';
        echo '<div class="card-header"><h5><i class="fas fa-circle me-2"></i>الحلقات النشطة</h5></div>';
        echo '<div class="card-body">';
        
        $stmt = $pdo->query("
            SELECT c.circle_name, u.full_name as teacher_name, cent.center_name, c.max_students
            FROM circles c 
            LEFT JOIN users u ON c.teacher_user_id = u.user_id 
            LEFT JOIN centers cent ON c.center_id = cent.center_id 
            WHERE c.is_active = 1
            ORDER BY c.circle_name
        ");
        $active_circles = $stmt->fetchAll();
        
        if (!empty($active_circles)) {
            echo '<div class="table-responsive">';
            echo '<table class="table table-striped">';
            echo '<thead><tr><th>اسم الحلقة</th><th>المعلم</th><th>المركز</th><th>الحد الأقصى</th></tr></thead>';
            echo '<tbody>';
            foreach ($active_circles as $circle) {
                echo '<tr>';
                echo '<td><strong>' . htmlspecialchars($circle['circle_name']) . '</strong></td>';
                echo '<td>' . htmlspecialchars($circle['teacher_name'] ?? 'غير محدد') . '</td>';
                echo '<td>' . htmlspecialchars($circle['center_name'] ?? 'غير محدد') . '</td>';
                echo '<td><span class="badge bg-info">' . ($circle['max_students'] ?? 'غير محدد') . '</span></td>';
                echo '</tr>';
            }
            echo '</tbody></table>';
            echo '</div>';
        } else {
            echo '<p class="text-muted">لا توجد حلقات نشطة</p>';
        }
        echo '</div></div>';

        // عرض آخر التسجيلات
        echo '<div class="card mb-4">';
        echo '<div class="card-header"><h5><i class="fas fa-user-plus me-2"></i>آخر 5 تسجيلات في الحلقات</h5></div>';
        echo '<div class="card-body">';
        
        $stmt = $pdo->query("
            SELECT s.full_name as student_name, c.circle_name, sce.enrollment_date
            FROM student_circle_enrollments sce 
            JOIN users s ON sce.student_user_id = s.user_id 
            JOIN circles c ON sce.circle_id = c.circle_id 
            WHERE sce.is_active = 1
            ORDER BY sce.enrollment_date DESC 
            LIMIT 5
        ");
        $recent_enrollments = $stmt->fetchAll();
        
        if (!empty($recent_enrollments)) {
            echo '<div class="table-responsive">';
            echo '<table class="table table-striped">';
            echo '<thead><tr><th>اسم الطالب</th><th>اسم الحلقة</th><th>تاريخ التسجيل</th></tr></thead>';
            echo '<tbody>';
            foreach ($recent_enrollments as $enrollment) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($enrollment['student_name']) . '</td>';
                echo '<td><span class="badge bg-success">' . htmlspecialchars($enrollment['circle_name']) . '</span></td>';
                echo '<td><small>' . $enrollment['enrollment_date'] . '</small></td>';
                echo '</tr>';
            }
            echo '</tbody></table>';
            echo '</div>';
        } else {
            echo '<p class="text-muted">لا توجد تسجيلات حديثة</p>';
        }
        echo '</div></div>';

        // فحص الجداول الاختيارية وعرض عينات
        $optional_tables = [
            'activity_logs' => ['title' => 'سجلات الأنشطة', 'icon' => 'fas fa-history', 'date_col' => 'created_at'],
            'announcements' => ['title' => 'الإعلانات', 'icon' => 'fas fa-bullhorn', 'date_col' => 'created_at'],
            'attendance_records' => ['title' => 'سجلات الحضور', 'icon' => 'fas fa-calendar-check', 'date_col' => 'session_date'],
            'memorization_progress' => ['title' => 'تقدم الحفظ', 'icon' => 'fas fa-book-open', 'date_col' => 'recitation_date']
        ];

        foreach ($optional_tables as $table => $info) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo '<div class="card mb-4">';
                echo '<div class="card-header"><h5><i class="' . $info['icon'] . ' me-2"></i>' . $info['title'] . '</h5></div>';
                echo '<div class="card-body">';
                
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                    $count = $stmt->fetchColumn();
                    
                    echo '<p><strong>إجمالي السجلات:</strong> <span class="badge bg-primary">' . $count . '</span></p>';
                    
                    if ($count > 0) {
                        // عرض آخر 3 سجلات
                        $stmt = $pdo->query("SELECT * FROM $table ORDER BY " . $info['date_col'] . " DESC LIMIT 3");
                        $records = $stmt->fetchAll();
                        
                        if (!empty($records)) {
                            echo '<h6>آخر 3 سجلات:</h6>';
                            echo '<div class="table-responsive">';
                            echo '<table class="table table-sm table-striped">';
                            echo '<thead><tr>';
                            
                            // عرض أسماء الأعمدة (أول 5 أعمدة فقط)
                            $columns = array_keys($records[0]);
                            $display_columns = array_slice($columns, 0, 5);
                            foreach ($display_columns as $column) {
                                echo '<th>' . htmlspecialchars($column) . '</th>';
                            }
                            if (count($columns) > 5) {
                                echo '<th>...</th>';
                            }
                            echo '</tr></thead>';
                            
                            echo '<tbody>';
                            foreach ($records as $record) {
                                echo '<tr>';
                                foreach ($display_columns as $column) {
                                    $value = $record[$column];
                                    if (strlen($value) > 30) {
                                        $value = substr($value, 0, 30) . '...';
                                    }
                                    echo '<td><small>' . htmlspecialchars($value ?? 'NULL') . '</small></td>';
                                }
                                if (count($columns) > 5) {
                                    echo '<td><small>...</small></td>';
                                }
                                echo '</tr>';
                            }
                            echo '</tbody></table>';
                            echo '</div>';
                        }
                    } else {
                        echo '<p class="text-muted">لا توجد سجلات في هذا الجدول</p>';
                    }
                } catch (PDOException $e) {
                    echo '<p class="text-danger">خطأ في قراءة الجدول: ' . htmlspecialchars($e->getMessage()) . '</p>';
                }
                
                echo '</div></div>';
            }
        }

        // إحصائيات سريعة
        echo '<div class="card">';
        echo '<div class="card-header"><h5><i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة</h5></div>';
        echo '<div class="card-body">';
        echo '<div class="row">';
        
        // عدد المستخدمين النشطين
        $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1");
        $active_users = $stmt->fetchColumn();
        echo '<div class="col-md-3 mb-3">';
        echo '<div class="text-center">';
        echo '<h3 class="text-primary">' . $active_users . '</h3>';
        echo '<p class="mb-0">مستخدم نشط</p>';
        echo '</div></div>';
        
        // عدد الحلقات النشطة
        $stmt = $pdo->query("SELECT COUNT(*) FROM circles WHERE is_active = 1");
        $active_circles_count = $stmt->fetchColumn();
        echo '<div class="col-md-3 mb-3">';
        echo '<div class="text-center">';
        echo '<h3 class="text-success">' . $active_circles_count . '</h3>';
        echo '<p class="mb-0">حلقة نشطة</p>';
        echo '</div></div>';
        
        // عدد التسجيلات النشطة
        $stmt = $pdo->query("SELECT COUNT(*) FROM student_circle_enrollments WHERE is_active = 1");
        $active_enrollments = $stmt->fetchColumn();
        echo '<div class="col-md-3 mb-3">';
        echo '<div class="text-center">';
        echo '<h3 class="text-info">' . $active_enrollments . '</h3>';
        echo '<p class="mb-0">تسجيل نشط</p>';
        echo '</div></div>';
        
        // عدد المراكز
        $stmt = $pdo->query("SELECT COUNT(*) FROM centers");
        $centers_count = $stmt->fetchColumn();
        echo '<div class="col-md-3 mb-3">';
        echo '<div class="text-center">';
        echo '<h3 class="text-warning">' . $centers_count . '</h3>';
        echo '<p class="mb-0">مركز</p>';
        echo '</div></div>';
        
        echo '</div>';
        echo '</div></div>';

    } catch (PDOException $e) {
        echo '<div class="alert alert-danger">';
        echo '<h5>خطأ في قاعدة البيانات</h5>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">';
        echo '<h5>خطأ عام</h5>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '</div>';
    }
    ?>

    <div class="mt-4">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h6>
            <ul class="mb-0">
                <li><strong>تاريخ الاستعلام:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                <li><strong>قاعدة البيانات:</strong> quran_circle_management</li>
                <li><strong>المستخدم:</strong> <?php echo htmlspecialchars($_SESSION['username']); ?></li>
                <li><strong>الدور:</strong> <?php echo htmlspecialchars($_SESSION['role_name']); ?></li>
            </ul>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
