<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has teacher role
if (!is_logged_in() || !has_role('teacher')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$teacher_id = $_SESSION['user_id'];
$error = '';
$success = '';

/**
 * Send WhatsApp notifications for attendance
 */
function send_attendance_notifications($pdo, $circle_id, $session_date, $attendance_data, $teacher_id) {
    try {
        // Get circle and teacher information
        $stmt = $pdo->prepare("
            SELECT c.circle_name, c.center_id, ct.center_name,
                   u.full_name as teacher_name, u.phone_number as teacher_phone
            FROM circles c
            JOIN centers ct ON c.center_id = ct.center_id
            JOIN users u ON c.teacher_user_id = u.user_id
            WHERE c.circle_id = ?
        ");
        $stmt->execute([$circle_id]);
        $circle_info = $stmt->fetch();

        if (!$circle_info) {
            log_whatsapp_activity("Circle not found for ID: {$circle_id}", 'ERROR');
            return;
        }

        foreach ($attendance_data as $enrollment_id => $status) {
            // Only send notifications for absent and late students
            if (!in_array($status, ['absent_unexcused', 'absent_excused', 'late'])) {
                continue;
            }

            // Get student and parent information
            $stmt = $pdo->prepare("
                SELECT u.full_name as student_name, u.user_id as student_id,
                       p.full_name as parent_name, p.phone_number as parent_phone,
                       ar.notes
                FROM student_circle_enrollments sce
                JOIN users u ON sce.student_user_id = u.user_id
                LEFT JOIN Parent_Student_Relations psr ON u.user_id = psr.student_user_id
                LEFT JOIN users p ON psr.parent_user_id = p.user_id
                LEFT JOIN attendance_records ar ON sce.enrollment_id = ar.enrollment_id
                    AND ar.session_date = ?
                WHERE sce.enrollment_id = ?
                LIMIT 1
            ");
            $stmt->execute([$session_date, $enrollment_id]);
            $student_info = $stmt->fetch();

            if (!$student_info || empty($student_info['parent_phone'])) {
                log_whatsapp_activity("No parent phone found for enrollment ID: {$enrollment_id}", 'WARNING');
                continue;
            }

            // Prepare student and attendance data
            $student_data = [
                'student_name' => $student_info['student_name'],
                'parent_phone' => $student_info['parent_phone']
            ];

            $attendance_info = [
                'status' => $status,
                'session_date' => $session_date,
                'circle_name' => $circle_info['circle_name'],
                'teacher_name' => $circle_info['teacher_name'],
                'teacher_phone' => $circle_info['teacher_phone'],
                'center_name' => $circle_info['center_name'],
                'notes' => $student_info['notes'] ?? ''
            ];

            // Send WhatsApp notification
            $result = WhatsAppService::sendAttendanceNotification($student_data, $attendance_info);

            if ($result['success']) {
                log_whatsapp_activity("Attendance notification sent successfully for student: {$student_info['student_name']}", 'INFO');
            } else {
                log_whatsapp_activity("Failed to send attendance notification for student: {$student_info['student_name']}. Error: {$result['message']}", 'ERROR');
            }
        }

    } catch (Exception $e) {
        log_whatsapp_activity("Exception in send_attendance_notifications: " . $e->getMessage(), 'ERROR');
    }
}

// Get circle ID from query string if provided
$circle_id = isset($_GET['circle_id']) ? (int)$_GET['circle_id'] : null;

// Get teacher's circles
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name
        FROM circles c
        WHERE c.teacher_user_id = ? AND c.is_active = TRUE
        ORDER BY c.circle_name
    ");
    $stmt->execute([$teacher_id]);
    $circles = $stmt->fetchAll();

    if (empty($circles)) {
        $error = 'ليس لديك حلقات نشطة حالياً';
    } elseif (!$circle_id) {
        // If no circle ID provided, use the first one
        $circle_id = $circles[0]['circle_id'];
    } else {
        // Verify that the teacher has access to this circle
        $has_access = false;
        foreach ($circles as $circle) {
            if ($circle['circle_id'] == $circle_id) {
                $has_access = true;
                break;
            }
        }

        if (!$has_access) {
            set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الحلقة');
            redirect('pages/teacher_dashboard.php');
        }
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get selected circle information
if ($circle_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.circle_name, c.schedule_details
            FROM circles c
            WHERE c.circle_id = ?
        ");
        $stmt->execute([$circle_id]);
        $selected_circle = $stmt->fetch();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
    }
}

// Get students in the selected circle
if ($circle_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.full_name, u.profile_picture_url, sce.enrollment_id
            FROM student_circle_enrollments sce
            JOIN users u ON sce.student_user_id = u.user_id
            WHERE sce.circle_id = ? AND sce.status = 'approved'
            ORDER BY u.full_name
        ");
        $stmt->execute([$circle_id]);
        $students = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب: ' . $e->getMessage();
    }
}

// Get attendance date from query string or use today
$attendance_date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Validate date format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $attendance_date)) {
    $attendance_date = date('Y-m-d');
}

// Get existing attendance records for the selected date
if ($circle_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT ar.attendance_id, ar.enrollment_id, ar.status, ar.notes
            FROM attendance_records ar
            JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
            WHERE sce.circle_id = ? AND ar.session_date = ?
        ");
        $stmt->execute([$circle_id, $attendance_date]);
        $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Convert to associative array for easier access
        $attendance_by_enrollment = [];
        foreach ($attendance_records as $record) {
            $attendance_by_enrollment[$record['enrollment_id']] = [
                'attendance_id' => $record['attendance_id'],
                'status' => $record['status'],
                'notes' => $record['notes']
            ];
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع سجلات الحضور: ' . $e->getMessage();
    }
}

// Process attendance form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_attendance'])) {
    $posted_circle_id = (int)$_POST['circle_id'];
    $posted_date = $_POST['attendance_date'];
    $attendance_data = isset($_POST['attendance']) ? $_POST['attendance'] : [];
    $notes_data = isset($_POST['notes']) ? $_POST['notes'] : [];

    // Validate circle access
    $has_access = false;
    foreach ($circles as $circle) {
        if ($circle['circle_id'] == $posted_circle_id) {
            $has_access = true;
            break;
        }
    }

    if (!$has_access) {
        $error = 'غير مصرح لك بالوصول إلى هذه الحلقة';
    } elseif (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $posted_date)) {
        $error = 'تنسيق التاريخ غير صالح';
    } else {
        try {
            // Start transaction
            $pdo->beginTransaction();

            foreach ($attendance_data as $enrollment_id => $status) {
                $notes = isset($notes_data[$enrollment_id]) ? $notes_data[$enrollment_id] : '';

                // Check if record already exists
                $stmt = $pdo->prepare("
                    SELECT attendance_id
                    FROM attendance_records
                    WHERE enrollment_id = ? AND session_date = ?
                ");
                $stmt->execute([$enrollment_id, $posted_date]);
                $existing_record = $stmt->fetch();

                if ($existing_record) {
                    // Update existing record
                    $stmt = $pdo->prepare("
                        UPDATE attendance_records
                        SET status = ?, notes = ?, recorded_by_user_id = ?
                        WHERE attendance_id = ?
                    ");
                    $stmt->execute([$status, $notes, $teacher_id, $existing_record['attendance_id']]);
                } else {
                    // Insert new record
                    $stmt = $pdo->prepare("
                        INSERT INTO attendance_records (enrollment_id, session_date, status, notes, recorded_by_user_id)
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$enrollment_id, $posted_date, $status, $notes, $teacher_id]);
                }
            }

            // Commit transaction
            $pdo->commit();

            $success = 'تم حفظ سجلات الحضور بنجاح';

            // Send WhatsApp notifications for absent and late students
            if (is_whatsapp_enabled() && WHATSAPP_ATTENDANCE_NOTIFICATIONS) {
                send_attendance_notifications($pdo, $posted_circle_id, $posted_date, $attendance_data, $teacher_id);
            }

            // Refresh attendance records
            $stmt = $pdo->prepare("
                SELECT ar.attendance_id, ar.enrollment_id, ar.status, ar.notes
                FROM attendance_records ar
                JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
                WHERE sce.circle_id = ? AND ar.session_date = ?
            ");
            $stmt->execute([$posted_circle_id, $posted_date]);
            $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Convert to associative array for easier access
            $attendance_by_enrollment = [];
            foreach ($attendance_records as $record) {
                $attendance_by_enrollment[$record['enrollment_id']] = [
                    'attendance_id' => $record['attendance_id'],
                    'status' => $record['status'],
                    'notes' => $record['notes']
                ];
            }

            // Update variables to reflect the submitted form
            $circle_id = $posted_circle_id;
            $attendance_date = $posted_date;

            // Refresh circle information
            $stmt = $pdo->prepare("
                SELECT c.circle_name, c.schedule_details
                FROM circles c
                WHERE c.circle_id = ?
            ");
            $stmt->execute([$circle_id]);
            $selected_circle = $stmt->fetch();

            // Refresh students list
            $stmt = $pdo->prepare("
                SELECT u.user_id, u.full_name, u.profile_picture_url, sce.enrollment_id
                FROM student_circle_enrollments sce
                JOIN users u ON sce.student_user_id = u.user_id
                WHERE sce.circle_id = ? AND sce.status = 'approved'
                ORDER BY u.full_name
            ");
            $stmt->execute([$circle_id]);
            $students = $stmt->fetchAll();
        } catch (PDOException $e) {
            // Rollback transaction
            $pdo->rollBack();

            $error = 'حدث خطأ أثناء حفظ سجلات الحضور: ' . $e->getMessage();
        }
    }
}

// Get attendance history for the selected circle
if ($circle_id && empty($error)) {
    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT ar.session_date, COUNT(ar.attendance_id) AS total_records,
                   SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) AS present_count,
                   SUM(CASE WHEN ar.status = 'absent_excused' THEN 1 ELSE 0 END) AS excused_count,
                   SUM(CASE WHEN ar.status = 'absent_unexcused' THEN 1 ELSE 0 END) AS unexcused_count,
                   SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) AS late_count
            FROM attendance_records ar
            JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
            WHERE sce.circle_id = ?
            GROUP BY ar.session_date
            ORDER BY ar.session_date DESC
            LIMIT 10
        ");
        $stmt->execute([$circle_id]);
        $attendance_history = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع سجلات الحضور السابقة: ' . $e->getMessage();
    }
}
?>

<?php
// Set page title
$page_title = 'تسجيل الحضور';

// Include header
include_once '../includes/header_inner.php';
?>

    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>تسجيل الحضور</h1>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <?php if (!empty($circles)): ?>
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-calendar-check me-2"></i>
                                تسجيل الحضور - <?php echo isset($selected_circle) ? $selected_circle['circle_name'] : ''; ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="mb-4">
                                <div class="row g-3 align-items-end">
                                    <div class="col-md-5">
                                        <label for="circle_id" class="form-label">الحلقة</label>
                                        <select class="form-select" id="circle_id" name="circle_id" required>
                                            <?php foreach ($circles as $circle): ?>
                                                <option value="<?php echo $circle['circle_id']; ?>" <?php echo $circle['circle_id'] == $circle_id ? 'selected' : ''; ?>>
                                                    <?php echo $circle['circle_name']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <label for="date" class="form-label">التاريخ</label>
                                        <input type="date" class="form-control" id="date" name="date" value="<?php echo $attendance_date; ?>" required>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary w-100">عرض</button>
                                    </div>
                                </div>
                            </form>

                            <?php if (isset($students) && !empty($students)): ?>
                                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" id="attendance-form">
                                    <input type="hidden" name="circle_id" value="<?php echo $circle_id; ?>">
                                    <input type="hidden" name="attendance_date" value="<?php echo $attendance_date; ?>">

                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الطالب</th>
                                                    <th>الحالة</th>
                                                    <th>ملاحظات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($students as $student): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <img src="<?php echo !empty($student['profile_picture_url']) ? '../' . $student['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>"
                                                                     class="rounded-circle me-2" width="40" height="40" alt="صورة الطالب">
                                                                <div>
                                                                    <?php echo $student['full_name']; ?>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <?php
                                                                $current_status = isset($attendance_by_enrollment[$student['enrollment_id']])
                                                                    ? $attendance_by_enrollment[$student['enrollment_id']]['status']
                                                                    : 'present';
                                                                ?>
                                                                <input type="radio" class="btn-check" name="attendance[<?php echo $student['enrollment_id']; ?>]"
                                                                       id="present_<?php echo $student['enrollment_id']; ?>" value="present"
                                                                       <?php echo $current_status === 'present' ? 'checked' : ''; ?>>
                                                                <label class="btn btn-outline-success" for="present_<?php echo $student['enrollment_id']; ?>">
                                                                    <i class="fas fa-check-circle"></i> حاضر
                                                                </label>

                                                                <input type="radio" class="btn-check" name="attendance[<?php echo $student['enrollment_id']; ?>]"
                                                                       id="late_<?php echo $student['enrollment_id']; ?>" value="late"
                                                                       <?php echo $current_status === 'late' ? 'checked' : ''; ?>>
                                                                <label class="btn btn-outline-warning" for="late_<?php echo $student['enrollment_id']; ?>">
                                                                    <i class="fas fa-clock"></i> متأخر
                                                                </label>

                                                                <input type="radio" class="btn-check" name="attendance[<?php echo $student['enrollment_id']; ?>]"
                                                                       id="absent_excused_<?php echo $student['enrollment_id']; ?>" value="absent_excused"
                                                                       <?php echo $current_status === 'absent_excused' ? 'checked' : ''; ?>>
                                                                <label class="btn btn-outline-info" for="absent_excused_<?php echo $student['enrollment_id']; ?>">
                                                                    <i class="fas fa-exclamation-circle"></i> غائب بعذر
                                                                </label>

                                                                <input type="radio" class="btn-check" name="attendance[<?php echo $student['enrollment_id']; ?>]"
                                                                       id="absent_unexcused_<?php echo $student['enrollment_id']; ?>" value="absent_unexcused"
                                                                       <?php echo $current_status === 'absent_unexcused' ? 'checked' : ''; ?>>
                                                                <label class="btn btn-outline-danger" for="absent_unexcused_<?php echo $student['enrollment_id']; ?>">
                                                                    <i class="fas fa-times-circle"></i> غائب
                                                                </label>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <input type="text" class="form-control" name="notes[<?php echo $student['enrollment_id']; ?>]"
                                                                   placeholder="ملاحظات (اختياري)"
                                                                   value="<?php echo isset($attendance_by_enrollment[$student['enrollment_id']]) ? $attendance_by_enrollment[$student['enrollment_id']]['notes'] : ''; ?>">
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                                        <button type="submit" name="save_attendance" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i> حفظ سجل الحضور
                                        </button>
                                    </div>
                                </form>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> لا يوجد طلاب مسجلين في هذه الحلقة.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i> سجلات الحضور السابقة</h5>
                        </div>
                        <div class="card-body">
                            <?php if (isset($attendance_history) && !empty($attendance_history)): ?>
                                <div class="list-group">
                                    <?php foreach ($attendance_history as $record): ?>
                                        <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?circle_id=' . $circle_id . '&date=' . $record['session_date']; ?>"
                                           class="list-group-item list-group-item-action <?php echo $record['session_date'] === $attendance_date ? 'active' : ''; ?>">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1"><?php echo date('Y-m-d', strtotime($record['session_date'])); ?></h6>
                                                <small>
                                                    <?php
                                                    $total = $record['total_records'];
                                                    $present = $record['present_count'];
                                                    $late = $record['late_count'];

                                                    $attendance_rate = $total > 0 ? round((($present + $late) / $total) * 100) : 0;
                                                    echo $attendance_rate . '%';
                                                    ?>
                                                </small>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <small class="attendance-present"><i class="fas fa-check-circle"></i> <?php echo $record['present_count']; ?></small>
                                                <small class="attendance-late"><i class="fas fa-clock"></i> <?php echo $record['late_count']; ?></small>
                                                <small class="attendance-excused"><i class="fas fa-exclamation-circle"></i> <?php echo $record['excused_count']; ?></small>
                                                <small class="attendance-absent"><i class="fas fa-times-circle"></i> <?php echo $record['unexcused_count']; ?></small>
                                            </div>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <p class="text-center">لا توجد سجلات حضور سابقة.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> إرشادات</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> <strong>حاضر:</strong> الطالب حضر في الوقت المحدد
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-clock text-warning me-2"></i> <strong>متأخر:</strong> الطالب حضر متأخراً عن الموعد المحدد
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-exclamation-circle text-info me-2"></i> <strong>غائب بعذر:</strong> الطالب غائب ولكن بعذر مقبول
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times-circle text-danger me-2"></i> <strong>غائب:</strong> الطالب غائب بدون عذر
                                </li>
                            </ul>
                            <hr>
                            <p class="mb-0">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                يمكنك إضافة ملاحظات لكل طالب لتوضيح سبب الغياب أو التأخر أو أي ملاحظات أخرى.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Auto-submit form when changing circle or date
        document.addEventListener('DOMContentLoaded', function() {
            const circleSelect = document.getElementById('circle_id');
            const dateInput = document.getElementById('date');

            if (circleSelect && dateInput) {
                circleSelect.addEventListener('change', function() {
                    this.form.submit();
                });

                dateInput.addEventListener('change', function() {
                    this.form.submit();
                });
            }
        });
    </script>

<?php
// Include footer
include_once '../includes/footer_inner.php';
?>
