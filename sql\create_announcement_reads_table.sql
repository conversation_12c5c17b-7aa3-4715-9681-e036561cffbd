-- Create announcement_reads table if it doesn't exist
CREATE TABLE IF NOT EXISTS announcement_reads (
    read_id INT AUTO_INCREMENT PRIMARY KEY,
    announcement_id INT NOT NULL,
    user_id INT NOT NULL,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE,
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY unique_announcement_user (announcement_id, user_id)
);
