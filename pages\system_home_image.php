<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';

// Get current home image path from settings
$home_image_path = 'assets/images/quran-study.jpg'; // Default path
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'home_image_url'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $home_image_path = $result['setting_value'];
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع مسار الصورة الحالية: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['home_image']) && $_FILES['home_image']['error'] === UPLOAD_ERR_OK) {
    try {
        $upload_dir = '../assets/images/';

        // Create directory if it doesn't exist
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $file_extension = pathinfo($_FILES['home_image']['name'], PATHINFO_EXTENSION);
        $new_filename = 'quran-study.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;

        // If the file already exists, create a backup
        if (file_exists($upload_path)) {
            $backup_filename = 'quran-study-backup-' . date('YmdHis') . '.' . $file_extension;
            rename($upload_path, $upload_dir . $backup_filename);
        }

        if (move_uploaded_file($_FILES['home_image']['tmp_name'], $upload_path)) {
            // Update the setting in the database
            $new_image_path = 'assets/images/' . $new_filename;

            // Begin transaction
            $pdo->beginTransaction();

            // Check if the setting exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_settings WHERE setting_key = 'home_image_url'");
            $stmt->execute();
            $setting_exists = ($stmt->fetchColumn() > 0);

            if ($setting_exists) {
                // Update existing setting
                $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'home_image_url'");
                $stmt->execute([$new_image_path]);
            } else {
                // Insert new setting
                $stmt = $pdo->prepare("
                    INSERT INTO system_settings
                    (setting_key, setting_value, setting_description, setting_group, is_public)
                    VALUES ('home_image_url', ?, 'رابط الصورة الرئيسية', 'appearance', TRUE)
                ");
                $stmt->execute([$new_image_path]);
            }

            // Commit transaction
            $pdo->commit();

            $success = 'تم تحميل الصورة الرئيسية وتحديث الإعدادات بنجاح';
            $home_image_path = $new_image_path;
        } else {
            $error = 'حدث خطأ أثناء تحميل الصورة';
        }
    } catch (PDOException $e) {
        // Rollback transaction if it's active
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
    } catch (Exception $e) {
        // Rollback transaction if it's active
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = 'حدث خطأ أثناء معالجة الصورة: ' . $e->getMessage();
    }
}

// Page title
$page_title = 'تغيير الصورة الرئيسية';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => 'system_owner_dashboard.php'],
    ['title' => 'تغيير الصورة الرئيسية']
];

// Include header template
include_template('header', [
    'page_title' => $page_title
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-image me-2"></i> تغيير الصورة الرئيسية</h1>
        <a href="system_owner_dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
        </a>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-upload me-2"></i> تحميل صورة جديدة</h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-4">
                            <label for="home_image" class="form-label">اختر صورة جديدة للصفحة الرئيسية</label>
                            <input type="file" class="form-control" id="home_image" name="home_image" accept="image/*" required onchange="previewImage(this)">
                            <div class="form-text">
                                الحد الأقصى لحجم الملف: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF<br>
                                الأبعاد المثالية: 800×600 بكسل
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">معاينة الصورة الجديدة</label>
                            <div class="card p-3 text-center">
                                <img id="image_preview" src="#" alt="معاينة الصورة" class="img-fluid rounded shadow-sm" style="max-height: 300px; display: none;">
                                <div id="preview_placeholder" class="text-muted p-5">
                                    <i class="fas fa-image fa-3x mb-3"></i>
                                    <p>سيتم عرض معاينة الصورة هنا بعد اختيارها</p>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ الصورة الجديدة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> الصورة الحالية</h5>
                </div>
                <div class="card-body text-center">
                    <img src="<?php echo '../' . $home_image_path; ?>" alt="الصورة الرئيسية الحالية" class="img-fluid rounded shadow-sm mb-3">
                    <p class="text-muted">هذه هي الصورة التي تظهر حالياً في الصفحة الرئيسية</p>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0"><i class="fas fa-lightbulb me-2"></i> نصائح</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check-circle text-success me-2"></i> استخدم صورة ذات جودة عالية.</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i> تأكد من أن الصورة مناسبة لموضوع الموقع.</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i> يفضل استخدام صورة بأبعاد متناسبة مع تصميم الموقع.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Preview image before upload
    function previewImage(input) {
        const preview = document.getElementById('image_preview');
        const placeholder = document.getElementById('preview_placeholder');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
                placeholder.style.display = 'none';
            }

            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
            placeholder.style.display = 'block';
        }
    }
</script>

<?php
// Include footer template
include_template('footer');
?>
