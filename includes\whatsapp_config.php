<?php
/**
 * WhatsApp Notification Configuration
 * 
 * This file contains configuration for WhatsApp API services
 * Choose one of the following services and configure accordingly
 */

// WhatsApp Service Configuration
define('WHATSAPP_SERVICE', 'twilio'); // Options: 'twilio', 'ultramsg', 'wati', '360dialog', 'disabled'

// ===========================================
// TWILIO CONFIGURATION (Recommended)
// ===========================================
define('TWILIO_ACCOUNT_SID', 'your_twilio_account_sid_here');
define('TWILIO_AUTH_TOKEN', 'your_twilio_auth_token_here');
define('TWILIO_WHATSAPP_NUMBER', 'whatsapp:+***********'); // Twilio Sandbox number

// ===========================================
// ULTRAMSG CONFIGURATION (Arabic Service)
// ===========================================
define('ULTRAMSG_TOKEN', 'your_ultramsg_token_here');
define('ULTRAMSG_INSTANCE_ID', 'your_instance_id_here');

// ===========================================
// WATI CONFIGURATION
// ===========================================
define('WATI_API_KEY', 'your_wati_api_key_here');
define('WATI_API_URL', 'https://live-server.wati.io/api/v1/');

// ===========================================
// 360DIALOG CONFIGURATION
// ===========================================
define('DIALOG_360_API_KEY', 'your_360dialog_api_key_here');
define('DIALOG_360_PARTNER_ID', 'your_partner_id_here');

// ===========================================
// NOTIFICATION SETTINGS
// ===========================================
define('WHATSAPP_NOTIFICATIONS_ENABLED', true);
define('WHATSAPP_ATTENDANCE_NOTIFICATIONS', true);
define('WHATSAPP_ASSIGNMENT_NOTIFICATIONS', true);
define('WHATSAPP_ANNOUNCEMENT_NOTIFICATIONS', true);

// Message Templates
define('ATTENDANCE_ABSENT_MESSAGE_TEMPLATE', 'السلام عليكم ورحمة الله وبركاته

عزيزي ولي الأمر،

نود إعلامكم بأن الطالب/ة: {student_name}
كان غائباً عن حلقة: {circle_name}
بتاريخ: {date}

{absence_reason}

للاستفسار يرجى التواصل مع المعلم: {teacher_name}
هاتف: {teacher_phone}

مركز تحفيظ القرآن الكريم
{center_name}');

define('ATTENDANCE_LATE_MESSAGE_TEMPLATE', 'السلام عليكم ورحمة الله وبركاته

عزيزي ولي الأمر،

نود إعلامكم بأن الطالب/ة: {student_name}
تأخر عن حلقة: {circle_name}
بتاريخ: {date}

{notes}

نرجو الحرص على الحضور في الوقت المحدد.

مركز تحفيظ القرآن الكريم
{center_name}');

// Phone number validation regex (Saudi Arabia format)
define('PHONE_VALIDATION_REGEX', '/^((\+966)|0)?[5][0-9]{8}$/');

// Logging settings
define('WHATSAPP_LOG_ENABLED', true);
define('WHATSAPP_LOG_FILE', ROOT_PATH . 'logs/whatsapp.log');

/**
 * Format phone number for WhatsApp
 * 
 * @param string $phone_number
 * @return string|false
 */
function format_whatsapp_phone($phone_number) {
    // Remove all non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone_number);
    
    // Handle Saudi Arabia numbers
    if (preg_match('/^0?5[0-9]{8}$/', $phone)) {
        // Remove leading zero if present
        $phone = ltrim($phone, '0');
        // Add country code
        return '+966' . $phone;
    }
    
    // Handle numbers that already have country code
    if (preg_match('/^966[5][0-9]{8}$/', $phone)) {
        return '+' . $phone;
    }
    
    // Handle international format
    if (preg_match('/^\+[1-9][0-9]{1,14}$/', $phone_number)) {
        return $phone_number;
    }
    
    return false;
}

/**
 * Log WhatsApp activity
 * 
 * @param string $message
 * @param string $level
 */
function log_whatsapp_activity($message, $level = 'INFO') {
    if (!WHATSAPP_LOG_ENABLED) {
        return;
    }
    
    $log_dir = dirname(WHATSAPP_LOG_FILE);
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    file_put_contents(WHATSAPP_LOG_FILE, $log_message, FILE_APPEND | LOCK_EX);
}

/**
 * Check if WhatsApp notifications are enabled
 * 
 * @return bool
 */
function is_whatsapp_enabled() {
    return WHATSAPP_NOTIFICATIONS_ENABLED && WHATSAPP_SERVICE !== 'disabled';
}

/**
 * Validate WhatsApp configuration
 * 
 * @return array
 */
function validate_whatsapp_config() {
    $errors = [];
    
    if (!is_whatsapp_enabled()) {
        return ['WhatsApp notifications are disabled'];
    }
    
    switch (WHATSAPP_SERVICE) {
        case 'twilio':
            if (empty(TWILIO_ACCOUNT_SID) || TWILIO_ACCOUNT_SID === 'your_twilio_account_sid_here') {
                $errors[] = 'Twilio Account SID is not configured';
            }
            if (empty(TWILIO_AUTH_TOKEN) || TWILIO_AUTH_TOKEN === 'your_twilio_auth_token_here') {
                $errors[] = 'Twilio Auth Token is not configured';
            }
            break;
            
        case 'ultramsg':
            if (empty(ULTRAMSG_TOKEN) || ULTRAMSG_TOKEN === 'your_ultramsg_token_here') {
                $errors[] = 'Ultramsg Token is not configured';
            }
            if (empty(ULTRAMSG_INSTANCE_ID) || ULTRAMSG_INSTANCE_ID === 'your_instance_id_here') {
                $errors[] = 'Ultramsg Instance ID is not configured';
            }
            break;
            
        case 'wati':
            if (empty(WATI_API_KEY) || WATI_API_KEY === 'your_wati_api_key_here') {
                $errors[] = 'WATI API Key is not configured';
            }
            break;
            
        case '360dialog':
            if (empty(DIALOG_360_API_KEY) || DIALOG_360_API_KEY === 'your_360dialog_api_key_here') {
                $errors[] = '360Dialog API Key is not configured';
            }
            break;
            
        default:
            $errors[] = 'Invalid WhatsApp service selected';
    }
    
    return $errors;
}
?>
