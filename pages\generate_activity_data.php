<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/activity_logger.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success_messages = [];
$error_messages = [];

try {
    // Create activity_logs table
    create_activity_logs_table($pdo);
    $success_messages[] = "تم إنشاء جدول سجلات الأنشطة";

    // Get some users for generating activities
    $stmt = $pdo->query("
        SELECT u.user_id, u.username, u.full_name, r.role_name 
        FROM users u 
        JOIN roles r ON u.role_id = r.role_id 
        WHERE u.is_active = 1 
        LIMIT 10
    ");
    $users = $stmt->fetchAll();

    if (empty($users)) {
        $error_messages[] = "لا توجد مستخدمين في النظام لإنشاء أنشطة تجريبية";
    } else {
        $activity_count = 0;

        // Generate login/logout activities for the last 7 days
        foreach ($users as $user) {
            for ($day = 1; $day <= 7; $day++) {
                $login_time = date('Y-m-d H:i:s', strtotime("-{$day} days") + rand(28800, 32400)); // 8-9 AM
                $logout_time = date('Y-m-d H:i:s', strtotime("-{$day} days") + rand(57600, 64800)); // 4-6 PM

                // Login activity
                $stmt = $pdo->prepare("
                    INSERT INTO activity_logs (category, action, description, user_id, ip_address, user_agent, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    'AUTH',
                    'login_success',
                    "تسجيل دخول ناجح للمستخدم: {$user['username']}",
                    $user['user_id'],
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    $login_time
                ]);
                $activity_count++;

                // Logout activity
                $stmt->execute([
                    'AUTH',
                    'logout',
                    "تسجيل خروج للمستخدم: {$user['username']}",
                    $user['user_id'],
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    $logout_time
                ]);
                $activity_count++;
            }
        }

        // Generate user management activities
        $admin_users = array_filter($users, function($u) { 
            return in_array($u['role_name'], ['system_owner', 'center_admin']); 
        });

        foreach ($admin_users as $admin) {
            for ($i = 1; $i <= 3; $i++) {
                $activity_time = date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(36000, 54000));
                
                $activities = [
                    "تم تحديث بيانات المستخدم: {$users[array_rand($users)]['full_name']}",
                    "تم إنشاء مستخدم جديد: طالب جديد",
                    "تم تفعيل حساب المستخدم: {$users[array_rand($users)]['full_name']}",
                    "تم تغيير كلمة مرور المستخدم: {$users[array_rand($users)]['full_name']}"
                ];

                $stmt->execute([
                    'USER',
                    'user_updated',
                    $activities[array_rand($activities)],
                    $admin['user_id'],
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    $activity_time
                ]);
                $activity_count++;
            }
        }

        // Generate attendance activities
        $teachers = array_filter($users, function($u) { return $u['role_name'] === 'teacher'; });
        
        foreach ($teachers as $teacher) {
            for ($day = 1; $day <= 5; $day++) {
                for ($student = 1; $student <= 3; $student++) {
                    $activity_time = date('Y-m-d H:i:s', strtotime("-{$day} days") + rand(36000, 43200));
                    $statuses = ['حاضر', 'غائب بعذر', 'غائب بدون عذر', 'متأخر'];
                    $status = $statuses[array_rand($statuses)];
                    $student_name = "الطالب " . chr(65 + $student);

                    $stmt->execute([
                        'ATTENDANCE',
                        'attendance_recorded',
                        "تسجيل حضور: {$student_name} - {$status} - " . date('Y-m-d', strtotime("-{$day} days")),
                        $teacher['user_id'],
                        '192.168.1.' . rand(100, 200),
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        $activity_time
                    ]);
                    $activity_count++;
                }
            }
        }

        // Generate memorization activities
        foreach ($teachers as $teacher) {
            for ($day = 1; $day <= 4; $day++) {
                $activity_time = date('Y-m-d H:i:s', strtotime("-{$day} days") + rand(36000, 43200));
                $surahs = ['الفاتحة', 'البقرة', 'آل عمران', 'النساء', 'المائدة'];
                $qualities = ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف'];
                $surah = $surahs[array_rand($surahs)];
                $quality = $qualities[array_rand($qualities)];
                $student_name = "الطالب " . chr(65 + rand(1, 5));

                $stmt->execute([
                    'MEMORIZATION',
                    'memorization_evaluated',
                    "تقييم حفظ: {$student_name} - سورة {$surah} (آية 1 إلى 10) - {$quality}",
                    $teacher['user_id'],
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    $activity_time
                ]);
                $activity_count++;
            }
        }

        // Generate announcement activities
        foreach ($admin_users as $admin) {
            for ($i = 1; $i <= 2; $i++) {
                $activity_time = date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(32400, 39600));
                $announcements = [
                    "إعلان مهم للطلاب",
                    "إشعار للمعلمين",
                    "تحديث في النظام",
                    "اجتماع أولياء الأمور"
                ];
                $targets = ['الطلاب', 'المعلمين', 'الجميع', 'أولياء الأمور'];

                $announcement = $announcements[array_rand($announcements)];
                $target = $targets[array_rand($targets)];

                $stmt->execute([
                    'ANNOUNCEMENT',
                    'announcement_created',
                    "تم إنشاء إعلان: {$announcement} (مستهدف: {$target})",
                    $admin['user_id'],
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    $activity_time
                ]);
                $activity_count++;
            }
        }

        // Generate WhatsApp activities
        foreach ($admin_users as $admin) {
            for ($i = 1; $i <= 3; $i++) {
                $activity_time = date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(39600, 50400));
                $numbers = ['0501234567', '0551234568', '0561234569', '0571234570'];
                $message_types = ['إشعار غياب', 'تذكير بالواجب', 'إعلان عام', 'ملخص الحضور'];
                $statuses = ['تم الإرسال', 'فشل الإرسال'];

                $number = $numbers[array_rand($numbers)];
                $message_type = $message_types[array_rand($message_types)];
                $status = $statuses[array_rand($statuses)];

                $stmt->execute([
                    'WHATSAPP',
                    'message_sent',
                    "رسالة واتساب: {$message_type} إلى {$number} - {$status}",
                    $admin['user_id'],
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    $activity_time
                ]);
                $activity_count++;
            }
        }

        // Generate enrollment activities
        foreach ($admin_users as $admin) {
            for ($i = 1; $i <= 2; $i++) {
                $activity_time = date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(28800, 36000));
                $student_names = ['أحمد محمد', 'فاطمة علي', 'محمد أحمد', 'عائشة محمد', 'عبدالله علي'];
                $circle_names = ['حلقة الفجر', 'حلقة النور', 'حلقة الهدى', 'حلقة البركة'];

                $student_name = $student_names[array_rand($student_names)];
                $circle_name = $circle_names[array_rand($circle_names)];

                $stmt->execute([
                    'ENROLLMENT',
                    'student_enrolled',
                    "تسجيل طالب: {$student_name} في حلقة {$circle_name}",
                    $admin['user_id'],
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    $activity_time
                ]);
                $activity_count++;
            }
        }

        // Generate system activities
        foreach ($admin_users as $admin) {
            $system_activities = [
                "تم تحديث إعدادات النظام",
                "تم إجراء نسخ احتياطي للبيانات",
                "تم تحديث قاعدة البيانات",
                "تم تنظيف ملفات النظام المؤقتة",
                "تم فحص أمان النظام"
            ];

            for ($i = 1; $i <= 2; $i++) {
                $activity_time = date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(21600, 28800));
                $activity = $system_activities[array_rand($system_activities)];

                $stmt->execute([
                    'SYSTEM',
                    'system_maintenance',
                    $activity,
                    $admin['user_id'],
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    $activity_time
                ]);
                $activity_count++;
            }
        }

        $success_messages[] = "تم إنشاء {$activity_count} نشاط تجريبي شامل";
        $success_messages[] = "تم إنشاء أنشطة تسجيل دخول وخروج لجميع المستخدمين";
        $success_messages[] = "تم إنشاء أنشطة إدارة المستخدمين والحضور والتقييم";
        $success_messages[] = "تم إنشاء أنشطة الإعلانات والواتساب والتسجيل";
    }

} catch (PDOException $e) {
    $error_messages[] = 'حدث خطأ أثناء إنشاء الأنشطة: ' . $e->getMessage();
} catch (Exception $e) {
    $error_messages[] = 'حدث خطأ عام: ' . $e->getMessage();
}

// Redirect back to user activity page with messages
if (!empty($success_messages)) {
    set_flash_message('success', implode('<br>', $success_messages));
}
if (!empty($error_messages)) {
    set_flash_message('danger', implode('<br>', $error_messages));
}

redirect('user_activity.php');
?>
