<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';

// Handle user actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $user_id = (int)$_GET['id'];
    
    if ($action === 'delete') {
        try {
            // Check if user is the current logged in user
            if ($user_id == $_SESSION['user_id']) {
                $error = 'لا يمكنك حذف حسابك الحالي';
            } else {
                // Delete user
                $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $success = 'تم حذف المستخدم بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء حذف المستخدم: ' . $e->getMessage();
        }
    } elseif ($action === 'toggle') {
        try {
            // Check if user is the current logged in user
            if ($user_id == $_SESSION['user_id']) {
                $error = 'لا يمكنك تغيير حالة حسابك الحالي';
            } else {
                // Toggle user active status
                $stmt = $pdo->prepare("UPDATE users SET is_active = NOT is_active WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $success = 'تم تغيير حالة المستخدم بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تغيير حالة المستخدم: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$role_filter = isset($_GET['role']) ? $_GET['role'] : '';
$center_filter = isset($_GET['center']) ? (int)$_GET['center'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Build query based on filters
$query = "
    SELECT u.*, r.role_name, c.center_name
    FROM users u
    JOIN roles r ON u.role_id = r.role_id
    LEFT JOIN centers c ON u.center_id = c.center_id
    WHERE 1=1
";

$params = [];

if (!empty($role_filter)) {
    $query .= " AND r.role_name = ?";
    $params[] = $role_filter;
}

if (!empty($center_filter)) {
    $query .= " AND u.center_id = ?";
    $params[] = $center_filter;
}

if ($status_filter === 'active') {
    $query .= " AND u.is_active = 1";
} elseif ($status_filter === 'inactive') {
    $query .= " AND u.is_active = 0";
}

$query .= " ORDER BY u.full_name";

// Get users based on filters
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المستخدمين: ' . $e->getMessage();
}

// Get roles for filter
try {
    $stmt = $pdo->prepare("SELECT role_name FROM roles ORDER BY role_id");
    $stmt->execute();
    $roles = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الأدوار: ' . $e->getMessage();
}

// Get centers for filter
try {
    $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
    $stmt->execute();
    $centers = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
}

// Role names in Arabic
$role_names = [
    'system_owner' => 'مالك النظام',
    'center_admin' => 'مدير مركز',
    'teacher' => 'معلم',
    'student' => 'طالب',
    'parent' => 'ولي أمر'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="system_owner_dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="centers.php">المراكز</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="users.php">المستخدمين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="system_settings.php">إعدادات النظام</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <main class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-users me-2"></i> إدارة المستخدمين</h1>
            <div class="btn-group">
                <a href="add_admin.php" class="btn btn-primary">
                    <i class="fas fa-user-shield me-1"></i> إضافة مدير مركز
                </a>
                <a href="add_teacher.php" class="btn btn-success">
                    <i class="fas fa-chalkboard-teacher me-1"></i> إضافة معلم
                </a>
                <a href="add_student.php" class="btn btn-info">
                    <i class="fas fa-user-graduate me-1"></i> إضافة طالب
                </a>
            </div>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">تصفية المستخدمين</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="role" class="form-label">الدور</label>
                        <select class="form-select" id="role" name="role">
                            <option value="">الكل</option>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role; ?>" <?php echo $role_filter === $role ? 'selected' : ''; ?>>
                                    <?php echo $role_names[$role] ?? $role; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="center" class="form-label">المركز</label>
                        <select class="form-select" id="center" name="center">
                            <option value="">الكل</option>
                            <?php foreach ($centers as $center): ?>
                                <option value="<?php echo $center['center_id']; ?>" <?php echo $center_filter == $center['center_id'] ? 'selected' : ''; ?>>
                                    <?php echo $center['center_name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">الكل</option>
                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i> تصفية
                        </button>
                        <a href="users.php" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i> إعادة ضبط
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <?php if (empty($users)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا يوجد مستخدمين مطابقين لمعايير البحث.
            </div>
        <?php else: ?>
            <div class="card shadow">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="usersTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>المركز</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $user['full_name']; ?></strong>
                                        </td>
                                        <td><?php echo $user['username']; ?></td>
                                        <td><?php echo $user['email']; ?></td>
                                        <td>
                                            <?php 
                                                $role_badge_class = '';
                                                switch ($user['role_name']) {
                                                    case 'system_owner':
                                                        $role_badge_class = 'bg-danger';
                                                        break;
                                                    case 'center_admin':
                                                        $role_badge_class = 'bg-warning text-dark';
                                                        break;
                                                    case 'teacher':
                                                        $role_badge_class = 'bg-success';
                                                        break;
                                                    case 'student':
                                                        $role_badge_class = 'bg-info text-dark';
                                                        break;
                                                    case 'parent':
                                                        $role_badge_class = 'bg-secondary';
                                                        break;
                                                }
                                            ?>
                                            <span class="badge <?php echo $role_badge_class; ?>">
                                                <?php echo $role_names[$user['role_name']] ?? $user['role_name']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($user['center_name']): ?>
                                                <?php echo $user['center_name']; ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                        <td>
                                            <?php if ($user['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="user_details.php?id=<?php echo $user['user_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_user.php?id=<?php echo $user['user_id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($user['user_id'] != $_SESSION['user_id']): ?>
                                                    <a href="users.php?action=toggle&id=<?php echo $user['user_id']; ?>" class="btn btn-sm btn-secondary" title="<?php echo $user['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                                        <i class="fas <?php echo $user['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                                    </a>
                                                    <a href="users.php?action=delete&id=<?php echo $user['user_id']; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </main>
    
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#usersTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                },
                "order": [[ 0, "asc" ]]
            });
        });
    </script>
</body>
</html>
