<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('auth/login.php');
}

$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$error = '';
$success = '';

// Get inbox messages
try {
    // Check if is_deleted_by_recipient column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'messages'
        AND COLUMN_NAME = 'is_deleted_by_recipient'
    ");
    $stmt->execute();
    $column_exists = (bool)$stmt->fetchColumn();

    if ($column_exists) {
        $stmt = $pdo->prepare("
            SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                   u.full_name AS sender_name, u.role_id, r.role_name
            FROM messages m
            JOIN users u ON m.sender_user_id = u.user_id
            JOIN roles r ON u.role_id = r.role_id
            WHERE m.recipient_user_id = ? AND m.is_deleted_by_recipient = FALSE
            ORDER BY m.sent_at DESC
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                   u.full_name AS sender_name, u.role_id, r.role_name
            FROM messages m
            JOIN users u ON m.sender_user_id = u.user_id
            JOIN roles r ON u.role_id = r.role_id
            WHERE m.recipient_user_id = ?
            ORDER BY m.sent_at DESC
        ");
    }
    $stmt->execute([$user_id]);
    $inbox_messages = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الرسائل الواردة: ' . $e->getMessage();
}

// Get sent messages
try {
    // Check if is_deleted_by_sender column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'messages'
        AND COLUMN_NAME = 'is_deleted_by_sender'
    ");
    $stmt->execute();
    $column_exists = (bool)$stmt->fetchColumn();

    if ($column_exists) {
        $stmt = $pdo->prepare("
            SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                   u.full_name AS recipient_name, u.role_id, r.role_name
            FROM messages m
            JOIN users u ON m.recipient_user_id = u.user_id
            JOIN roles r ON u.role_id = r.role_id
            WHERE m.sender_user_id = ? AND m.is_deleted_by_sender = FALSE
            ORDER BY m.sent_at DESC
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                   u.full_name AS recipient_name, u.role_id, r.role_name
            FROM messages m
            JOIN users u ON m.recipient_user_id = u.user_id
            JOIN roles r ON u.role_id = r.role_id
            WHERE m.sender_user_id = ?
            ORDER BY m.sent_at DESC
        ");
    }
    $stmt->execute([$user_id]);
    $sent_messages = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الرسائل المرسلة: ' . $e->getMessage();
}

// Get unread messages count
try {
    // Check if is_deleted_by_recipient column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'messages'
        AND COLUMN_NAME = 'is_deleted_by_recipient'
    ");
    $stmt->execute();
    $column_exists = (bool)$stmt->fetchColumn();

    if ($column_exists) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS unread_count
            FROM messages
            WHERE recipient_user_id = ? AND read_at IS NULL AND is_deleted_by_recipient = FALSE
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS unread_count
            FROM messages
            WHERE recipient_user_id = ? AND read_at IS NULL
        ");
    }
    $stmt->execute([$user_id]);
    $unread_count = $stmt->fetch()['unread_count'];
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع عدد الرسائل غير المقروءة: ' . $e->getMessage();
}

// Get potential recipients based on user role
try {
    if ($role_name === 'system_owner') {
        // System owner can message anyone
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.full_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id != ? AND u.is_active = TRUE
            ORDER BY r.role_name, u.full_name
        ");
        $stmt->execute([$user_id]);
    } elseif ($role_name === 'center_admin') {
        // Center admin can message teachers, students, and parents in their center
        $stmt = $pdo->prepare("
            SELECT u.user_id, u.full_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id != ? AND u.is_active = TRUE
            AND (
                (u.center_id = ? AND r.role_name IN ('teacher', 'student'))
                OR
                (r.role_name = 'parent' AND EXISTS (
                    SELECT 1 FROM student_circle_enrollments sce
                    JOIN users s ON sce.student_user_id = s.user_id
                    WHERE sce.parent_user_id = u.user_id AND s.center_id = ?
                ))
                OR
                (r.role_name = 'system_owner')
            )
            ORDER BY r.role_name, u.full_name
        ");
        $stmt->execute([$user_id, $_SESSION['center_id'], $_SESSION['center_id']]);
    } elseif ($role_name === 'teacher') {
        // Teacher can message students in their circles, their parents, and center admin
        $stmt = $pdo->prepare("
            SELECT DISTINCT u.user_id, u.full_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id != ? AND u.is_active = TRUE
            AND (
                (r.role_name = 'student' AND EXISTS (
                    SELECT 1 FROM student_circle_enrollments sce
                    JOIN circles c ON sce.circle_id = c.circle_id
                    WHERE sce.student_user_id = u.user_id AND c.teacher_user_id = ?
                ))
                OR
                (r.role_name = 'parent' AND EXISTS (
                    SELECT 1 FROM student_circle_enrollments sce
                    JOIN circles c ON sce.circle_id = c.circle_id
                    WHERE sce.parent_user_id = u.user_id AND c.teacher_user_id = ?
                ))
                OR
                (r.role_name = 'center_admin' AND u.center_id = (
                    SELECT center_id FROM users WHERE user_id = ?
                ))
                OR
                (r.role_name = 'system_owner')
            )
            ORDER BY r.role_name, u.full_name
        ");
        $stmt->execute([$user_id, $user_id, $user_id, $user_id]);
    } elseif ($role_name === 'student') {
        // Student can message their teachers and center admin
        $stmt = $pdo->prepare("
            SELECT DISTINCT u.user_id, u.full_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id != ? AND u.is_active = TRUE
            AND (
                (r.role_name = 'teacher' AND EXISTS (
                    SELECT 1 FROM student_circle_enrollments sce
                    JOIN circles c ON sce.circle_id = c.circle_id
                    WHERE sce.student_user_id = ? AND c.teacher_user_id = u.user_id
                ))
                OR
                (r.role_name = 'center_admin' AND u.center_id = (
                    SELECT center_id FROM users WHERE user_id = ?
                ))
            )
            ORDER BY r.role_name, u.full_name
        ");
        $stmt->execute([$user_id, $user_id, $user_id]);
    } elseif ($role_name === 'parent') {
        // Parent can message their children's teachers and center admin
        $stmt = $pdo->prepare("
            SELECT DISTINCT u.user_id, u.full_name, r.role_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id != ? AND u.is_active = TRUE
            AND (
                (r.role_name = 'teacher' AND EXISTS (
                    SELECT 1 FROM student_circle_enrollments sce
                    JOIN circles c ON sce.circle_id = c.circle_id
                    WHERE sce.parent_user_id = ? AND c.teacher_user_id = u.user_id
                ))
                OR
                (r.role_name = 'center_admin' AND EXISTS (
                    SELECT 1 FROM student_circle_enrollments sce
                    JOIN users s ON sce.student_user_id = s.user_id
                    WHERE sce.parent_user_id = ? AND s.center_id = u.center_id
                ))
            )
            ORDER BY r.role_name, u.full_name
        ");
        $stmt->execute([$user_id, $user_id, $user_id]);
    }

    $recipients = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع قائمة المستلمين: ' . $e->getMessage();
}

// Process send message form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $recipient_id = (int)$_POST['recipient_id'];
    $subject = sanitize_input($_POST['subject']);
    $message_text = sanitize_input($_POST['message_text']);

    // Validate input
    if (empty($recipient_id) || empty($subject) || empty($message_text)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // Verify recipient is valid
            $valid_recipient = false;
            foreach ($recipients as $recipient) {
                if ($recipient['user_id'] == $recipient_id) {
                    $valid_recipient = true;
                    break;
                }
            }

            if (!$valid_recipient) {
                $error = 'المستلم غير صالح';
            } else {
                // Insert message
                $stmt = $pdo->prepare("
                    INSERT INTO messages (sender_user_id, recipient_user_id, subject, message_text, sent_at)
                    VALUES (?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$user_id, $recipient_id, $subject, $message_text]);

                $success = 'تم إرسال الرسالة بنجاح';

                // Refresh sent messages
                // Check if is_deleted_by_sender column exists
                $stmt = $pdo->prepare("
                    SELECT COUNT(*)
                    FROM information_schema.COLUMNS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'messages'
                    AND COLUMN_NAME = 'is_deleted_by_sender'
                ");
                $stmt->execute();
                $column_exists = (bool)$stmt->fetchColumn();

                if ($column_exists) {
                    $stmt = $pdo->prepare("
                        SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                               u.full_name AS recipient_name, u.role_id, r.role_name
                        FROM messages m
                        JOIN users u ON m.recipient_user_id = u.user_id
                        JOIN roles r ON u.role_id = r.role_id
                        WHERE m.sender_user_id = ? AND m.is_deleted_by_sender = FALSE
                        ORDER BY m.sent_at DESC
                    ");
                } else {
                    $stmt = $pdo->prepare("
                        SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                               u.full_name AS recipient_name, u.role_id, r.role_name
                        FROM messages m
                        JOIN users u ON m.recipient_user_id = u.user_id
                        JOIN roles r ON u.role_id = r.role_id
                        WHERE m.sender_user_id = ?
                        ORDER BY m.sent_at DESC
                    ");
                }
                $stmt->execute([$user_id]);
                $sent_messages = $stmt->fetchAll();
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إرسال الرسالة: ' . $e->getMessage();
        }
    }
}

// Process delete message form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_message'])) {
    $message_id = (int)$_POST['message_id'];
    $message_type = sanitize_input($_POST['message_type']);

    try {
        // Check if deletion columns exist
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'messages'
            AND COLUMN_NAME = 'is_deleted_by_recipient'
        ");
        $stmt->execute();
        $recipient_column_exists = (bool)$stmt->fetchColumn();

        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'messages'
            AND COLUMN_NAME = 'is_deleted_by_sender'
        ");
        $stmt->execute();
        $sender_column_exists = (bool)$stmt->fetchColumn();

        if ($message_type === 'inbox') {
            if ($recipient_column_exists) {
                // Delete from inbox (mark as deleted by recipient)
                $stmt = $pdo->prepare("
                    UPDATE messages
                    SET is_deleted_by_recipient = TRUE
                    WHERE message_id = ? AND recipient_user_id = ?
                ");
                $stmt->execute([$message_id, $user_id]);
            } else {
                // If column doesn't exist, actually delete the message
                $stmt = $pdo->prepare("
                    DELETE FROM messages
                    WHERE message_id = ? AND recipient_user_id = ?
                ");
                $stmt->execute([$message_id, $user_id]);
            }

            // Refresh inbox messages
            if ($recipient_column_exists) {
                $stmt = $pdo->prepare("
                    SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                           u.full_name AS sender_name, u.role_id, r.role_name
                    FROM messages m
                    JOIN users u ON m.sender_user_id = u.user_id
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE m.recipient_user_id = ? AND m.is_deleted_by_recipient = FALSE
                    ORDER BY m.sent_at DESC
                ");
            } else {
                $stmt = $pdo->prepare("
                    SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                           u.full_name AS sender_name, u.role_id, r.role_name
                    FROM messages m
                    JOIN users u ON m.sender_user_id = u.user_id
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE m.recipient_user_id = ?
                    ORDER BY m.sent_at DESC
                ");
            }
            $stmt->execute([$user_id]);
            $inbox_messages = $stmt->fetchAll();
        } else { // sent
            if ($sender_column_exists) {
                // Delete from sent (mark as deleted by sender)
                $stmt = $pdo->prepare("
                    UPDATE messages
                    SET is_deleted_by_sender = TRUE
                    WHERE message_id = ? AND sender_user_id = ?
                ");
                $stmt->execute([$message_id, $user_id]);
            } else {
                // If column doesn't exist, actually delete the message
                $stmt = $pdo->prepare("
                    DELETE FROM messages
                    WHERE message_id = ? AND sender_user_id = ?
                ");
                $stmt->execute([$message_id, $user_id]);
            }

            // Refresh sent messages
            if ($sender_column_exists) {
                $stmt = $pdo->prepare("
                    SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                           u.full_name AS recipient_name, u.role_id, r.role_name
                    FROM messages m
                    JOIN users u ON m.recipient_user_id = u.user_id
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE m.sender_user_id = ? AND m.is_deleted_by_sender = FALSE
                    ORDER BY m.sent_at DESC
                ");
            } else {
                $stmt = $pdo->prepare("
                    SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                           u.full_name AS recipient_name, u.role_id, r.role_name
                    FROM messages m
                    JOIN users u ON m.recipient_user_id = u.user_id
                    JOIN roles r ON u.role_id = r.role_id
                    WHERE m.sender_user_id = ?
                    ORDER BY m.sent_at DESC
                ");
            }
            $stmt->execute([$user_id]);
            $sent_messages = $stmt->fetchAll();
        }

        $success = 'تم حذف الرسالة بنجاح';
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء حذف الرسالة: ' . $e->getMessage();
    }
}

// Process mark as read
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['mark_as_read'])) {
    $message_id = (int)$_POST['message_id'];

    try {
        // Mark message as read
        $stmt = $pdo->prepare("
            UPDATE messages
            SET read_at = NOW()
            WHERE message_id = ? AND recipient_user_id = ? AND read_at IS NULL
        ");
        $stmt->execute([$message_id, $user_id]);

        // Check if is_deleted_by_recipient column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'messages'
            AND COLUMN_NAME = 'is_deleted_by_recipient'
        ");
        $stmt->execute();
        $column_exists = (bool)$stmt->fetchColumn();

        // Refresh inbox messages
        if ($column_exists) {
            $stmt = $pdo->prepare("
                SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                       u.full_name AS sender_name, u.role_id, r.role_name
                FROM messages m
                JOIN users u ON m.sender_user_id = u.user_id
                JOIN roles r ON u.role_id = r.role_id
                WHERE m.recipient_user_id = ? AND m.is_deleted_by_recipient = FALSE
                ORDER BY m.sent_at DESC
            ");
        } else {
            $stmt = $pdo->prepare("
                SELECT m.message_id, m.subject, m.message_text, m.sent_at, m.read_at,
                       u.full_name AS sender_name, u.role_id, r.role_name
                FROM messages m
                JOIN users u ON m.sender_user_id = u.user_id
                JOIN roles r ON u.role_id = r.role_id
                WHERE m.recipient_user_id = ?
                ORDER BY m.sent_at DESC
            ");
        }
        $stmt->execute([$user_id]);
        $inbox_messages = $stmt->fetchAll();

        // Refresh unread count
        if ($column_exists) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) AS unread_count
                FROM messages
                WHERE recipient_user_id = ? AND read_at IS NULL AND is_deleted_by_recipient = FALSE
            ");
        } else {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) AS unread_count
                FROM messages
                WHERE recipient_user_id = ? AND read_at IS NULL
            ");
        }
        $stmt->execute([$user_id]);
        $unread_count = $stmt->fetch()['unread_count'];
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء تحديث حالة الرسالة: ' . $e->getMessage();
    }
}
?>

<?php
// Set page title
$page_title = 'الرسائل';

// Include header
include_once '../includes/header_inner.php';
?>

    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>الرسائل</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#composeMessageModal">
                <i class="fas fa-paper-plane me-1"></i> إرسال رسالة جديدة
            </button>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-3">
                <div class="list-group mb-4">
                    <a href="#inbox" class="list-group-item list-group-item-action active" data-bs-toggle="list">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-inbox me-2"></i> صندوق الوارد
                            </div>
                            <?php if ($unread_count > 0): ?>
                                <span class="badge bg-danger rounded-pill"><?php echo $unread_count; ?></span>
                            <?php endif; ?>
                        </div>
                    </a>
                    <a href="#sent" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="fas fa-paper-plane me-2"></i> الرسائل المرسلة
                    </a>
                </div>

                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i> معلومات
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>يمكنك استخدام نظام الرسائل للتواصل مع:</p>
                        <ul class="list-unstyled">
                            <?php if ($role_name === 'system_owner'): ?>
                                <li><i class="fas fa-check-circle text-success me-2"></i> جميع المستخدمين</li>
                            <?php elseif ($role_name === 'center_admin'): ?>
                                <li><i class="fas fa-check-circle text-success me-2"></i> المعلمين في مركزك</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> الطلاب في مركزك</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> أولياء أمور الطلاب في مركزك</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> مدير النظام</li>
                            <?php elseif ($role_name === 'teacher'): ?>
                                <li><i class="fas fa-check-circle text-success me-2"></i> الطلاب في حلقاتك</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> أولياء أمور الطلاب في حلقاتك</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> مدير المركز</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> مدير النظام</li>
                            <?php elseif ($role_name === 'student'): ?>
                                <li><i class="fas fa-check-circle text-success me-2"></i> معلمي حلقاتك</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> مدير المركز</li>
                            <?php elseif ($role_name === 'parent'): ?>
                                <li><i class="fas fa-check-circle text-success me-2"></i> معلمي أبنائك</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> مدير المركز</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="inbox">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-inbox me-2"></i> صندوق الوارد
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($inbox_messages)): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> لا توجد رسائل في صندوق الوارد.
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>المرسل</th>
                                                    <th>الموضوع</th>
                                                    <th>التاريخ</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($inbox_messages as $message): ?>
                                                    <tr class="<?php echo $message['read_at'] ? '' : 'table-primary'; ?>">
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <span class="me-2">
                                                                    <?php
                                                                    $role_icon = '';
                                                                    switch ($message['role_name']) {
                                                                        case 'system_owner':
                                                                            $role_icon = 'fas fa-crown text-warning';
                                                                            break;
                                                                        case 'center_admin':
                                                                            $role_icon = 'fas fa-user-tie text-danger';
                                                                            break;
                                                                        case 'teacher':
                                                                            $role_icon = 'fas fa-chalkboard-teacher text-info';
                                                                            break;
                                                                        case 'student':
                                                                            $role_icon = 'fas fa-user-graduate text-success';
                                                                            break;
                                                                        case 'parent':
                                                                            $role_icon = 'fas fa-user-friends text-primary';
                                                                            break;
                                                                        default:
                                                                            $role_icon = 'fas fa-user';
                                                                    }
                                                                    echo '<i class="' . $role_icon . '"></i>';
                                                                    ?>
                                                                </span>
                                                                <?php echo $message['sender_name']; ?>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <?php if (!$message['read_at']): ?>
                                                                <span class="badge bg-danger me-1">جديد</span>
                                                            <?php endif; ?>
                                                            <?php echo $message['subject']; ?>
                                                        </td>
                                                        <td><?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?></td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-info view-message"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#viewMessageModal"
                                                                    data-message-id="<?php echo $message['message_id']; ?>"
                                                                    data-sender="<?php echo $message['sender_name']; ?>"
                                                                    data-subject="<?php echo $message['subject']; ?>"
                                                                    data-message="<?php echo htmlspecialchars($message['message_text']); ?>"
                                                                    data-date="<?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?>"
                                                                    data-read="<?php echo $message['read_at'] ? 'true' : 'false'; ?>">
                                                                <i class="fas fa-eye"></i> عرض
                                                            </button>

                                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteMessageModal<?php echo $message['message_id']; ?>_inbox">
                                                                <i class="fas fa-trash"></i> حذف
                                                            </button>

                                                            <!-- Delete Message Modal -->
                                                            <div class="modal fade" id="deleteMessageModal<?php echo $message['message_id']; ?>_inbox" tabindex="-1" aria-labelledby="deleteMessageModalLabel<?php echo $message['message_id']; ?>_inbox" aria-hidden="true">
                                                                <div class="modal-dialog">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title" id="deleteMessageModalLabel<?php echo $message['message_id']; ?>_inbox">تأكيد الحذف</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            <p>هل أنت متأكد من رغبتك في حذف الرسالة "<?php echo $message['subject']; ?>"؟</p>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                                                                                <input type="hidden" name="message_id" value="<?php echo $message['message_id']; ?>">
                                                                                <input type="hidden" name="message_type" value="inbox">
                                                                                <button type="submit" name="delete_message" class="btn btn-danger">حذف</button>
                                                                            </form>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="sent">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-paper-plane me-2"></i> الرسائل المرسلة
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($sent_messages)): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> لا توجد رسائل مرسلة.
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>المستلم</th>
                                                    <th>الموضوع</th>
                                                    <th>التاريخ</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($sent_messages as $message): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <span class="me-2">
                                                                    <?php
                                                                    $role_icon = '';
                                                                    switch ($message['role_name']) {
                                                                        case 'system_owner':
                                                                            $role_icon = 'fas fa-crown text-warning';
                                                                            break;
                                                                        case 'center_admin':
                                                                            $role_icon = 'fas fa-user-tie text-danger';
                                                                            break;
                                                                        case 'teacher':
                                                                            $role_icon = 'fas fa-chalkboard-teacher text-info';
                                                                            break;
                                                                        case 'student':
                                                                            $role_icon = 'fas fa-user-graduate text-success';
                                                                            break;
                                                                        case 'parent':
                                                                            $role_icon = 'fas fa-user-friends text-primary';
                                                                            break;
                                                                        default:
                                                                            $role_icon = 'fas fa-user';
                                                                    }
                                                                    echo '<i class="' . $role_icon . '"></i>';
                                                                    ?>
                                                                </span>
                                                                <?php echo $message['recipient_name']; ?>
                                                            </div>
                                                        </td>
                                                        <td><?php echo $message['subject']; ?></td>
                                                        <td><?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?></td>
                                                        <td>
                                                            <?php
                                                            if ($message['read_at']) {
                                                                echo '<span class="badge bg-success">تمت القراءة</span>';
                                                                echo '<br><small class="text-muted">' . date('Y-m-d H:i', strtotime($message['read_at'])) . '</small>';
                                                            } else {
                                                                echo '<span class="badge bg-warning">لم تتم القراءة</span>';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-info"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#viewSentMessageModal"
                                                                    data-recipient="<?php echo $message['recipient_name']; ?>"
                                                                    data-subject="<?php echo $message['subject']; ?>"
                                                                    data-message="<?php echo htmlspecialchars($message['message_text']); ?>"
                                                                    data-date="<?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?>">
                                                                <i class="fas fa-eye"></i> عرض
                                                            </button>

                                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteMessageModal<?php echo $message['message_id']; ?>_sent">
                                                                <i class="fas fa-trash"></i> حذف
                                                            </button>

                                                            <!-- Delete Message Modal -->
                                                            <div class="modal fade" id="deleteMessageModal<?php echo $message['message_id']; ?>_sent" tabindex="-1" aria-labelledby="deleteMessageModalLabel<?php echo $message['message_id']; ?>_sent" aria-hidden="true">
                                                                <div class="modal-dialog">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title" id="deleteMessageModalLabel<?php echo $message['message_id']; ?>_sent">تأكيد الحذف</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            <p>هل أنت متأكد من رغبتك في حذف الرسالة "<?php echo $message['subject']; ?>"؟</p>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                                            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                                                                                <input type="hidden" name="message_id" value="<?php echo $message['message_id']; ?>">
                                                                                <input type="hidden" name="message_type" value="sent">
                                                                                <button type="submit" name="delete_message" class="btn btn-danger">حذف</button>
                                                                            </form>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compose Message Modal -->
    <div class="modal fade" id="composeMessageModal" tabindex="-1" aria-labelledby="composeMessageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="composeMessageModalLabel">إرسال رسالة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="recipient_id" class="form-label">المستلم <span class="text-danger">*</span></label>
                            <select class="form-select" id="recipient_id" name="recipient_id" required>
                                <option value="">اختر المستلم</option>
                                <?php if (!empty($recipients)): ?>
                                    <?php
                                    $current_role = '';
                                    foreach ($recipients as $recipient):
                                        if ($current_role != $recipient['role_name']) {
                                            if ($current_role != '') {
                                                echo '</optgroup>';
                                            }
                                            $current_role = $recipient['role_name'];
                                            $role_label = '';
                                            switch ($current_role) {
                                                case 'system_owner':
                                                    $role_label = 'مدير النظام';
                                                    break;
                                                case 'center_admin':
                                                    $role_label = 'مدير المركز';
                                                    break;
                                                case 'teacher':
                                                    $role_label = 'المعلمين';
                                                    break;
                                                case 'student':
                                                    $role_label = 'الطلاب';
                                                    break;
                                                case 'parent':
                                                    $role_label = 'أولياء الأمور';
                                                    break;
                                                default:
                                                    $role_label = $current_role;
                                            }
                                            echo '<optgroup label="' . $role_label . '">';
                                        }
                                    ?>
                                        <option value="<?php echo $recipient['user_id']; ?>">
                                            <?php echo $recipient['full_name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                    <?php if ($current_role != '') echo '</optgroup>'; ?>
                                <?php endif; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">الموضوع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>

                        <div class="mb-3">
                            <label for="message_text" class="form-label">نص الرسالة <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message_text" name="message_text" rows="5" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="send_message" class="btn btn-primary">إرسال</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Message Modal -->
    <div class="modal fade" id="viewMessageModal" tabindex="-1" aria-labelledby="viewMessageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewMessageModalLabel">عرض الرسالة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <strong>المرسل:</strong> <span id="view-sender"></span>
                    </div>
                    <div class="mb-3">
                        <strong>الموضوع:</strong> <span id="view-subject"></span>
                    </div>
                    <div class="mb-3">
                        <strong>التاريخ:</strong> <span id="view-date"></span>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div id="view-message" class="p-3 bg-light rounded"></div>
                    </div>

                    <form id="mark-as-read-form" method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                        <input type="hidden" id="message-id" name="message_id" value="">
                        <input type="hidden" name="mark_as_read" value="1">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Sent Message Modal -->
    <div class="modal fade" id="viewSentMessageModal" tabindex="-1" aria-labelledby="viewSentMessageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewSentMessageModalLabel">عرض الرسالة المرسلة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <strong>المستلم:</strong> <span id="view-recipient"></span>
                    </div>
                    <div class="mb-3">
                        <strong>الموضوع:</strong> <span id="view-sent-subject"></span>
                    </div>
                    <div class="mb-3">
                        <strong>التاريخ:</strong> <span id="view-sent-date"></span>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <div id="view-sent-message" class="p-3 bg-light rounded"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <?php include_once '../includes/footer_inner.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle view message modal
            const viewMessageModal = document.getElementById('viewMessageModal');
            if (viewMessageModal) {
                viewMessageModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const messageId = button.getAttribute('data-message-id');
                    const sender = button.getAttribute('data-sender');
                    const subject = button.getAttribute('data-subject');
                    const message = button.getAttribute('data-message');
                    const date = button.getAttribute('data-date');
                    const read = button.getAttribute('data-read');

                    document.getElementById('view-sender').textContent = sender;
                    document.getElementById('view-subject').textContent = subject;
                    document.getElementById('view-date').textContent = date;
                    document.getElementById('view-message').textContent = message;
                    document.getElementById('message-id').value = messageId;

                    // If message is not read, mark it as read
                    if (read === 'false') {
                        document.getElementById('mark-as-read-form').submit();
                    }
                });
            }

            // Handle view sent message modal
            const viewSentMessageModal = document.getElementById('viewSentMessageModal');
            if (viewSentMessageModal) {
                viewSentMessageModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const recipient = button.getAttribute('data-recipient');
                    const subject = button.getAttribute('data-subject');
                    const message = button.getAttribute('data-message');
                    const date = button.getAttribute('data-date');

                    document.getElementById('view-recipient').textContent = recipient;
                    document.getElementById('view-sent-subject').textContent = subject;
                    document.getElementById('view-sent-date').textContent = date;
                    document.getElementById('view-sent-message').textContent = message;
                });
            }
        });
    </script>
</body>
</html>
