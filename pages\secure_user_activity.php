<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/activity_logger.php';
require_once '../includes/center_security.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in()) {
    set_flash_message('danger', 'يجب تسجيل الدخول أولاً');
    redirect('auth/login.php');
}

// Check if user has permission to view activity data
$user_role = $_SESSION['role_name'];
if (!in_array($user_role, ['system_owner', 'center_admin'])) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('dashboard.php');
}

$page_title = 'نشاط المستخدمين (آمن)';
include_once '../includes/header_inner.php';

// Get user's center for security
$user_center_id = get_user_center_id($pdo, $_SESSION['user_id']);

// Get filter parameters
$days_filter = $_GET['days'] ?? 30;
$activity_filter = $_GET['activity'] ?? '';
$user_filter = $_GET['user'] ?? '';
$page = max(1, $_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

$activities = [];
$error = null;

try {
    // Check which tables exist
    $existing_tables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }

    // Build secure queries with center restrictions
    $queries = [];
    $params = [];

    // 1. User registrations - with center security
    if (empty($activity_filter) || $activity_filter === 'تسجيل مستخدم جديد') {
        $user_query = create_secure_users_query($pdo, $_SESSION['user_id'], "
            AND u.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND u.created_at IS NOT NULL
        ");
        
        $user_registration_query = "
            SELECT 
                'تسجيل مستخدم جديد' as activity_type,
                CONCAT('تم تسجيل مستخدم جديد: ', u.full_name, ' (', r.role_name, ') - ', COALESCE(c.center_name, 'بدون مركز')) as description,
                u.created_at as timestamp,
                'النظام' as user_name,
                NULL as user_id,
                NULL as ip_address,
                'USER_REGISTRATION' as category,
                'INFO' as severity
            FROM (" . str_replace("SELECT u.*, r.role_name, c.center_name", "SELECT u.user_id, u.full_name, u.created_at, u.center_id, u.role_id", $user_query) . ") u
            JOIN roles r ON u.role_id = r.role_id
            LEFT JOIN centers c ON u.center_id = c.center_id
        ";
        
        $queries[] = $user_registration_query;
        $params[] = $days_filter;
    }

    // 2. Student enrollments - with center security
    if (empty($activity_filter) || $activity_filter === 'تسجيل في الحلقات') {
        $enrollment_query = create_secure_enrollments_query($pdo, $_SESSION['user_id'], "
            AND sce.enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND sce.enrollment_date IS NOT NULL
        ");
        
        $secure_enrollment_query = "
            SELECT 
                'تسجيل في الحلقات' as activity_type,
                CONCAT('تسجيل طالب: ', s.full_name, ' في حلقة ', c.circle_name, ' - ', cent.center_name,
                       CASE WHEN p.full_name IS NOT NULL THEN CONCAT(' (ولي الأمر: ', p.full_name, ')') ELSE '' END) as description,
                sce.enrollment_date as timestamp,
                'النظام' as user_name,
                NULL as user_id,
                NULL as ip_address,
                'ENROLLMENT' as category,
                'INFO' as severity
            FROM (" . $enrollment_query . ") sce_data
            JOIN student_circle_enrollments sce ON sce_data.enrollment_id = sce.enrollment_id
            JOIN users s ON sce.student_user_id = s.user_id
            JOIN circles c ON sce.circle_id = c.circle_id
            LEFT JOIN users p ON sce.parent_user_id = p.user_id
            LEFT JOIN centers cent ON c.center_id = cent.center_id
        ";
        
        // Simplified version
        $enrollment_simple_query = "
            SELECT 
                'تسجيل في الحلقات' as activity_type,
                CONCAT('تسجيل طالب: ', s.full_name, ' في حلقة ', c.circle_name, ' - ', cent.center_name) as description,
                sce.enrollment_date as timestamp,
                'النظام' as user_name,
                NULL as user_id,
                NULL as ip_address,
                'ENROLLMENT' as category,
                'INFO' as severity
            FROM student_circle_enrollments sce
            JOIN users s ON sce.student_user_id = s.user_id
            JOIN circles c ON sce.circle_id = c.circle_id
            LEFT JOIN centers cent ON c.center_id = cent.center_id
            WHERE sce.enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND sce.enrollment_date IS NOT NULL
        ";
        
        // Apply center security
        if ($user_center_id !== 'all') {
            if ($user_center_id === null) {
                $enrollment_simple_query .= " AND 1=0";
            } else {
                $enrollment_simple_query .= " AND c.center_id = " . intval($user_center_id);
            }
        }
        
        $queries[] = $enrollment_simple_query;
        $params[] = $days_filter;
    }

    // 3. Activity logs - with center security
    if (in_array('activity_logs', $existing_tables)) {
        $activity_logs_query = "
            SELECT 
                CASE al.category
                    WHEN 'AUTH' THEN 'تسجيل دخول/خروج'
                    WHEN 'USER' THEN 'إدارة المستخدمين'
                    WHEN 'ATTENDANCE' THEN 'تسجيل الحضور'
                    WHEN 'MEMORIZATION' THEN 'تقييم الحفظ'
                    WHEN 'ANNOUNCEMENT' THEN 'إنشاء إعلان'
                    WHEN 'WHATSAPP' THEN 'رسائل الواتساب'
                    WHEN 'ENROLLMENT' THEN 'تسجيل في الحلقات'
                    WHEN 'SYSTEM' THEN 'أنشطة النظام'
                    WHEN 'SECURITY' THEN 'أمان النظام'
                    ELSE al.category
                END as activity_type,
                al.description as description,
                al.created_at as timestamp,
                COALESCE(u.full_name, 'النظام') as user_name,
                al.user_id,
                al.ip_address,
                al.category as category,
                'INFO' as severity
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.user_id
            WHERE al.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND al.created_at IS NOT NULL
        ";
        
        // Apply center security for activity logs
        if ($user_center_id !== 'all') {
            if ($user_center_id === null) {
                $activity_logs_query .= " AND 1=0";
            } else {
                $activity_logs_query .= " AND (u.center_id = " . intval($user_center_id) . " OR u.center_id IS NULL)";
            }
        }
        
        $queries[] = $activity_logs_query;
        $params[] = $days_filter;
    }

    // 4. Attendance records - with center security
    if ((empty($activity_filter) || $activity_filter === 'تسجيل الحضور') && in_array('attendance_records', $existing_tables)) {
        $attendance_query = create_secure_attendance_query($pdo, $_SESSION['user_id'], "
            AND ar.session_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND ar.session_date IS NOT NULL
        ");
        
        $secure_attendance_query = "
            SELECT 
                'تسجيل الحضور' as activity_type,
                CONCAT('حضور: ', s.full_name, ' - ',
                       CASE ar.status
                           WHEN 'present' THEN 'حاضر'
                           WHEN 'absent_excused' THEN 'غائب بعذر'
                           WHEN 'absent_unexcused' THEN 'غائب بدون عذر'
                           WHEN 'late' THEN 'متأخر'
                           ELSE ar.status
                       END, ' - ', ar.session_date, ' - ', cent.center_name) as description,
                COALESCE(ar.recorded_at, CONCAT(ar.session_date, ' 12:00:00')) as timestamp,
                COALESCE(t.full_name, 'النظام') as user_name,
                ar.recorded_by_user_id as user_id,
                NULL as ip_address,
                'ATTENDANCE' as category,
                CASE ar.status
                    WHEN 'absent_unexcused' THEN 'WARNING'
                    WHEN 'late' THEN 'WARNING'
                    ELSE 'INFO'
                END as severity
            FROM attendance_records ar
            JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
            JOIN users s ON sce.student_user_id = s.user_id
            JOIN circles c ON sce.circle_id = c.circle_id
            LEFT JOIN users t ON ar.recorded_by_user_id = t.user_id
            LEFT JOIN centers cent ON c.center_id = cent.center_id
            WHERE ar.session_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND ar.session_date IS NOT NULL
        ";
        
        // Apply center security
        if ($user_center_id !== 'all') {
            if ($user_center_id === null) {
                $secure_attendance_query .= " AND 1=0";
            } else {
                $secure_attendance_query .= " AND c.center_id = " . intval($user_center_id);
            }
        }
        
        $queries[] = $secure_attendance_query;
        $params[] = $days_filter;
    }

    // Execute combined query
    if (!empty($queries)) {
        $final_query = "(" . implode(") UNION (", $queries) . ")";
        
        // Add user filter if specified and user has access
        if (!empty($user_filter)) {
            if (can_access_user($pdo, $_SESSION['user_id'], $user_filter)) {
                $final_query = "SELECT * FROM ({$final_query}) as combined WHERE user_id = ?";
                $params[] = $user_filter;
            } else {
                log_unauthorized_access($pdo, $_SESSION['user_id'], "user_activity", "filter_user_$user_filter");
                $error = 'غير مصرح لك بعرض أنشطة هذا المستخدم';
                $queries = [];
            }
        }
        
        if (!empty($queries)) {
            $final_query .= " ORDER BY timestamp DESC LIMIT {$per_page} OFFSET {$offset}";
            
            $stmt = $pdo->prepare($final_query);
            $stmt->execute($params);
            $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات النشاط: ' . $e->getMessage();
}

// Get accessible users for filter dropdown
try {
    $secure_users_query = create_secure_users_query($pdo, $_SESSION['user_id'], "AND u.is_active = 1 ORDER BY u.full_name");
    $stmt = $pdo->query($secure_users_query);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $users = [];
}

// Get total count for pagination
$total_activities = 0;
try {
    if (!empty($queries)) {
        $count_query = "SELECT COUNT(*) FROM (" . implode(") UNION (", array_map(function($q) {
            return str_replace("SELECT ", "SELECT 1 as dummy, ", $q);
        }, $queries)) . ") as combined";
        
        $stmt = $pdo->prepare($count_query);
        $stmt->execute(array_slice($params, 0, count($queries)));
        $total_activities = $stmt->fetchColumn();
    }
} catch (PDOException $e) {
    $total_activities = 0;
}

$total_pages = ceil($total_activities / $per_page);
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1><i class="fas fa-shield-alt me-2"></i> نشاط المستخدمين (آمن)</h1>
            <small class="text-muted">
                <?php if ($user_center_id === 'all'): ?>
                    <i class="fas fa-globe me-1"></i> عرض جميع المراكز
                <?php elseif ($user_center_id): ?>
                    <i class="fas fa-building me-1"></i> مركز: <?php echo htmlspecialchars($_SESSION['center_name'] ?? 'غير محدد'); ?>
                <?php else: ?>
                    <i class="fas fa-exclamation-triangle me-1"></i> لا يوجد مركز محدد
                <?php endif; ?>
            </small>
        </div>
        <div>
            <a href="user_operations_tracker.php" class="btn btn-primary me-2">
                <i class="fas fa-user-cog me-1"></i> متتبع العمليات
            </a>
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Security Info -->
    <div class="alert alert-info">
        <h6><i class="fas fa-info-circle me-2"></i>معلومات الأمان</h6>
        <ul class="mb-0">
            <li><strong>مستوى الوصول:</strong> <?php echo $user_role === 'system_owner' ? 'مدير النظام (وصول كامل)' : 'مدير مركز (مركز محدد فقط)'; ?></li>
            <li><strong>البيانات المعروضة:</strong> <?php echo $user_center_id === 'all' ? 'جميع المراكز' : 'مركزك فقط'; ?></li>
            <li><strong>عدد الأنشطة:</strong> <?php echo $total_activities; ?> نشاط</li>
        </ul>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i>تصفية الأنشطة</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="days" class="form-label">الفترة الزمنية</label>
                    <select name="days" id="days" class="form-select">
                        <option value="7" <?php echo ($days_filter == 7) ? 'selected' : ''; ?>>آخر 7 أيام</option>
                        <option value="30" <?php echo ($days_filter == 30) ? 'selected' : ''; ?>>آخر 30 يوم</option>
                        <option value="90" <?php echo ($days_filter == 90) ? 'selected' : ''; ?>>آخر 3 أشهر</option>
                        <option value="365" <?php echo ($days_filter == 365) ? 'selected' : ''; ?>>آخر سنة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="activity" class="form-label">نوع النشاط</label>
                    <select name="activity" id="activity" class="form-select">
                        <option value="">جميع الأنشطة</option>
                        <option value="تسجيل مستخدم جديد" <?php echo ($activity_filter == 'تسجيل مستخدم جديد') ? 'selected' : ''; ?>>تسجيل مستخدم جديد</option>
                        <option value="تسجيل في الحلقات" <?php echo ($activity_filter == 'تسجيل في الحلقات') ? 'selected' : ''; ?>>تسجيل في الحلقات</option>
                        <option value="تسجيل الحضور" <?php echo ($activity_filter == 'تسجيل الحضور') ? 'selected' : ''; ?>>تسجيل الحضور</option>
                        <option value="تسجيل دخول/خروج" <?php echo ($activity_filter == 'تسجيل دخول/خروج') ? 'selected' : ''; ?>>تسجيل دخول/خروج</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="user" class="form-label">المستخدم</label>
                    <select name="user" id="user" class="form-select">
                        <option value="">جميع المستخدمين</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?php echo $user['user_id']; ?>" <?php echo ($user_filter == $user['user_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['full_name'] . ' (' . $user['username'] . ')'); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block w-100">
                        <i class="fas fa-search me-1"></i> تصفية
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Activities List -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list me-2"></i>سجل الأنشطة</h5>
            <small class="text-muted">عرض <?php echo count($activities); ?> من أصل <?php echo $total_activities; ?> نشاط</small>
        </div>
        <div class="card-body">
            <?php if (!empty($activities)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نوع النشاط</th>
                                <th>الوصف</th>
                                <th>المستخدم</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($activities as $activity): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($activity['activity_type']); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($activity['description']); ?></td>
                                    <td><?php echo htmlspecialchars($activity['user_name']); ?></td>
                                    <td>
                                        <small><?php echo $activity['timestamp']; ?></small>
                                        <?php if ($activity['ip_address']): ?>
                                            <br><small class="text-muted">IP: <?php echo htmlspecialchars($activity['ip_address']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $severity_colors = [
                                            'INFO' => 'success',
                                            'WARNING' => 'warning',
                                            'ERROR' => 'danger'
                                        ];
                                        $color = $severity_colors[$activity['severity']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $color; ?>"><?php echo $activity['severity']; ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="صفحات الأنشطة">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&days=<?php echo $days_filter; ?>&activity=<?php echo urlencode($activity_filter); ?>&user=<?php echo $user_filter; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أنشطة</h5>
                    <p class="text-muted">لم يتم العثور على أنشطة تطابق المعايير المحددة</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
