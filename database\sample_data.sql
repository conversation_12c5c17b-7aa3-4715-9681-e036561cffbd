-- Sample data for Quran Circle Management System
USE quran_circle_management;

-- Insert centers
INSERT INTO centers (center_name, address, contact_person_name, contact_email, contact_phone, is_active) VALUES
('مركز الفرقان', 'الرياض - حي النزهة', 'عبدالله محمد', '<EMAIL>', '0501234567', TRUE),
('مركز النور', 'جدة - حي الروضة', 'محمد أحمد', '<EMAIL>', '0551234567', TRUE),
('مركز الهدى', 'الدمام - حي الشاطئ', 'خالد عبدالرحمن', '<EMAIL>', '0561234567', TRUE);

-- Insert system owner
INSERT INTO users (username, password_hash, full_name, email, phone_number, role_id, is_active) VALUES
('admin', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'مدير النظام', '<EMAIL>', '0500000000', 
(SELECT role_id FROM roles WHERE role_name = 'system_owner'), TRUE);

-- Insert center admins
INSERT INTO users (username, password_hash, full_name, email, phone_number, role_id, center_id, is_active) VALUES
('admin_furqan', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'مدير مركز الفرقان', '<EMAIL>', '0501111111', 
(SELECT role_id FROM roles WHERE role_name = 'center_admin'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'), TRUE),

('admin_noor', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'مدير مركز النور', '<EMAIL>', '0552222222', 
(SELECT role_id FROM roles WHERE role_name = 'center_admin'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'), TRUE),

('admin_huda', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'مدير مركز الهدى', '<EMAIL>', '0563333333', 
(SELECT role_id FROM roles WHERE role_name = 'center_admin'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز الهدى'), TRUE);

-- Update centers with director_user_id
UPDATE centers SET director_user_id = (SELECT user_id FROM users WHERE username = 'admin_furqan') 
WHERE center_name = 'مركز الفرقان';

UPDATE centers SET director_user_id = (SELECT user_id FROM users WHERE username = 'admin_noor') 
WHERE center_name = 'مركز النور';

UPDATE centers SET director_user_id = (SELECT user_id FROM users WHERE username = 'admin_huda') 
WHERE center_name = 'مركز الهدى';

-- Insert teachers for Furqan center
INSERT INTO users (username, password_hash, full_name, email, phone_number, role_id, center_id, is_active) VALUES
('teacher1', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'أحمد محمد', '<EMAIL>', '0501234001', 
(SELECT role_id FROM roles WHERE role_name = 'teacher'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'), TRUE),

('teacher2', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'محمد علي', '<EMAIL>', '0501234002', 
(SELECT role_id FROM roles WHERE role_name = 'teacher'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'), TRUE);

-- Insert teachers for Noor center
INSERT INTO users (username, password_hash, full_name, email, phone_number, role_id, center_id, is_active) VALUES
('teacher3', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'عبدالرحمن خالد', '<EMAIL>', '0551234001', 
(SELECT role_id FROM roles WHERE role_name = 'teacher'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'), TRUE),

('teacher4', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'خالد سعد', '<EMAIL>', '0551234002', 
(SELECT role_id FROM roles WHERE role_name = 'teacher'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'), TRUE);

-- Insert parents
INSERT INTO users (username, password_hash, full_name, email, phone_number, role_id, is_active) VALUES
('parent1', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'عبدالله العمري', '<EMAIL>', '0501234101', 
(SELECT role_id FROM roles WHERE role_name = 'parent'), TRUE),

('parent2', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'فهد السعيد', '<EMAIL>', '0501234102', 
(SELECT role_id FROM roles WHERE role_name = 'parent'), TRUE),

('parent3', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'سعد الفهد', '<EMAIL>', '0551234101', 
(SELECT role_id FROM roles WHERE role_name = 'parent'), TRUE),

('parent4', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'محمد السالم', '<EMAIL>', '0551234102', 
(SELECT role_id FROM roles WHERE role_name = 'parent'), TRUE);

-- Insert students for Furqan center
INSERT INTO users (username, password_hash, full_name, email, phone_number, role_id, center_id, is_active) VALUES
('student1', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'عمر العمري', '<EMAIL>', '0501234201', 
(SELECT role_id FROM roles WHERE role_name = 'student'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'), TRUE),

('student2', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'سعود العمري', '<EMAIL>', '0501234202', 
(SELECT role_id FROM roles WHERE role_name = 'student'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'), TRUE),

('student3', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'خالد السعيد', '<EMAIL>', '0501234203', 
(SELECT role_id FROM roles WHERE role_name = 'student'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'), TRUE),

('student4', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'فيصل السعيد', '<EMAIL>', '0501234204', 
(SELECT role_id FROM roles WHERE role_name = 'student'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'), TRUE);

-- Insert students for Noor center
INSERT INTO users (username, password_hash, full_name, email, phone_number, role_id, center_id, is_active) VALUES
('student5', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'عبدالعزيز الفهد', '<EMAIL>', '0551234201', 
(SELECT role_id FROM roles WHERE role_name = 'student'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'), TRUE),

('student6', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'عبدالله الفهد', '<EMAIL>', '0551234202', 
(SELECT role_id FROM roles WHERE role_name = 'student'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'), TRUE),

('student7', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'يوسف السالم', '<EMAIL>', '0551234203', 
(SELECT role_id FROM roles WHERE role_name = 'student'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'), TRUE),

('student8', '$2y$10$8zUUpnrz4wdQCBqGVi9qAOCWKdJIFLFnkqEKUZXkzQCQQQQzLZHZa', 'إبراهيم السالم', '<EMAIL>', '0551234204', 
(SELECT role_id FROM roles WHERE role_name = 'student'), 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'), TRUE);

-- Insert circles for Furqan center
INSERT INTO circles (circle_name, description, center_id, teacher_user_id, level, schedule_details, max_students, start_date, is_active) VALUES
('حلقة الفرقان 1', 'حلقة تحفيظ للمبتدئين', 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'),
(SELECT user_id FROM users WHERE username = 'teacher1'),
'مبتدئ', 'الأحد والثلاثاء والخميس من الساعة 4 إلى 6 مساءً', 10, '2023-09-01', TRUE),

('حلقة الفرقان 2', 'حلقة تحفيظ للمتوسطين', 
(SELECT center_id FROM centers WHERE center_name = 'مركز الفرقان'),
(SELECT user_id FROM users WHERE username = 'teacher2'),
'متوسط', 'السبت والاثنين والأربعاء من الساعة 4 إلى 6 مساءً', 10, '2023-09-01', TRUE);

-- Insert circles for Noor center
INSERT INTO circles (circle_name, description, center_id, teacher_user_id, level, schedule_details, max_students, start_date, is_active) VALUES
('حلقة النور 1', 'حلقة تحفيظ للمبتدئين', 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'),
(SELECT user_id FROM users WHERE username = 'teacher3'),
'مبتدئ', 'الأحد والثلاثاء والخميس من الساعة 5 إلى 7 مساءً', 10, '2023-09-01', TRUE),

('حلقة النور 2', 'حلقة تحفيظ للمتوسطين', 
(SELECT center_id FROM centers WHERE center_name = 'مركز النور'),
(SELECT user_id FROM users WHERE username = 'teacher4'),
'متوسط', 'السبت والاثنين والأربعاء من الساعة 5 إلى 7 مساءً', 10, '2023-09-01', TRUE);

-- Enroll students in Furqan circles
INSERT INTO student_circle_enrollments (student_user_id, circle_id, parent_user_id, enrollment_date, status) VALUES
((SELECT user_id FROM users WHERE username = 'student1'), 
 (SELECT circle_id FROM circles WHERE circle_name = 'حلقة الفرقان 1'),
 (SELECT user_id FROM users WHERE username = 'parent1'),
 '2023-09-05', 'approved'),

((SELECT user_id FROM users WHERE username = 'student2'), 
 (SELECT circle_id FROM circles WHERE circle_name = 'حلقة الفرقان 1'),
 (SELECT user_id FROM users WHERE username = 'parent1'),
 '2023-09-05', 'approved'),

((SELECT user_id FROM users WHERE username = 'student3'), 
 (SELECT circle_id FROM circles WHERE circle_name = 'حلقة الفرقان 2'),
 (SELECT user_id FROM users WHERE username = 'parent2'),
 '2023-09-05', 'approved'),

((SELECT user_id FROM users WHERE username = 'student4'), 
 (SELECT circle_id FROM circles WHERE circle_name = 'حلقة الفرقان 2'),
 (SELECT user_id FROM users WHERE username = 'parent2'),
 '2023-09-05', 'approved');

-- Enroll students in Noor circles
INSERT INTO student_circle_enrollments (student_user_id, circle_id, parent_user_id, enrollment_date, status) VALUES
((SELECT user_id FROM users WHERE username = 'student5'), 
 (SELECT circle_id FROM circles WHERE circle_name = 'حلقة النور 1'),
 (SELECT user_id FROM users WHERE username = 'parent3'),
 '2023-09-05', 'approved'),

((SELECT user_id FROM users WHERE username = 'student6'), 
 (SELECT circle_id FROM circles WHERE circle_name = 'حلقة النور 1'),
 (SELECT user_id FROM users WHERE username = 'parent3'),
 '2023-09-05', 'approved'),

((SELECT user_id FROM users WHERE username = 'student7'), 
 (SELECT circle_id FROM circles WHERE circle_name = 'حلقة النور 2'),
 (SELECT user_id FROM users WHERE username = 'parent4'),
 '2023-09-05', 'approved'),

((SELECT user_id FROM users WHERE username = 'student8'), 
 (SELECT circle_id FROM circles WHERE circle_name = 'حلقة النور 2'),
 (SELECT user_id FROM users WHERE username = 'parent4'),
 '2023-09-05', 'approved');

-- Insert memorization schedules for students
INSERT INTO memorization_schedules (enrollment_id, target_surah_start, target_ayah_start, target_surah_end, target_ayah_end, type, assigned_date, due_date, is_completed)
SELECT sce.enrollment_id, 'البقرة', 1, 'البقرة', 5, 'memorization', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 3 DAY), FALSE
FROM student_circle_enrollments sce
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student1', 'student2', 'student5', 'student6');

INSERT INTO memorization_schedules (enrollment_id, target_surah_start, target_ayah_start, target_surah_end, target_ayah_end, type, assigned_date, due_date, is_completed)
SELECT sce.enrollment_id, 'آل عمران', 1, 'آل عمران', 10, 'memorization', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 3 DAY), FALSE
FROM student_circle_enrollments sce
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student3', 'student4', 'student7', 'student8');

-- Insert attendance records for today
INSERT INTO attendance_records (enrollment_id, session_date, status, recorded_by_user_id)
SELECT sce.enrollment_id, CURDATE(), 'present', c.teacher_user_id
FROM student_circle_enrollments sce
JOIN circles c ON sce.circle_id = c.circle_id
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student1', 'student3', 'student5', 'student7');

INSERT INTO attendance_records (enrollment_id, session_date, status, recorded_by_user_id)
SELECT sce.enrollment_id, CURDATE(), 'absent_excused', c.teacher_user_id
FROM student_circle_enrollments sce
JOIN circles c ON sce.circle_id = c.circle_id
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student2', 'student6');

INSERT INTO attendance_records (enrollment_id, session_date, status, recorded_by_user_id)
SELECT sce.enrollment_id, CURDATE(), 'late', c.teacher_user_id
FROM student_circle_enrollments sce
JOIN circles c ON sce.circle_id = c.circle_id
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student4', 'student8');

-- Insert attendance records for yesterday
INSERT INTO attendance_records (enrollment_id, session_date, status, recorded_by_user_id)
SELECT sce.enrollment_id, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'present', c.teacher_user_id
FROM student_circle_enrollments sce
JOIN circles c ON sce.circle_id = c.circle_id;

-- Insert some memorization progress
INSERT INTO memorization_progress (enrollment_id, surah_name, ayah_from, ayah_to, recitation_date, memorization_quality, tajweed_application, fluency, teacher_notes, recorded_by_user_id)
SELECT sce.enrollment_id, 'الفاتحة', 1, 7, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'excellent', 'very_good', 'excellent', 'حفظ ممتاز مع تجويد جيد جداً', c.teacher_user_id
FROM student_circle_enrollments sce
JOIN circles c ON sce.circle_id = c.circle_id
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student1', 'student5');

INSERT INTO memorization_progress (enrollment_id, surah_name, ayah_from, ayah_to, recitation_date, memorization_quality, tajweed_application, fluency, teacher_notes, recorded_by_user_id)
SELECT sce.enrollment_id, 'الفاتحة', 1, 7, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'good', 'good', 'good', 'حفظ جيد مع بعض الأخطاء البسيطة', c.teacher_user_id
FROM student_circle_enrollments sce
JOIN circles c ON sce.circle_id = c.circle_id
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student2', 'student6');

INSERT INTO memorization_progress (enrollment_id, surah_name, ayah_from, ayah_to, recitation_date, memorization_quality, tajweed_application, fluency, teacher_notes, recorded_by_user_id)
SELECT sce.enrollment_id, 'الفاتحة', 1, 7, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'very_good', 'excellent', 'very_good', 'حفظ جيد جداً مع تجويد ممتاز', c.teacher_user_id
FROM student_circle_enrollments sce
JOIN circles c ON sce.circle_id = c.circle_id
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student3', 'student7');

INSERT INTO memorization_progress (enrollment_id, surah_name, ayah_from, ayah_to, recitation_date, memorization_quality, tajweed_application, fluency, teacher_notes, recorded_by_user_id)
SELECT sce.enrollment_id, 'الفاتحة', 1, 7, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'fair', 'fair', 'fair', 'يحتاج إلى مزيد من المراجعة', c.teacher_user_id
FROM student_circle_enrollments sce
JOIN circles c ON sce.circle_id = c.circle_id
JOIN users u ON sce.student_user_id = u.user_id
WHERE u.username IN ('student4', 'student8');
