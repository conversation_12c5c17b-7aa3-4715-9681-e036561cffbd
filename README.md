# نظام إدارة حلقات تحفيظ القرآن الكريم

نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم، يتيح للمعلمين والطلاب وأولياء الأمور ومدراء المراكز متابعة التقدم وإدارة الحلقات بسهولة.

## المميزات

- **لوحات تحكم مخصصة** لكل من:
  - مدير النظام
  - مدير المركز
  - المعلم
  - الطالب
  - ولي الأمر
- **متابعة الحفظ والمراجعة** مع تقارير تفصيلية وإحصائيات
- **تسجيل الحضور والغياب** مع إشعارات تلقائية لأولياء الأمور
- **إدارة الواجبات** وتقييمها
- **نظام رسائل** للتواصل بين المستخدمين
- **تقارير وإحصائيات** شاملة

## المتطلبات

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache أو Nginx)

## التثبيت

1. قم بإنشاء قاعدة بيانات MySQL جديدة باسم `quran_circle_management`
2. قم باستيراد ملف `database/db_setup.sql` لإنشاء جداول قاعدة البيانات
3. قم باستيراد ملف `database/sample_data.sql` لإضافة بيانات تجريبية (اختياري)
4. قم بتعديل ملف `includes/config.php` لتحديد إعدادات الاتصال بقاعدة البيانات
5. انسخ جميع الملفات إلى مجلد الخادم الخاص بك
6. قم بإنشاء المجلدات التالية وتأكد من أنها قابلة للكتابة:
   - `uploads/profile_pictures`
   - `uploads/attachments`

## بيانات الدخول الافتراضية

### مدير النظام
- اسم المستخدم: `admin`
- كلمة المرور: `password`

### مدير المركز
- اسم المستخدم: `admin_furqan`
- كلمة المرور: `password`

### المعلم
- اسم المستخدم: `teacher1`
- كلمة المرور: `password`

### الطالب
- اسم المستخدم: `student1`
- كلمة المرور: `password`

### ولي الأمر
- اسم المستخدم: `parent1`
- كلمة المرور: `password`

## هيكل المشروع

```
├── auth/                  # ملفات المصادقة (تسجيل الدخول، التسجيل، إلخ)
├── css/                   # ملفات CSS
├── database/              # ملفات قاعدة البيانات
├── includes/              # ملفات PHP المشتركة
├── js/                    # ملفات JavaScript
├── pages/                 # صفحات التطبيق
├── uploads/               # مجلد التحميلات
│   ├── profile_pictures/  # صور الملفات الشخصية
│   └── attachments/       # المرفقات
├── index.php              # الصفحة الرئيسية
└── README.md              # ملف التوثيق
```

## الصفحات الرئيسية

- **index.php**: الصفحة الرئيسية
- **auth/login.php**: صفحة تسجيل الدخول
- **auth/register.php**: صفحة التسجيل
- **pages/system_owner_dashboard.php**: لوحة تحكم مدير النظام
- **pages/center_admin_dashboard.php**: لوحة تحكم مدير المركز
- **pages/teacher_dashboard.php**: لوحة تحكم المعلم
- **pages/student_dashboard.php**: لوحة تحكم الطالب
- **pages/parent_dashboard.php**: لوحة تحكم ولي الأمر

## الوظائف الرئيسية

### للمعلم
- تسجيل الحضور والغياب
- تسجيل تقدم الحفظ والمراجعة
- إضافة واجبات وتقييمها
- التواصل مع الطلاب وأولياء الأمور

### للطالب
- عرض تقدم الحفظ
- عرض الواجبات وتسليمها
- التواصل مع المعلم

### لولي الأمر
- متابعة تقدم الأبناء
- الاطلاع على سجل الحضور والغياب
- التواصل مع المعلمين

### لمدير المركز
- إدارة المعلمين والطلاب
- إنشاء وإدارة الحلقات
- عرض التقارير والإحصائيات

### لمدير النظام
- إدارة المراكز
- إدارة المستخدمين
- إعدادات النظام

## المساهمة

نرحب بمساهماتكم في تطوير هذا النظام. يرجى اتباع الخطوات التالية:

1. قم بعمل fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات
4. قم بعمل commit للتغييرات (`git commit -m 'Add some amazing feature'`)
5. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
6. قم بفتح طلب سحب (Pull Request)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الاتصال

إذا كان لديك أي أسئلة أو استفسارات، يرجى التواصل معنا عبر البريد الإلكتروني: <EMAIL>
