<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/activity_logger.php';

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect based on user role
    switch ($_SESSION['role_name']) {
        case 'system_owner':
            redirect('pages/system_owner_dashboard.php');
            break;
        case 'center_admin':
            redirect('pages/center_admin_dashboard.php');
            break;
        case 'teacher':
            redirect('pages/teacher_dashboard.php');
            break;
        case 'student':
            redirect('pages/student_dashboard.php');
            break;
        case 'parent':
            redirect('pages/parent_dashboard.php');
            break;
        default:
            redirect('index.php');
    }
}

$error = '';

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username']);
    $password = $_POST['password'];

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            // Prepare SQL statement
            $stmt = $pdo->prepare("SELECT u.user_id, u.username, u.password_hash, u.full_name, u.email, u.center_id, r.role_name
                                  FROM users u
                                  JOIN roles r ON u.role_id = r.role_id
                                  WHERE u.username = ? AND u.is_active = TRUE");
            $stmt->execute([$username]);

            if ($user = $stmt->fetch()) {
                // Verify password
                if (password_verify($password, $user['password_hash'])) {
                    // Set session variables
                    $_SESSION['user_id'] = $user['user_id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['role_name'] = $user['role_name'];
                    $_SESSION['center_id'] = $user['center_id'];

                    // Log successful login activity
                    log_login_activity($pdo, $user['user_id'], $user['username'], true);

                    // Redirect based on user role
                    switch ($user['role_name']) {
                        case 'system_owner':
                            redirect('pages/system_owner_dashboard.php');
                            break;
                        case 'center_admin':
                            redirect('pages/center_admin_dashboard.php');
                            break;
                        case 'teacher':
                            redirect('pages/teacher_dashboard.php');
                            break;
                        case 'student':
                            redirect('pages/student_dashboard.php');
                            break;
                        case 'parent':
                            redirect('pages/parent_dashboard.php');
                            break;
                        default:
                            redirect('index.php');
                    }
                } else {
                    // Log failed login attempt
                    log_login_activity($pdo, null, $username, false, 'كلمة مرور خاطئة');
                    $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                }
            } else {
                // Log failed login attempt
                log_login_activity($pdo, null, $username, false, 'اسم مستخدم غير موجود');
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء محاولة تسجيل الدخول. يرجى المحاولة مرة أخرى لاحقًا.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-light">
    <div class="container">
        <div class="auth-form">
            <h2 class="form-title">تسجيل الدخول</h2>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <button class="btn btn-outline-secondary toggle-password" type="button" toggle="#password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">تذكرني</label>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                </div>
            </form>

            <div class="form-footer">
                <p>نسيت كلمة المرور؟ <a href="forgot_password.php">استعادة كلمة المرور</a></p>
                <p>ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a></p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>
</body>
</html>
