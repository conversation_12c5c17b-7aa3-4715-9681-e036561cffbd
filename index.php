<?php
// Include common functions and definitions
require_once 'includes/common.php';

// Include announcement stats functions
require_once 'includes/announcement_stats.php';

// Get home image URL from settings
$home_image_url = 'assets/images/quran-study.jpg'; // Default value
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'home_image_url'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $home_image_url = $result['setting_value'];
    }
} catch (PDOException $e) {
    // Silently fail and use default image
    error_log('Error fetching home image URL: ' . $e->getMessage());
}

// Get featured announcements
$featured_announcements = [];
try {
    $stmt = $pdo->prepare("
        SELECT a.*, u.full_name AS sender_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        WHERE a.is_active = TRUE
        AND a.is_featured = TRUE
        AND (a.is_public = TRUE OR ?)
        AND CURRENT_DATE BETWEEN a.start_date AND a.end_date
        ORDER BY a.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([is_logged_in()]);
    $featured_announcements = $stmt->fetchAll();
} catch (PDOException $e) {
    // Silently fail
    error_log('Error fetching featured announcements: ' . $e->getMessage());
}

// Get public announcements
$public_announcements = [];
try {
    $stmt = $pdo->prepare("
        SELECT a.*, u.full_name AS sender_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        WHERE a.is_active = TRUE
        AND a.is_public = TRUE
        AND CURRENT_DATE BETWEEN a.start_date AND a.end_date
        ORDER BY a.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $public_announcements = $stmt->fetchAll();
} catch (PDOException $e) {
    // Silently fail
    error_log('Error fetching public announcements: ' . $e->getMessage());
}

// Get the latest public announcement for permanent display
$latest_public_announcement = null;
try {
    // Check if columns exist in the announcements table
    $media_type_exists = column_exists($pdo, 'announcements', 'media_type');
    $media_url_exists = column_exists($pdo, 'announcements', 'media_url');
    $background_color_exists = column_exists($pdo, 'announcements', 'background_color');
    $text_color_exists = column_exists($pdo, 'announcements', 'text_color');
    $image_url_exists = column_exists($pdo, 'announcements', 'image_url');

    $stmt = $pdo->prepare("
        SELECT a.announcement_id, a.title, a.content, a.sender_user_id, a.target_role, a.target_center_id,
               a.is_active, a.created_at, a.updated_at,
               " . ($media_type_exists ? "a.media_type" : "'none'") . " AS media_type,
               " . ($media_url_exists ? "a.media_url" : "NULL") . " AS media_url,
               " . ($image_url_exists ? "a.image_url" : "NULL") . " AS image_url,
               " . ($background_color_exists ? "a.background_color" : "'#ffffff'") . " AS background_color,
               " . ($text_color_exists ? "a.text_color" : "'#000000'") . " AS text_color,
               u.full_name AS sender_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        WHERE a.is_active = TRUE
        AND a.is_public = TRUE
        AND CURRENT_DATE BETWEEN a.start_date AND a.end_date
        ORDER BY a.created_at DESC
        LIMIT 1
    ");
    $stmt->execute();
    $latest_public_announcement = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Silently fail
    error_log('Error fetching latest public announcement: ' . $e->getMessage());
}

// Include header
include_once 'includes/header.php';
?>

<?php if (!is_logged_in() && !empty($latest_public_announcement)): ?>
<!-- Permanent Public Announcement for Visitors -->
<div class="container-fluid py-3 mb-4" style="background-color: <?php echo !empty($latest_public_announcement['background_color']) ? $latest_public_announcement['background_color'] : '#ffffff'; ?>; color: <?php echo !empty($latest_public_announcement['text_color']) ? $latest_public_announcement['text_color'] : '#000000'; ?>;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h3 class="mb-2"><?php echo $latest_public_announcement['title']; ?></h3>
                <div class="mb-2">
                    <?php
                    // Limit content to 150 characters
                    $content = strip_tags($latest_public_announcement['content']);
                    echo strlen($content) > 150 ? substr($content, 0, 150) . '...' : $content;
                    ?>
                </div>
                <div>
                    <a href="pages/public_announcements.php" class="btn btn-sm btn-primary">عرض جميع الإعلانات</a>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <?php if (!empty($latest_public_announcement['media_url'])): ?>
                    <?php if ($latest_public_announcement['media_type'] === 'image'): ?>
                        <?php echo get_safe_image_tag($latest_public_announcement['media_url'], 'صورة الإعلان', 'img-fluid rounded', 'max-height: 150px;'); ?>
                    <?php elseif ($latest_public_announcement['media_type'] === 'video'): ?>
                        <video controls class="img-fluid rounded" style="max-height: 150px;">
                            <source src="<?php echo get_safe_media_url($latest_public_announcement['media_url']); ?>" type="video/mp4">
                            متصفحك لا يدعم تشغيل الفيديو.
                        </video>
                    <?php elseif ($latest_public_announcement['media_type'] === 'audio'): ?>
                        <audio controls class="w-100">
                            <source src="<?php echo get_safe_media_url($latest_public_announcement['media_url']); ?>" type="audio/mpeg">
                            متصفحك لا يدعم تشغيل الصوت.
                        </audio>
                    <?php endif; ?>
                <?php elseif (!empty($latest_public_announcement['image_url'])): ?>
                    <?php echo get_safe_image_tag($latest_public_announcement['image_url'], $latest_public_announcement['title'], 'img-fluid rounded', 'max-height: 150px;'); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (!empty($featured_announcements)): ?>
<!-- Featured Announcements Carousel -->
<div id="featuredAnnouncementsCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
    <div class="carousel-indicators">
        <?php foreach ($featured_announcements as $index => $announcement): ?>
            <button type="button" data-bs-target="#featuredAnnouncementsCarousel" data-bs-slide-to="<?php echo $index; ?>"
                    <?php echo $index === 0 ? 'class="active" aria-current="true"' : ''; ?>
                    aria-label="Slide <?php echo $index + 1; ?>"></button>
        <?php endforeach; ?>
    </div>
    <div class="carousel-inner">
        <?php foreach ($featured_announcements as $index => $announcement): ?>
            <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>"
                 style="background-color: <?php echo !empty($announcement['background_color']) ? $announcement['background_color'] : '#f8f9fa'; ?>;
                        color: <?php echo !empty($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>;">
                <div class="container py-5">
                    <div class="row align-items-center">
                        <?php if (!empty($announcement['image_url'])): ?>
                            <div class="col-lg-6 mb-4 mb-lg-0">
                                <div class="text-center">
                                    <?php echo get_safe_image_tag($announcement['image_url'], $announcement['title'], 'img-fluid rounded shadow', 'max-height: 300px;'); ?>
                                </div>
                            </div>
                            <div class="col-lg-6">
                        <?php else: ?>
                            <div class="col-12 text-center">
                        <?php endif; ?>
                                <h2 class="display-5 fw-bold mb-3"><?php echo $announcement['title']; ?></h2>
                                <div class="mb-4"><?php echo $announcement['content']; ?></div>
                                <div class="d-flex justify-content-center">
                                    <?php if (is_logged_in()): ?>
                                        <a href="pages/announcements.php" class="btn btn-primary px-4">عرض جميع الإعلانات</a>
                                    <?php else: ?>
                                        <a href="pages/public_announcements.php" class="btn btn-primary px-4">عرض جميع الإعلانات</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <button class="carousel-control-prev" type="button" data-bs-target="#featuredAnnouncementsCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">السابق</span>
    </button>
    <button class="carousel-control-next" type="button" data-bs-target="#featuredAnnouncementsCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">التالي</span>
    </button>
</div>
<?php endif; ?>

<div class="container py-5">
    <div class="row align-items-center">
        <div class="col-lg-6">
            <h1 class="display-4 fw-bold mb-4">نظام إدارة حلقات تحفيظ القرآن الكريم</h1>
            <p class="lead mb-4">منصة متكاملة لإدارة حلقات تحفيظ القرآن الكريم بكفاءة عالية، تتيح للمعلمين والطلاب وأولياء الأمور ومدراء المراكز متابعة التقدم وإدارة الحلقات بسهولة.</p>
            <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                <?php if (!is_logged_in()): ?>
                    <a href="auth/login.php" class="btn btn-primary btn-lg px-4 me-md-2">تسجيل الدخول</a>
                    <a href="auth/register.php" class="btn btn-outline-primary btn-lg px-4">إنشاء حساب</a>
                <?php else: ?>
                    <?php
                    $dashboard_link = '';
                    switch ($_SESSION['role_name']) {
                        case 'system_owner':
                            $dashboard_link = 'pages/system_owner_dashboard.php';
                            break;
                        case 'center_admin':
                            $dashboard_link = 'pages/center_admin_dashboard.php';
                            break;
                        case 'teacher':
                            $dashboard_link = 'pages/teacher_dashboard.php';
                            break;
                        case 'student':
                            $dashboard_link = 'pages/student_dashboard.php';
                            break;
                        case 'parent':
                            $dashboard_link = 'pages/parent_dashboard.php';
                            break;
                    }
                    ?>
                    <a href="<?php echo $dashboard_link; ?>" class="btn btn-primary btn-lg px-4">الذهاب إلى لوحة التحكم</a>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-lg-6">
            <img src="<?php echo $home_image_url; ?>" class="img-fluid rounded shadow-lg" alt="حلقات تحفيظ القرآن">
        </div>
    </div>
</div>

<div class="bg-light py-5">
    <div class="container">
        <h2 class="text-center mb-5">مميزات النظام</h2>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-book-open fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">متابعة الحفظ والمراجعة</h5>
                        <p class="card-text">متابعة دقيقة لتقدم الطلاب في حفظ ومراجعة القرآن الكريم، مع تقارير تفصيلية وإحصائيات.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-user-check fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">تسجيل الحضور والغياب</h5>
                        <p class="card-text">نظام متكامل لتسجيل حضور وغياب الطلاب في الحلقات، مع إشعارات تلقائية لأولياء الأمور.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-comments fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">التواصل الفعال</h5>
                        <p class="card-text">نظام تواصل متكامل بين المعلمين والطلاب وأولياء الأمور، مع إمكانية إرسال الإشعارات والرسائل.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-chart-line fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">تقارير وإحصائيات</h5>
                        <p class="card-text">تقارير تفصيلية وإحصائيات دقيقة عن أداء الطلاب والحلقات والمراكز، تساعد في اتخاذ القرارات.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-tasks fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">إدارة الواجبات</h5>
                        <p class="card-text">نظام متكامل لإدارة الواجبات والمهام للطلاب، مع إمكانية المتابعة والتقييم.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">واجهة سهلة الاستخدام</h5>
                        <p class="card-text">واجهة مستخدم سهلة وبسيطة، تعمل على جميع الأجهزة بكفاءة عالية.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">كيف يعمل النظام؟</h2>
        <div class="row">
            <div class="col-md-3">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <h3 class="m-0">1</h3>
                    </div>
                    <h5>إنشاء حساب</h5>
                    <p>قم بإنشاء حساب جديد كمدير مركز أو معلم أو طالب أو ولي أمر.</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <h3 class="m-0">2</h3>
                    </div>
                    <h5>إعداد المركز والحلقات</h5>
                    <p>قم بإعداد مركز التحفيظ وإنشاء الحلقات وإضافة المعلمين والطلاب.</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <h3 class="m-0">3</h3>
                    </div>
                    <h5>متابعة التقدم</h5>
                    <p>متابعة تقدم الطلاب في الحفظ والمراجعة وتسجيل الحضور والغياب.</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <h3 class="m-0">4</h3>
                    </div>
                    <h5>التقارير والإحصائيات</h5>
                    <p>الحصول على تقارير تفصيلية وإحصائيات دقيقة عن أداء الطلاب والحلقات.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($public_announcements)): ?>
<div class="bg-white py-5">
    <div class="container">
        <h2 class="text-center mb-5">آخر الإعلانات</h2>
        <div class="row">
            <?php foreach ($public_announcements as $announcement): ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100 shadow-sm"
                         style="background-color: <?php echo !empty($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>;
                                color: <?php echo !empty($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>;">
                        <?php if (!empty($announcement['image_url'])): ?>
                            <?php echo get_safe_image_tag($announcement['image_url'], $announcement['title'], 'card-img-top', 'max-height: 200px; object-fit: cover;'); ?>
                        <?php endif; ?>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo $announcement['title']; ?></h5>
                            <div class="card-text mb-3">
                                <?php
                                // Limit content to 150 characters
                                $content = strip_tags($announcement['content']);
                                echo strlen($content) > 150 ? substr($content, 0, 150) . '...' : $content;
                                ?>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i> <?php echo $announcement['sender_name']; ?> |
                                    <i class="fas fa-calendar-alt me-1"></i> <?php echo date('Y-m-d', strtotime($announcement['created_at'])); ?>
                                </small>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-top-0">
                            <a href="pages/announcements.php" class="btn btn-sm btn-outline-primary">قراءة المزيد</a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <div class="text-center mt-4">
            <?php if (is_logged_in()): ?>
                <a href="pages/announcements.php" class="btn btn-primary">عرض جميع الإعلانات</a>
            <?php else: ?>
                <a href="pages/public_announcements.php" class="btn btn-primary">عرض جميع الإعلانات</a>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include public announcement modal
include_once 'includes/public_announcement_modal.php';

// Include footer
require_once 'includes/footer.php';
?>
