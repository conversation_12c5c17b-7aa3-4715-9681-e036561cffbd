<?php
/**
 * Announcement Modal Component
 *
 * This file contains the modal that displays unread announcements to users
 * when they log in or visit their dashboard.
 */

// Get unread announcements for the current user
$unread_announcements = [];

if (is_logged_in()) {
    $user_id = $_SESSION['user_id'];

    // Get user role from database instead of session
    try {
        $role_stmt = $pdo->prepare("
            SELECT r.role_name, u.center_id
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.user_id = ?
        ");
        $role_stmt->execute([$user_id]);
        $user_data = $role_stmt->fetch(PDO::FETCH_ASSOC);

        if ($user_data) {
            $role_name = $user_data['role_name'];
            $center_id = $user_data['center_id'];

            // Debug information
            error_log("User ID: $user_id, Role: $role_name, Center ID: " . ($center_id ?? 'NULL'));

            // Get unread announcements - simplified query to ensure it works
            $stmt = $pdo->prepare("
                SELECT a.announcement_id, a.title, a.content, a.created_at,
                       a.media_type, a.media_url, a.background_color, a.text_color,
                       u.full_name AS creator_name, a.target_role, a.target_center_id,
                       u2.center_id AS sender_center_id
                FROM announcements a
                JOIN users u ON a.sender_user_id = u.user_id
                LEFT JOIN users u2 ON a.sender_user_id = u2.user_id
                WHERE a.is_active = TRUE
                AND (
                    (a.target_role = 'all' AND (
                        -- Si el remitente es system_owner, mostrar a todos
                        EXISTS (SELECT 1 FROM users u3 JOIN roles r ON u3.role_id = r.role_id
                               WHERE u3.user_id = a.sender_user_id AND r.role_name = 'system_owner')
                        -- Si el remitente es center_admin, mostrar solo a usuarios del mismo centro
                        OR (EXISTS (SELECT 1 FROM users u3 JOIN roles r ON u3.role_id = r.role_id
                                  WHERE u3.user_id = a.sender_user_id AND r.role_name = 'center_admin'
                                  AND u3.center_id = ?))
                    ))
                    OR a.target_role = ?
                    OR (a.target_role = 'center_specific' AND a.target_center_id = ?)
                )
                AND NOT EXISTS (
                    SELECT 1 FROM announcement_reads ar
                    WHERE ar.announcement_id = a.announcement_id AND ar.user_id = ?
                )
                ORDER BY a.created_at DESC
            ");
            $stmt->execute([$center_id, $role_name, $center_id, $user_id]);
            $unread_announcements = $stmt->fetchAll();

            // Debug information
            error_log("Found " . count($unread_announcements) . " unread announcements");
            foreach ($unread_announcements as $idx => $ann) {
                error_log("Announcement $idx: ID={$ann['announcement_id']}, Title={$ann['title']}, Target={$ann['target_role']}");
            }
        } else {
            error_log("User data not found for user ID: $user_id");
        }
    } catch (PDOException $e) {
        // Log the error instead of silently failing
        error_log("Error retrieving announcements: " . $e->getMessage());
    }
}

// Only show the modal if there are unread announcements
if (!empty($unread_announcements)):
?>

<!-- Announcements Modal -->
<div class="modal fade" id="announcementsModal" tabindex="-1" aria-labelledby="announcementsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="announcementsModalLabel">
                    <i class="fas fa-bullhorn me-2"></i> إعلانات جديدة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (count($unread_announcements) > 1): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لديك <?php echo count($unread_announcements); ?> إعلانات جديدة
                    </div>

                    <div class="accordion" id="announcementsAccordion">
                        <?php foreach ($unread_announcements as $index => $announcement): ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading<?php echo $announcement['announcement_id']; ?>">
                                    <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $announcement['announcement_id']; ?>" aria-expanded="<?php echo $index === 0 ? 'true' : 'false'; ?>" aria-controls="collapse<?php echo $announcement['announcement_id']; ?>">
                                        <?php echo $announcement['title']; ?>
                                    </button>
                                </h2>
                                <div id="collapse<?php echo $announcement['announcement_id']; ?>" class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" aria-labelledby="heading<?php echo $announcement['announcement_id']; ?>" data-bs-parent="#announcementsAccordion">
                                    <div class="accordion-body">
                                        <?php if (!empty($announcement['media_url'])): ?>
                                            <div class="text-center mb-3">
                                                <?php if ($announcement['media_type'] === 'image'): ?>
                                                    <img src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 300px;">
                                                <?php elseif ($announcement['media_type'] === 'video'): ?>
                                                    <video controls class="img-fluid rounded" style="max-height: 300px;">
                                                        <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="video/mp4">
                                                        متصفحك لا يدعم تشغيل الفيديو.
                                                    </video>
                                                <?php elseif ($announcement['media_type'] === 'audio'): ?>
                                                    <audio controls class="w-100">
                                                        <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="audio/mpeg">
                                                        متصفحك لا يدعم تشغيل الصوت.
                                                    </audio>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        <div class="mb-3" style="background-color: <?php echo !empty($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>; color: <?php echo !empty($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>; padding: 15px; border-radius: 5px;">
                                            <?php echo $announcement['content']; ?>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i> <?php echo $announcement['creator_name']; ?> |
                                                <i class="fas fa-calendar-alt me-1"></i> <?php echo date('Y-m-d', strtotime($announcement['created_at'])); ?>
                                            </small>
                                            <button type="button" class="btn btn-sm btn-outline-primary mark-read-btn" data-announcement-id="<?php echo $announcement['announcement_id']; ?>">
                                                <i class="fas fa-check me-1"></i> تحديد كمقروء
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <?php $announcement = $unread_announcements[0]; ?>
                    <h4><?php echo $announcement['title']; ?></h4>
                    <?php if (!empty($announcement['media_url'])): ?>
                        <div class="text-center mb-3">
                            <?php if ($announcement['media_type'] === 'image'): ?>
                                <img src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 300px;">
                            <?php elseif ($announcement['media_type'] === 'video'): ?>
                                <video controls class="img-fluid rounded" style="max-height: 300px;">
                                    <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="video/mp4">
                                    متصفحك لا يدعم تشغيل الفيديو.
                                </video>
                            <?php elseif ($announcement['media_type'] === 'audio'): ?>
                                <audio controls class="w-100">
                                    <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="audio/mpeg">
                                    متصفحك لا يدعم تشغيل الصوت.
                                </audio>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    <div class="mb-3" style="background-color: <?php echo !empty($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>; color: <?php echo !empty($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>; padding: 15px; border-radius: 5px;">
                        <?php echo $announcement['content']; ?>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i> <?php echo $announcement['creator_name']; ?> |
                            <i class="fas fa-calendar-alt me-1"></i> <?php echo date('Y-m-d', strtotime($announcement['created_at'])); ?>
                        </small>
                        <button type="button" class="btn btn-sm btn-outline-primary mark-read-btn" data-announcement-id="<?php echo $announcement['announcement_id']; ?>">
                            <i class="fas fa-check me-1"></i> تحديد كمقروء
                        </button>
                    </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer">
                <a href="<?php echo get_root_url(); ?>pages/announcements.php" class="btn btn-outline-secondary">
                    <i class="fas fa-list me-1"></i> عرض كل الإعلانات
                </a>
                <button type="button" class="btn btn-primary" id="markAllReadBtn">
                    <i class="fas fa-check-double me-1"></i> تحديد الكل كمقروء
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Show the announcements modal when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Announcements modal script loaded');

        // Check if the modal element exists
        const modalElement = document.getElementById('announcementsModal');
        if (!modalElement) {
            console.error('Announcements modal element not found');
            return;
        }

        console.log('Announcements modal element found, initializing...');

        // Initialize the modal
        let announcementsModal;
        try {
            announcementsModal = new bootstrap.Modal(modalElement);
            console.log('Modal initialized successfully');
        } catch (error) {
            console.error('Error initializing modal:', error);
            return;
        }

        // Show the modal
        try {
            announcementsModal.show();
            console.log('Modal shown successfully');
        } catch (error) {
            console.error('Error showing modal:', error);
        }

        // Handle marking individual announcements as read
        document.querySelectorAll('.mark-read-btn').forEach(function(button) {
            button.addEventListener('click', function() {
                const announcementId = this.getAttribute('data-announcement-id');
                console.log('Marking announcement as read:', announcementId);
                markAnnouncementAsRead(announcementId, this);
            });
        });

        // Handle marking all announcements as read
        const markAllBtn = document.getElementById('markAllReadBtn');
        if (markAllBtn) {
            markAllBtn.addEventListener('click', function() {
                console.log('Marking all announcements as read');
                const buttons = document.querySelectorAll('.mark-read-btn');
                buttons.forEach(function(button) {
                    const announcementId = button.getAttribute('data-announcement-id');
                    markAnnouncementAsRead(announcementId, button);
                });

                // Close the modal after marking all as read
                setTimeout(function() {
                    announcementsModal.hide();
                }, 500);
            });
        }

        // Function to mark announcement as read
        function markAnnouncementAsRead(announcementId, button) {
            // Send AJAX request to mark as read
            console.log('Sending AJAX request to mark announcement as read:', announcementId);
            fetch('<?php echo get_root_url(); ?>pages/announcements.php?action=mark_read&id=' + announcementId + '&ajax=1', {
                method: 'GET'
            })
            .then(response => {
                console.log('Response received:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Data received:', data);
                if (data.success) {
                    // Update button appearance
                    button.innerHTML = '<i class="fas fa-check-circle me-1"></i> تم القراءة';
                    button.classList.remove('btn-outline-primary');
                    button.classList.add('btn-success');
                    button.disabled = true;

                    // If all announcements are marked as read, close the modal
                    const activeButtons = document.querySelectorAll('.mark-read-btn:not(:disabled)');
                    if (activeButtons.length === 0) {
                        setTimeout(function() {
                            announcementsModal.hide();
                        }, 500);
                    }
                } else {
                    console.error('Error in response:', data.error);
                    alert('حدث خطأ أثناء تحديد الإعلان كمقروء');
                }
            })
            .catch(error => {
                console.error('Error marking announcement as read:', error);
                alert('حدث خطأ أثناء تحديد الإعلان كمقروء');
            });
        }
    });
</script>

<?php endif; ?>
