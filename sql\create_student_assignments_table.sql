-- إن<PERSON>اء جدول واجبات الطلاب
CREATE TABLE IF NOT EXISTS student_assignments (
    student_assignment_id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    student_user_id INT NOT NULL,
    status ENUM('pending', 'submitted', 'late_submission', 'graded', 'not_submitted') NOT NULL DEFAULT 'pending',
    submission_date DATETIME NULL,
    submission_content TEXT NULL,
    grade DECIMAL(5,2) NULL,
    feedback TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assignment_id) REFERENCES assignments(assignment_id) ON DELETE CASCADE,
    FOREIGN KEY (student_user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- إضافة فهارس للتحسين الأداء
CREATE INDEX idx_student_assignments_assignment_id ON student_assignments(assignment_id);
CREATE INDEX idx_student_assignments_student_user_id ON student_assignments(student_user_id);
CREATE INDEX idx_student_assignments_status ON student_assignments(status);
