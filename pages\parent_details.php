<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Check if parent ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف ولي الأمر غير صحيح');
    redirect('pages/parents.php');
}

$parent_id = (int)$_GET['id'];
$error = '';
$success = '';

// Get parent information
try {
    $stmt = $pdo->prepare("
        SELECT u.*, c.center_name, r.role_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ? AND r.role_name = 'parent'
    ");
    $stmt->execute([$parent_id]);
    $parent = $stmt->fetch();
    
    if (!$parent) {
        set_flash_message('danger', 'ولي الأمر غير موجود');
        redirect('pages/parents.php');
    }
    
    // Check if current user has access to this parent
    if (has_role('center_admin') && $parent['center_id'] != $_SESSION['center_id']) {
        set_flash_message('danger', 'غير مصرح لك بالوصول إلى بيانات ولي الأمر هذا');
        redirect('pages/parents.php');
    }
    
    // Get children relations
    $stmt = $pdo->prepare("
        SELECT psr.relation_id, psr.student_user_id, psr.relation_type, psr.is_primary,
               s.full_name AS student_name, s.email AS student_email, s.profile_picture_url,
               c.circle_id, c.circle_name, c.level,
               t.full_name AS teacher_name
        FROM parent_student_relations psr
        JOIN users s ON psr.student_user_id = s.user_id
        LEFT JOIN student_circle_enrollments sce ON s.user_id = sce.student_user_id AND sce.status = 'approved'
        LEFT JOIN circles c ON sce.circle_id = c.circle_id
        LEFT JOIN users t ON c.teacher_user_id = t.user_id
        WHERE psr.parent_user_id = ?
        ORDER BY psr.is_primary DESC, s.full_name
    ");
    $stmt->execute([$parent_id]);
    $children = $stmt->fetchAll();
    
    // Check if address column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'address'
    ");
    $stmt->execute();
    $address_exists = (bool)$stmt->fetchColumn();
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات ولي الأمر: ' . $e->getMessage();
}

// Page variables
$page_title = 'تفاصيل ولي الأمر: ' . $parent['full_name'];
$active_page = 'parents';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'أولياء الأمور', 'url' => 'parents.php'],
    ['title' => $parent['full_name']]
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="fas fa-user-friends me-2"></i>
        <?php echo $parent['full_name']; ?>
    </h1>
    <div>
        <a href="edit_parent.php?id=<?php echo $parent_id; ?>" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i> تعديل
        </a>
        <a href="parents.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <!-- Parent Information -->
    <div class="col-md-4 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> معلومات ولي الأمر</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <?php if (!empty($parent['profile_picture_url'])): ?>
                        <img src="<?php echo get_root_url() . $parent['profile_picture_url']; ?>" alt="صورة ولي الأمر" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    <?php else: ?>
                        <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                            <i class="fas fa-user-tie fa-5x text-secondary"></i>
                        </div>
                    <?php endif; ?>
                    <h4><?php echo $parent['full_name']; ?></h4>
                    <p class="text-muted"><?php echo $parent['username']; ?></p>
                    <div class="badge bg-<?php echo $parent['is_active'] ? 'success' : 'danger'; ?> mb-3">
                        <?php echo $parent['is_active'] ? 'نشط' : 'غير نشط'; ?>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-envelope me-2"></i> البريد الإلكتروني</h6>
                    <p><?php echo $parent['email']; ?></p>
                </div>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-phone me-2"></i> رقم الهاتف</h6>
                    <p><?php echo $parent['phone_number'] ?: 'غير متوفر'; ?></p>
                </div>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-building me-2"></i> المركز</h6>
                    <p><?php echo $parent['center_name'] ?: 'غير منتسب لمركز'; ?></p>
                </div>
                
                <?php if ($address_exists && !empty($parent['address'])): ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-map-marker-alt me-2"></i> العنوان</h6>
                    <p><?php echo nl2br($parent['address']); ?></p>
                </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-calendar-alt me-2"></i> تاريخ التسجيل</h6>
                    <p><?php echo date('Y-m-d', strtotime($parent['created_at'])); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Children Information -->
    <div class="col-md-8 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0"><i class="fas fa-child me-2"></i> الأبناء المرتبطين</h5>
            </div>
            <div class="card-body">
                <?php if (empty($children)): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> لا يوجد أبناء مرتبطين بولي الأمر.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover datatable">
                            <thead>
                                <tr>
                                    <th>الطالب</th>
                                    <th>نوع العلاقة</th>
                                    <th>الحلقة</th>
                                    <th>المعلم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($children as $child): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if (!empty($child['profile_picture_url'])): ?>
                                                    <img src="<?php echo get_root_url() . $child['profile_picture_url']; ?>" alt="صورة الطالب" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                        <i class="fas fa-user text-secondary"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo $child['student_name']; ?></strong>
                                                    <?php if ($child['is_primary']): ?>
                                                        <span class="badge bg-primary ms-1">رئيسي</span>
                                                    <?php endif; ?>
                                                    <br>
                                                    <small class="text-muted"><?php echo $child['student_email']; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo $child['relation_type']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($child['circle_name']): ?>
                                                <div>
                                                    <strong><?php echo $child['circle_name']; ?></strong><br>
                                                    <small class="text-muted">المستوى: <?php echo $child['level']; ?></small>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">غير مسجل في حلقة</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo $child['teacher_name'] ?: '<span class="text-muted">-</span>'; ?>
                                        </td>
                                        <td>
                                            <a href="student_details.php?id=<?php echo $child['student_user_id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a href="edit_student.php?id=<?php echo $child['student_user_id']; ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
                
                <div class="mt-3">
                    <a href="edit_parent.php?id=<?php echo $parent_id; ?>" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة طالب
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer template
include_template('footer');
?>
