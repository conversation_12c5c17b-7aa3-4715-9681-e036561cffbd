<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('../auth/login.php');
}

// Get circle ID from URL
$circle_id = isset($_GET['circle_id']) ? (int)$_GET['circle_id'] : 0;

if ($circle_id <= 0) {
    set_flash_message('danger', 'معرف الحلقة غير صحيح');
    redirect('teacher_dashboard.php');
}

$error = '';
$success = '';
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];

// Get circle information and verify access
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name, c.description, c.level, c.teacher_user_id,
               cen.center_name, cen.center_id,
               t.full_name AS teacher_name
        FROM circles c
        JOIN centers cen ON c.center_id = cen.center_id
        JOIN users t ON c.teacher_user_id = t.user_id
        WHERE c.circle_id = ? AND c.is_active = TRUE
    ");
    $stmt->execute([$circle_id]);
    $circle = $stmt->fetch();

    if (!$circle) {
        set_flash_message('danger', 'الحلقة غير موجودة أو غير نشطة');
        redirect('teacher_dashboard.php');
    }

    // Check access permissions
    $has_access = false;
    if ($role_name === 'system_owner') {
        $has_access = true;
    } elseif ($role_name === 'center_admin') {
        $has_access = ($circle['center_id'] == $_SESSION['center_id']);
    } elseif ($role_name === 'teacher') {
        $has_access = ($circle['teacher_user_id'] == $user_id);
    } elseif ($role_name === 'student') {
        // Check if student is enrolled in this circle
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM student_circle_enrollments
            WHERE student_user_id = ? AND circle_id = ? AND status = 'approved'
        ");
        $stmt->execute([$user_id, $circle_id]);
        $has_access = (bool)$stmt->fetchColumn();
    } elseif ($role_name === 'parent') {
        // Check if parent has children in this circle
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM student_circle_enrollments sce
            WHERE sce.parent_user_id = ? AND sce.circle_id = ? AND sce.status = 'approved'
        ");
        $stmt->execute([$user_id, $circle_id]);
        $has_access = (bool)$stmt->fetchColumn();
    }

    if (!$has_access) {
        set_flash_message('danger', 'غير مصرح لك بالوصول إلى إعلانات هذه الحلقة');
        redirect('teacher_dashboard.php');
    }

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
}

// Get announcements for this circle
$announcements = [];
try {
    // Check if announcements table exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'announcements'
    ");
    $stmt->execute();
    $announcements_table_exists = (bool)$stmt->fetchColumn();

    if ($announcements_table_exists) {
        // Check if circle_id column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'announcements'
            AND column_name = 'circle_id'
        ");
        $stmt->execute();
        $circle_id_exists = (bool)$stmt->fetchColumn();

        // Check if priority_level column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.columns
            WHERE table_schema = DATABASE()
            AND table_name = 'announcements'
            AND column_name = 'priority_level'
        ");
        $stmt->execute();
        $priority_exists = (bool)$stmt->fetchColumn();

        $priority_field = $priority_exists ? "a.priority_level" : "'normal' AS priority_level";
        $circle_condition = $circle_id_exists ? "OR a.circle_id = ?" : "";

        $stmt = $pdo->prepare("
            SELECT a.announcement_id, a.title, a.content, a.created_at, a.updated_at,
                   a.target_role, a.target_center_id, a.is_active, $priority_field,
                   u.full_name AS creator_name,
                   (SELECT COUNT(*) FROM announcement_reads ar WHERE ar.announcement_id = a.announcement_id AND ar.user_id = ?) AS is_read,
                   (SELECT COUNT(*) FROM announcement_recipients ar WHERE ar.announcement_id = a.announcement_id AND ar.user_id = ? AND ar.is_read = TRUE) AS is_read_new
            FROM announcements a
            JOIN users u ON a.sender_user_id = u.user_id
            WHERE a.is_active = TRUE
            AND CURDATE() BETWEEN a.start_date AND a.end_date
            AND (
                (a.target_role = 'all' AND a.target_center_id = ?) OR
                (a.target_role = 'center_specific' AND a.target_center_id = ?) OR
                (a.target_role = ? AND a.target_center_id = ?)
                $circle_condition
            )
            ORDER BY " . ($priority_exists ? "FIELD(a.priority_level, 'urgent', 'high', 'normal')," : "") . " a.created_at DESC
        ");

        $params = [$user_id, $user_id, $circle['center_id'], $circle['center_id'], $role_name, $circle['center_id']];
        if ($circle_id_exists) {
            $params[] = $circle_id;
        }

        $stmt->execute($params);
        $announcements = $stmt->fetchAll();
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الإعلانات: ' . $e->getMessage();
}

// Process mark as read
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['mark_read'])) {
    $announcement_id = (int)$_POST['announcement_id'];

    try {
        // Check if announcement_reads table exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'announcement_reads'
        ");
        $stmt->execute();
        $reads_table_exists = (bool)$stmt->fetchColumn();

        // Check if announcement_recipients table exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            AND table_name = 'announcement_recipients'
        ");
        $stmt->execute();
        $recipients_table_exists = (bool)$stmt->fetchColumn();

        if ($recipients_table_exists) {
            // Update announcement_recipients table
            $stmt = $pdo->prepare("
                UPDATE announcement_recipients
                SET is_read = TRUE, read_at = NOW()
                WHERE announcement_id = ? AND user_id = ?
            ");
            $stmt->execute([$announcement_id, $user_id]);

            if ($stmt->rowCount() > 0) {
                $success = 'تم تمييز الإعلان كمقروء';
            }
        } elseif ($reads_table_exists) {
            // Fallback to old table
            $stmt = $pdo->prepare("
                SELECT COUNT(*)
                FROM announcement_reads
                WHERE announcement_id = ? AND user_id = ?
            ");
            $stmt->execute([$announcement_id, $user_id]);
            $already_read = (bool)$stmt->fetchColumn();

            if (!$already_read) {
                $stmt = $pdo->prepare("
                    INSERT INTO announcement_reads (announcement_id, user_id, read_at)
                    VALUES (?, ?, NOW())
                ");
                $stmt->execute([$announcement_id, $user_id]);
                $success = 'تم تمييز الإعلان كمقروء';
            }
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء تمييز الإعلان كمقروء: ' . $e->getMessage();
    }
}

// Page variables
$page_title = 'إعلانات الحلقة: ' . $circle['circle_name'];
$active_page = 'circles';

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">
            <i class="fas fa-bullhorn me-2"></i>
            إعلانات الحلقة
        </h1>
        <div>
            <?php if ($role_name === 'teacher' && $circle['teacher_user_id'] == $user_id): ?>
            <a href="<?php echo get_root_url(); ?>pages/create_circle_announcement.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> إعلان جديد
            </a>
            <?php endif; ?>
            <a href="<?php echo get_root_url(); ?>pages/teacher_dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة
            </a>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <!-- Circle Information -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-circle me-2"></i> معلومات الحلقة
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>اسم الحلقة:</strong> <?php echo htmlspecialchars($circle['circle_name']); ?></p>
                    <p><strong>المستوى:</strong> <?php echo htmlspecialchars($circle['level'] ?? 'غير محدد'); ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>المعلم:</strong> <?php echo htmlspecialchars($circle['teacher_name']); ?></p>
                    <p><strong>المركز:</strong> <?php echo htmlspecialchars($circle['center_name']); ?></p>
                </div>
            </div>
            <?php if (!empty($circle['description'])): ?>
            <div class="mt-2">
                <p><strong>الوصف:</strong> <?php echo nl2br(htmlspecialchars($circle['description'])); ?></p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Announcements -->
    <div class="card">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i> الإعلانات
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($announcements)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد إعلانات</h5>
                    <p class="text-muted">لم يتم نشر أي إعلانات لهذه الحلقة بعد.</p>
                    <?php if ($role_name === 'teacher' && $circle['teacher_user_id'] == $user_id): ?>
                    <a href="<?php echo get_root_url(); ?>pages/create_circle_announcement.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> إنشاء أول إعلان
                    </a>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($announcements as $announcement): ?>
                    <div class="col-md-6 mb-4">
                        <?php
                        $is_read = $announcement['is_read'] || $announcement['is_read_new'];
                        $priority = $announcement['priority_level'] ?? 'normal';
                        $priority_class = '';
                        $priority_badge = '';

                        switch ($priority) {
                            case 'urgent':
                                $priority_class = $is_read ? 'border-danger' : 'border-danger';
                                $priority_badge = '<span class="badge bg-danger me-1">عاجل</span>';
                                break;
                            case 'high':
                                $priority_class = $is_read ? 'border-warning' : 'border-warning';
                                $priority_badge = '<span class="badge bg-warning me-1">مهم</span>';
                                break;
                            default:
                                $priority_class = $is_read ? 'border-secondary' : 'border-primary';
                        }
                        ?>
                        <div class="card h-100 <?php echo $priority_class; ?>">
                            <div class="card-header <?php echo $is_read ? 'bg-light' : ($priority === 'urgent' ? 'bg-danger text-white' : ($priority === 'high' ? 'bg-warning' : 'bg-primary text-white')); ?>">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0">
                                        <?php if (!$is_read): ?>
                                        <i class="fas fa-star text-warning me-1"></i>
                                        <?php endif; ?>
                                        <?php echo htmlspecialchars($announcement['title']); ?>
                                    </h6>
                                    <div>
                                        <?php echo $priority_badge; ?>
                                        <?php if (!$is_read): ?>
                                        <span class="badge bg-success">جديد</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <?php
                                    $content = htmlspecialchars($announcement['content']);
                                    echo strlen($content) > 150 ? substr($content, 0, 150) . '...' : $content;
                                    ?>
                                </p>
                                <div class="text-muted small">
                                    <p class="mb-1">
                                        <i class="fas fa-user me-1"></i>
                                        بواسطة: <?php echo htmlspecialchars($announcement['creator_name']); ?>
                                    </p>
                                    <p class="mb-0">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo date('Y-m-d H:i', strtotime($announcement['created_at'])); ?>
                                    </p>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo get_root_url(); ?>pages/announcement_details.php?id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye me-1"></i> عرض التفاصيل
                                    </a>
                                    <?php if (!$is_read): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="announcement_id" value="<?php echo $announcement['announcement_id']; ?>">
                                        <button type="submit" name="mark_read" class="btn btn-sm btn-success">
                                            <i class="fas fa-check me-1"></i> تمييز كمقروء
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer template
include_template('footer');
?>
