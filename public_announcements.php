<?php
// Include common functions and definitions
require_once 'includes/common.php';

// Get public announcements
$public_announcements = [];
try {
    $stmt = $pdo->prepare("
        SELECT a.*, u.full_name AS sender_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        WHERE a.is_active = TRUE 
        AND a.is_public = TRUE
        AND CURRENT_DATE BETWEEN a.start_date AND a.end_date
        ORDER BY a.created_at DESC
    ");
    $stmt->execute();
    $public_announcements = $stmt->fetchAll();
} catch (PDOException $e) {
    // Silently fail
    error_log('Error fetching public announcements: ' . $e->getMessage());
}

// Include header
include_once 'includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4 text-center">الإعلانات العامة</h1>
            
            <?php if (empty($public_announcements)): ?>
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> لا توجد إعلانات متاحة حالياً.
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($public_announcements as $announcement): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm" 
                                 style="background-color: <?php echo !empty($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>; 
                                        color: <?php echo !empty($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>;">
                                <?php if (!empty($announcement['image_url'])): ?>
                                    <img src="<?php echo $announcement['image_url']; ?>" class="card-img-top" alt="<?php echo $announcement['title']; ?>" style="max-height: 200px; object-fit: cover;">
                                <?php endif; ?>
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo $announcement['title']; ?></h5>
                                    <div class="card-text mb-3">
                                        <?php echo $announcement['content']; ?>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i> <?php echo $announcement['sender_name']; ?> |
                                            <i class="fas fa-calendar-alt me-1"></i> <?php echo date('Y-m-d', strtotime($announcement['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <div class="text-center mt-4">
                <?php if (!is_logged_in()): ?>
                    <p class="mb-3">للاطلاع على المزيد من الإعلانات والميزات، يرجى تسجيل الدخول أو إنشاء حساب جديد.</p>
                    <a href="auth/login.php" class="btn btn-primary me-2">تسجيل الدخول</a>
                    <a href="auth/register.php" class="btn btn-outline-primary">إنشاء حساب</a>
                <?php else: ?>
                    <a href="pages/announcements.php" class="btn btn-primary">عرض جميع الإعلانات</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?>
