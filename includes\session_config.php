<?php
/**
 * Session configuration
 * This file should be included before session_start() is called
 */

// Session settings - must be set before session_start()
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

/**
 * Helper function to start a session with proper configuration
 */
function start_secure_session() {
    // Session settings are already set above
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}
?>
