<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('../auth/login.php');
}

// Check if assignment ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    set_flash_message('danger', 'معرف الواجب غير صالح');
    redirect('assignments.php');
}

$assignment_id = (int)$_GET['id'];
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$error = '';
$success = '';

// Get assignment information
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.circle_id, c.circle_name, c.teacher_user_id, c.center_id,
               u.full_name AS created_by_name
        FROM assignments a
        JOIN circles c ON a.circle_id = c.circle_id
        JOIN users u ON a.created_by_user_id = u.user_id
        WHERE a.assignment_id = ?
    ");
    $stmt->execute([$assignment_id]);
    $assignment = $stmt->fetch();

    if (!$assignment) {
        set_flash_message('danger', 'لم يتم العثور على الواجب');
        redirect('assignments.php');
    }

    // Check if user has access to this assignment
    $has_access = false;

    if ($role_name === 'system_owner') {
        $has_access = true;
    } elseif ($role_name === 'center_admin') {
        $has_access = ($assignment['center_id'] == $_SESSION['center_id']);
    } elseif ($role_name === 'teacher') {
        $has_access = ($assignment['teacher_user_id'] == $user_id);
    } elseif ($role_name === 'student') {
        $stmt = $pdo->prepare("
            SELECT sa.student_assignment_id
            FROM student_assignments sa
            WHERE sa.assignment_id = ? AND sa.student_user_id = ?
        ");
        $stmt->execute([$assignment_id, $user_id]);
        $has_access = ($stmt->rowCount() > 0);
    } elseif ($role_name === 'parent') {
        $stmt = $pdo->prepare("
            SELECT sa.student_assignment_id
            FROM student_assignments sa
            JOIN student_circle_enrollments sce ON sa.student_user_id = sce.student_user_id
            WHERE sa.assignment_id = ? AND sce.parent_user_id = ?
        ");
        $stmt->execute([$assignment_id, $user_id]);
        $has_access = ($stmt->rowCount() > 0);
    }

    if (!$has_access) {
        set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذا الواجب');
        redirect('assignments.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الواجب: ' . $e->getMessage();
}

// Get student assignments
try {
    $stmt = $pdo->prepare("
        SELECT sa.student_assignment_id, sa.status, sa.submission_date, sa.grade, sa.feedback,
               u.user_id, u.full_name, u.profile_picture_url
        FROM student_assignments sa
        JOIN users u ON sa.student_user_id = u.user_id
        WHERE sa.assignment_id = ?
        ORDER BY u.full_name
    ");
    $stmt->execute([$assignment_id]);
    $student_assignments = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات تسليمات الطلاب: ' . $e->getMessage();
}

// If user is a student, get their assignment
if ($role_name === 'student') {
    try {
        $stmt = $pdo->prepare("
            SELECT sa.*
            FROM student_assignments sa
            WHERE sa.assignment_id = ? AND sa.student_user_id = ?
        ");
        $stmt->execute([$assignment_id, $user_id]);
        $my_assignment = $stmt->fetch();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الواجب الخاص بك: ' . $e->getMessage();
    }
}

// If user is a parent, get their children's assignments
if ($role_name === 'parent') {
    try {
        $stmt = $pdo->prepare("
            SELECT sa.*, u.full_name AS student_name
            FROM student_assignments sa
            JOIN users u ON sa.student_user_id = u.user_id
            JOIN student_circle_enrollments sce ON sa.student_user_id = sce.student_user_id
            WHERE sa.assignment_id = ? AND sce.parent_user_id = ?
        ");
        $stmt->execute([$assignment_id, $user_id]);
        $children_assignments = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات واجبات أبنائك: ' . $e->getMessage();
    }
}

// Process submit assignment form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_assignment']) && $role_name === 'student') {
    $student_assignment_id = (int)$_POST['student_assignment_id'];

    try {
        // Verify student has access to this assignment
        $stmt = $pdo->prepare("
            SELECT sa.student_assignment_id
            FROM student_assignments sa
            WHERE sa.student_assignment_id = ? AND sa.student_user_id = ?
        ");
        $stmt->execute([$student_assignment_id, $user_id]);

        if ($stmt->rowCount() === 0) {
            $error = 'غير مصرح لك بتسليم هذا الواجب';
        } else {
            // Update assignment status
            $stmt = $pdo->prepare("
                UPDATE student_assignments
                SET status = 'submitted', submission_date = NOW()
                WHERE student_assignment_id = ?
            ");
            $stmt->execute([$student_assignment_id]);

            $success = 'تم تسليم الواجب بنجاح';

            // Refresh student assignment
            $stmt = $pdo->prepare("
                SELECT sa.*
                FROM student_assignments sa
                WHERE sa.assignment_id = ? AND sa.student_user_id = ?
            ");
            $stmt->execute([$assignment_id, $user_id]);
            $my_assignment = $stmt->fetch();
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء تسليم الواجب: ' . $e->getMessage();
    }
}

// Process grade assignment form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['grade_assignment']) && ($role_name === 'teacher' || $role_name === 'center_admin')) {
    $student_assignment_id = (int)$_POST['student_assignment_id'];
    $grade = sanitize_input($_POST['grade']);
    $feedback = sanitize_input($_POST['feedback']);

    try {
        // Verify teacher has access to this assignment
        if ($role_name === 'teacher') {
            $stmt = $pdo->prepare("
                SELECT sa.student_assignment_id
                FROM student_assignments sa
                JOIN assignments a ON sa.assignment_id = a.assignment_id
                JOIN circles c ON a.circle_id = c.circle_id
                WHERE sa.student_assignment_id = ? AND c.teacher_user_id = ?
            ");
            $stmt->execute([$student_assignment_id, $user_id]);
        } else { // center_admin
            $stmt = $pdo->prepare("
                SELECT sa.student_assignment_id
                FROM student_assignments sa
                JOIN assignments a ON sa.assignment_id = a.assignment_id
                JOIN circles c ON a.circle_id = c.circle_id
                WHERE sa.student_assignment_id = ? AND c.center_id = ?
            ");
            $stmt->execute([$student_assignment_id, $_SESSION['center_id']]);
        }

        if ($stmt->rowCount() === 0) {
            $error = 'غير مصرح لك بتقييم هذا الواجب';
        } else {
            // Update assignment grade and status
            $stmt = $pdo->prepare("
                UPDATE student_assignments
                SET status = 'graded', grade = ?, feedback = ?
                WHERE student_assignment_id = ?
            ");
            $stmt->execute([$grade, $feedback, $student_assignment_id]);

            $success = 'تم تقييم الواجب بنجاح';

            // Refresh student assignments
            $stmt = $pdo->prepare("
                SELECT sa.student_assignment_id, sa.status, sa.submission_date, sa.grade, sa.feedback,
                       u.user_id, u.full_name, u.profile_picture_url
                FROM student_assignments sa
                JOIN users u ON sa.student_user_id = u.user_id
                WHERE sa.assignment_id = ?
                ORDER BY u.full_name
            ");
            $stmt->execute([$assignment_id]);
            $student_assignments = $stmt->fetchAll();
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء تقييم الواجب: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الواجب - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <?php include_once '../includes/header.php'; ?>

    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>تفاصيل الواجب</h1>
            <a href="assignments.php<?php echo isset($_GET['circle_id']) ? '?circle_id=' . $_GET['circle_id'] : ''; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى الواجبات
            </a>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <?php if (isset($assignment)): ?>
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        <?php echo $assignment['title']; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <?php if (!empty($assignment['description'])): ?>
                                <div class="mb-3">
                                    <h5>الوصف:</h5>
                                    <p><?php echo nl2br($assignment['description']); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <h5>معلومات الواجب:</h5>
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <strong>الحلقة:</strong> <?php echo $assignment['circle_name']; ?>
                                    </li>
                                    <li class="list-group-item">
                                        <strong>تاريخ الاستحقاق:</strong>
                                        <?php
                                        $due_date = strtotime($assignment['due_date']);
                                        $now = time();
                                        $days_diff = round(($due_date - $now) / (60 * 60 * 24));

                                        echo date('Y-m-d', $due_date);

                                        if ($days_diff < 0) {
                                            echo ' <span class="badge bg-danger">انتهى</span>';
                                        } elseif ($days_diff == 0) {
                                            echo ' <span class="badge bg-warning">اليوم</span>';
                                        } else {
                                            echo ' <span class="badge bg-info">متبقي ' . $days_diff . ' يوم</span>';
                                        }
                                        ?>
                                    </li>
                                    <li class="list-group-item">
                                        <strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d', strtotime($assignment['created_at'])); ?>
                                    </li>
                                    <li class="list-group-item">
                                        <strong>بواسطة:</strong> <?php echo $assignment['created_by_name']; ?>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($role_name === 'student' && isset($my_assignment)): ?>
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user-graduate me-2"></i>
                            الواجب الخاص بك
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>الحالة:
                                    <?php
                                    $status = $my_assignment['status'];
                                    $badge_class = '';
                                    $status_text = '';

                                    switch ($status) {
                                        case 'pending':
                                            $badge_class = 'warning';
                                            $status_text = 'معلق';
                                            break;
                                        case 'submitted':
                                            $badge_class = 'info';
                                            $status_text = 'تم التسليم';
                                            break;
                                        case 'graded':
                                            $badge_class = 'success';
                                            $status_text = 'تم التقييم';
                                            break;
                                        default:
                                            $badge_class = 'secondary';
                                            $status_text = $status;
                                    }

                                    echo '<span class="badge bg-' . $badge_class . '">' . $status_text . '</span>';
                                    ?>
                                </h5>

                                <?php if ($status === 'submitted' || $status === 'graded'): ?>
                                    <p><strong>تاريخ التسليم:</strong> <?php echo date('Y-m-d H:i', strtotime($my_assignment['submission_date'])); ?></p>
                                <?php endif; ?>

                                <?php if ($status === 'graded'): ?>
                                    <p><strong>التقييم:</strong> <?php echo $my_assignment['grade']; ?></p>

                                    <?php if (!empty($my_assignment['feedback'])): ?>
                                        <div class="mt-3">
                                            <h6>ملاحظات المعلم:</h6>
                                            <div class="p-3 bg-light rounded">
                                                <?php echo nl2br($my_assignment['feedback']); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                            <?php if ($status === 'pending'): ?>
                                <div class="col-md-6">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        لم تقم بتسليم الواجب بعد. يرجى تسليم الواجب قبل تاريخ الاستحقاق.
                                    </div>

                                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?id=' . $assignment_id; ?>">
                                        <input type="hidden" name="student_assignment_id" value="<?php echo $my_assignment['student_assignment_id']; ?>">

                                        <div class="d-grid">
                                            <button type="submit" name="submit_assignment" class="btn btn-primary">
                                                <i class="fas fa-paper-plane me-1"></i> تسليم الواجب
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($role_name === 'parent' && isset($children_assignments)): ?>
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-child me-2"></i>
                            واجبات أبنائك
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($children_assignments)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> لا يوجد واجبات لأبنائك في هذه الحلقة.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الحالة</th>
                                            <th>تاريخ التسليم</th>
                                            <th>التقييم</th>
                                            <th>ملاحظات المعلم</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($children_assignments as $child_assignment): ?>
                                            <tr>
                                                <td><?php echo $child_assignment['student_name']; ?></td>
                                                <td>
                                                    <?php
                                                    $status = $child_assignment['status'];
                                                    $badge_class = '';
                                                    $status_text = '';

                                                    switch ($status) {
                                                        case 'pending':
                                                            $badge_class = 'warning';
                                                            $status_text = 'معلق';
                                                            break;
                                                        case 'submitted':
                                                            $badge_class = 'info';
                                                            $status_text = 'تم التسليم';
                                                            break;
                                                        case 'graded':
                                                            $badge_class = 'success';
                                                            $status_text = 'تم التقييم';
                                                            break;
                                                        default:
                                                            $badge_class = 'secondary';
                                                            $status_text = $status;
                                                    }

                                                    echo '<span class="badge bg-' . $badge_class . '">' . $status_text . '</span>';
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    if ($status === 'submitted' || $status === 'graded') {
                                                        echo date('Y-m-d H:i', strtotime($child_assignment['submission_date']));
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    if ($status === 'graded') {
                                                        echo $child_assignment['grade'];
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    if ($status === 'graded' && !empty($child_assignment['feedback'])) {
                                                        echo '<button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#feedbackModal' . $child_assignment['student_assignment_id'] . '">
                                                                <i class="fas fa-comment-dots me-1"></i> عرض الملاحظات
                                                              </button>';

                                                        echo '<div class="modal fade" id="feedbackModal' . $child_assignment['student_assignment_id'] . '" tabindex="-1" aria-labelledby="feedbackModalLabel' . $child_assignment['student_assignment_id'] . '" aria-hidden="true">
                                                                <div class="modal-dialog">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title" id="feedbackModalLabel' . $child_assignment['student_assignment_id'] . '">ملاحظات المعلم</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            <p>' . nl2br($child_assignment['feedback']) . '</p>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                              </div>';
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (($role_name === 'teacher' || $role_name === 'center_admin' || $role_name === 'system_owner') && isset($student_assignments)): ?>
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users me-2"></i>
                            تسليمات الطلاب
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($student_assignments)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> لا يوجد طلاب مسجلين في هذا الواجب.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الحالة</th>
                                            <th>تاريخ التسليم</th>
                                            <th>التقييم</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($student_assignments as $student_assignment): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo !empty($student_assignment['profile_picture_url']) ? '../' . $student_assignment['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>"
                                                             class="rounded-circle me-2" width="40" height="40" alt="صورة الطالب">
                                                        <div>
                                                            <?php echo $student_assignment['full_name']; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status = $student_assignment['status'];
                                                    $badge_class = '';
                                                    $status_text = '';

                                                    switch ($status) {
                                                        case 'pending':
                                                            $badge_class = 'warning';
                                                            $status_text = 'معلق';
                                                            break;
                                                        case 'submitted':
                                                            $badge_class = 'info';
                                                            $status_text = 'تم التسليم';
                                                            break;
                                                        case 'graded':
                                                            $badge_class = 'success';
                                                            $status_text = 'تم التقييم';
                                                            break;
                                                        default:
                                                            $badge_class = 'secondary';
                                                            $status_text = $status;
                                                    }

                                                    echo '<span class="badge bg-' . $badge_class . '">' . $status_text . '</span>';
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    if ($status === 'submitted' || $status === 'graded') {
                                                        echo date('Y-m-d H:i', strtotime($student_assignment['submission_date']));
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    if ($status === 'graded') {
                                                        echo $student_assignment['grade'];

                                                        if (!empty($student_assignment['feedback'])) {
                                                            echo ' <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#viewFeedbackModal' . $student_assignment['student_assignment_id'] . '">
                                                                    <i class="fas fa-comment-dots"></i>
                                                                  </button>';

                                                            echo '<div class="modal fade" id="viewFeedbackModal' . $student_assignment['student_assignment_id'] . '" tabindex="-1" aria-labelledby="viewFeedbackModalLabel' . $student_assignment['student_assignment_id'] . '" aria-hidden="true">
                                                                    <div class="modal-dialog">
                                                                        <div class="modal-content">
                                                                            <div class="modal-header">
                                                                                <h5 class="modal-title" id="viewFeedbackModalLabel' . $student_assignment['student_assignment_id'] . '">ملاحظات التقييم</h5>
                                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                            </div>
                                                                            <div class="modal-body">
                                                                                <p>' . nl2br($student_assignment['feedback']) . '</p>
                                                                            </div>
                                                                            <div class="modal-footer">
                                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                  </div>';
                                                        }
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($status === 'submitted' && ($role_name === 'teacher' || $role_name === 'center_admin')): ?>
                                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#gradeModal<?php echo $student_assignment['student_assignment_id']; ?>">
                                                            <i class="fas fa-star me-1"></i> تقييم
                                                        </button>

                                                        <!-- Grade Assignment Modal -->
                                                        <div class="modal fade" id="gradeModal<?php echo $student_assignment['student_assignment_id']; ?>" tabindex="-1" aria-labelledby="gradeModalLabel<?php echo $student_assignment['student_assignment_id']; ?>" aria-hidden="true">
                                                            <div class="modal-dialog">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title" id="gradeModalLabel<?php echo $student_assignment['student_assignment_id']; ?>">تقييم الواجب</h5>
                                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                    </div>
                                                                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?id=' . $assignment_id; ?>">
                                                                        <div class="modal-body">
                                                                            <input type="hidden" name="student_assignment_id" value="<?php echo $student_assignment['student_assignment_id']; ?>">

                                                                            <div class="mb-3">
                                                                                <label for="grade<?php echo $student_assignment['student_assignment_id']; ?>" class="form-label">التقييم <span class="text-danger">*</span></label>
                                                                                <select class="form-select" id="grade<?php echo $student_assignment['student_assignment_id']; ?>" name="grade" required>
                                                                                    <option value="">اختر التقييم</option>
                                                                                    <option value="ممتاز">ممتاز</option>
                                                                                    <option value="جيد جداً">جيد جداً</option>
                                                                                    <option value="جيد">جيد</option>
                                                                                    <option value="مقبول">مقبول</option>
                                                                                    <option value="ضعيف">ضعيف</option>
                                                                                </select>
                                                                            </div>

                                                                            <div class="mb-3">
                                                                                <label for="feedback<?php echo $student_assignment['student_assignment_id']; ?>" class="form-label">ملاحظات</label>
                                                                                <textarea class="form-control" id="feedback<?php echo $student_assignment['student_assignment_id']; ?>" name="feedback" rows="3"></textarea>
                                                                            </div>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                                            <button type="submit" name="grade_assignment" class="btn btn-primary">حفظ التقييم</button>
                                                                        </div>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php elseif ($status === 'graded' && ($role_name === 'teacher' || $role_name === 'center_admin')): ?>
                                                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#gradeModal<?php echo $student_assignment['student_assignment_id']; ?>">
                                                            <i class="fas fa-edit me-1"></i> تعديل التقييم
                                                        </button>

                                                        <!-- Edit Grade Modal -->
                                                        <div class="modal fade" id="gradeModal<?php echo $student_assignment['student_assignment_id']; ?>" tabindex="-1" aria-labelledby="gradeModalLabel<?php echo $student_assignment['student_assignment_id']; ?>" aria-hidden="true">
                                                            <div class="modal-dialog">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title" id="gradeModalLabel<?php echo $student_assignment['student_assignment_id']; ?>">تعديل التقييم</h5>
                                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                    </div>
                                                                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?id=' . $assignment_id; ?>">
                                                                        <div class="modal-body">
                                                                            <input type="hidden" name="student_assignment_id" value="<?php echo $student_assignment['student_assignment_id']; ?>">

                                                                            <div class="mb-3">
                                                                                <label for="grade<?php echo $student_assignment['student_assignment_id']; ?>" class="form-label">التقييم <span class="text-danger">*</span></label>
                                                                                <select class="form-select" id="grade<?php echo $student_assignment['student_assignment_id']; ?>" name="grade" required>
                                                                                    <option value="">اختر التقييم</option>
                                                                                    <option value="ممتاز" <?php echo $student_assignment['grade'] === 'ممتاز' ? 'selected' : ''; ?>>ممتاز</option>
                                                                                    <option value="جيد جداً" <?php echo $student_assignment['grade'] === 'جيد جداً' ? 'selected' : ''; ?>>جيد جداً</option>
                                                                                    <option value="جيد" <?php echo $student_assignment['grade'] === 'جيد' ? 'selected' : ''; ?>>جيد</option>
                                                                                    <option value="مقبول" <?php echo $student_assignment['grade'] === 'مقبول' ? 'selected' : ''; ?>>مقبول</option>
                                                                                    <option value="ضعيف" <?php echo $student_assignment['grade'] === 'ضعيف' ? 'selected' : ''; ?>>ضعيف</option>
                                                                                </select>
                                                                            </div>

                                                                            <div class="mb-3">
                                                                                <label for="feedback<?php echo $student_assignment['student_assignment_id']; ?>" class="form-label">ملاحظات</label>
                                                                                <textarea class="form-control" id="feedback<?php echo $student_assignment['student_assignment_id']; ?>" name="feedback" rows="3"><?php echo $student_assignment['feedback']; ?></textarea>
                                                                            </div>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                                            <button type="submit" name="grade_assignment" class="btn btn-primary">حفظ التقييم</button>
                                                                        </div>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>

                                                    <a href="student_details.php?id=<?php echo $student_assignment['user_id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-user me-1"></i> عرض الطالب
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <?php include_once '../includes/footer.php'; ?>
</body>
</html>
