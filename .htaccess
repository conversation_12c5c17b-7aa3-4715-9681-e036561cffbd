# Enable URL rewriting
RewriteEngine On

# Set the base directory
RewriteBase /qurann/

# Redirect URLs with /pages/pages/ to /pages/
RewriteRule ^pages/pages/(.*)$ pages/$1 [R=301,L]

# Redirect URLs with /pages/auth/ to /auth/
RewriteRule ^pages/auth/(.*)$ auth/$1 [R=301,L]

# If the request is for a real file or directory, skip the rewriting rules
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Redirect all requests to index.php
# RewriteRule ^(.*)$ index.php [QSA,L]

# Custom error pages
ErrorDocument 404 /qurann/error/404.php
ErrorDocument 500 /qurann/error/500.php

# PHP settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
</IfModule>

# Disable directory listing
Options -Indexes
