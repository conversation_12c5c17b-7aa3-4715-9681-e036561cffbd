<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin') || has_role('teacher'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Page variables
$page_title = 'إدارة الطلاب';
$active_page = 'students';
$success = '';
$error = '';

// Get user role and ID
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_GET['center_id']) ? (int)$_GET['center_id'] : null);
$circle_id = isset($_GET['circle_id']) ? (int)$_GET['circle_id'] : null;

// Handle student actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $student_id = (int)$_GET['id'];

    if ($action === 'delete') {
        try {
            // Check if student has enrollments
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM student_circle_enrollments WHERE student_user_id = ?");
            $stmt->execute([$student_id]);
            $enrollment_count = $stmt->fetchColumn();

            if ($enrollment_count > 0) {
                $error = 'لا يمكن حذف الطالب لأنه مسجل في حلقات. قم بإلغاء تسجيله من الحلقات أولاً.';
            } else {
                // Delete student
                $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ? AND role_id = (SELECT role_id FROM roles WHERE role_name = 'student')");
                $stmt->execute([$student_id]);
                $success = 'تم حذف الطالب بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء حذف الطالب: ' . $e->getMessage();
        }
    } elseif ($action === 'toggle') {
        try {
            // Toggle student active status
            $stmt = $pdo->prepare("UPDATE users SET is_active = NOT is_active WHERE user_id = ? AND role_id = (SELECT role_id FROM roles WHERE role_name = 'student')");
            $stmt->execute([$student_id]);
            $success = 'تم تغيير حالة الطالب بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تغيير حالة الطالب: ' . $e->getMessage();
        }
    }
}

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Get circles for dropdown
$circles = [];
try {
    if (has_role('teacher')) {
        $stmt = $pdo->prepare("
            SELECT circle_id, circle_name
            FROM circles
            WHERE teacher_user_id = ? AND is_active = TRUE
            ORDER BY circle_name
        ");
        $stmt->execute([$user_id]);
    } elseif (has_role('center_admin')) {
        $stmt = $pdo->prepare("
            SELECT circle_id, circle_name
            FROM circles
            WHERE center_id = ? AND is_active = TRUE
            ORDER BY circle_name
        ");
        $stmt->execute([$_SESSION['center_id']]);
    } elseif (has_role('system_owner') && $center_id) {
        $stmt = $pdo->prepare("
            SELECT circle_id, circle_name
            FROM circles
            WHERE center_id = ? AND is_active = TRUE
            ORDER BY circle_name
        ");
        $stmt->execute([$center_id]);
    } else {
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name, ce.center_name
            FROM circles c
            JOIN centers ce ON c.center_id = ce.center_id
            WHERE c.is_active = TRUE
            ORDER BY ce.center_name, c.circle_name
        ");
        $stmt->execute();
    }
    $circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Check if birth_date column exists
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'birth_date'
    ");
    $stmt->execute();
    $birth_date_exists = (bool)$stmt->fetchColumn();

    // Check if gender column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'gender'
    ");
    $stmt->execute();
    $gender_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من أعمدة الجدول: ' . $e->getMessage();
}

// Get students based on role and filters
try {
    $query = "
        SELECT u.user_id, u.full_name, u.username, u.email, u.phone_number,
               " . ($birth_date_exists ? "u.birth_date, " : "NULL as birth_date, ") .
               ($gender_exists ? "u.gender, " : "NULL as gender, ") . "
               u.is_active, u.created_at
    ";

    $params = [];

    if (has_role('teacher')) {
        $query .= "
            FROM users u
            JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id
            JOIN circles c ON sce.circle_id = c.circle_id
            WHERE u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
            AND c.teacher_user_id = ?
        ";
        $params[] = $user_id;

        if ($circle_id) {
            $query .= " AND sce.circle_id = ?";
            $params[] = $circle_id;
        }
    } elseif (has_role('center_admin')) {
        $query .= "
            FROM users u
            WHERE u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
            AND u.center_id = ?
        ";
        $params[] = $_SESSION['center_id'];

        if ($circle_id) {
            $query .= " AND u.user_id IN (
                SELECT sce.student_user_id
                FROM student_circle_enrollments sce
                WHERE sce.circle_id = ?
            )";
            $params[] = $circle_id;
        }
    } elseif (has_role('system_owner')) {
        $query .= "
            FROM users u
            WHERE u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
        ";

        if ($center_id) {
            $query .= " AND u.center_id = ?";
            $params[] = $center_id;

            if ($circle_id) {
                $query .= " AND u.user_id IN (
                    SELECT sce.student_user_id
                    FROM student_circle_enrollments sce
                    WHERE sce.circle_id = ?
                )";
                $params[] = $circle_id;
            }
        }
    }

    $query .= " ORDER BY u.full_name";

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $students = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب: ' . $e->getMessage();
}

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true
]);

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : (has_role('center_admin') ? 'center_admin_dashboard.php' : 'teacher_dashboard.php')],
    ['title' => 'الطلاب']
];

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-user-graduate',
    'action_button' => has_role('system_owner') || has_role('center_admin') ? [
        'url' => 'add_student.php',
        'text' => 'إضافة طالب جديد',
        'icon' => 'fas fa-plus-circle'
    ] : null
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i> تصفية الطلاب</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <?php if (has_role('system_owner')): ?>
                <div class="col-md-4">
                    <label for="center_id" class="form-label">المركز</label>
                    <select class="form-select" id="center_id" name="center_id">
                        <option value="">جميع المراكز</option>
                        <?php foreach ($centers as $center): ?>
                            <option value="<?php echo $center['center_id']; ?>" <?php echo $center_id == $center['center_id'] ? 'selected' : ''; ?>>
                                <?php echo $center['center_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            <?php endif; ?>

            <div class="col-md-4">
                <label for="circle_id" class="form-label">الحلقة</label>
                <select class="form-select" id="circle_id" name="circle_id">
                    <option value="">جميع الحلقات</option>
                    <?php foreach ($circles as $circle): ?>
                        <option value="<?php echo $circle['circle_id']; ?>" <?php echo $circle_id == $circle['circle_id'] ? 'selected' : ''; ?>>
                            <?php echo $circle['circle_name']; ?>
                            <?php if (has_role('system_owner') && !$center_id && isset($circle['center_name'])): ?>
                                (<?php echo $circle['center_name']; ?>)
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-1"></i> تصفية
                </button>
                <a href="students.php" class="btn btn-secondary">
                    <i class="fas fa-redo me-1"></i> إعادة ضبط
                </a>
            </div>
        </form>
    </div>
</div>

<?php if (empty($students)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> لا يوجد طلاب مطابقين لمعايير البحث.
    </div>
<?php else: ?>
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table id="studentsTable" class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>معلومات الاتصال</th>
                            <th>الجنس</th>
                            <th>تاريخ الميلاد</th>
                            <th>تاريخ التسجيل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($students as $student): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $student['full_name']; ?></strong><br>
                                    <small class="text-muted">@<?php echo $student['username']; ?></small>
                                </td>
                                <td>
                                    <i class="fas fa-envelope me-1"></i> <?php echo $student['email']; ?><br>
                                    <?php if (!empty($student['phone_number'])): ?>
                                        <i class="fas fa-phone me-1"></i> <?php echo $student['phone_number']; ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $student['gender'] ?? 'غير محدد'; ?></td>
                                <td><?php echo $student['birth_date'] ? date('Y-m-d', strtotime($student['birth_date'])) : 'غير محدد'; ?></td>
                                <td><?php echo date('Y-m-d', strtotime($student['created_at'])); ?></td>
                                <td>
                                    <?php if ($student['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="student_details.php?id=<?php echo $student['user_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (has_role('system_owner') || has_role('center_admin')): ?>
                                            <a href="edit_student.php?id=<?php echo $student['user_id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="students.php?action=toggle&id=<?php echo $student['user_id']; ?><?php echo $center_id ? '&center_id=' . $center_id : ''; ?><?php echo $circle_id ? '&circle_id=' . $circle_id : ''; ?>" class="btn btn-sm btn-secondary" title="<?php echo $student['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                                <i class="fas <?php echo $student['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                            </a>
                                            <a href="students.php?action=delete&id=<?php echo $student['user_id']; ?><?php echo $center_id ? '&center_id=' . $center_id : ''; ?><?php echo $circle_id ? '&circle_id=' . $circle_id : ''; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا الطالب؟')">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (has_role('system_owner')): ?>
    // Update circles dropdown when center changes
    document.getElementById('center_id').addEventListener('change', function() {
        const form = this.form;
        form.submit();
    });
    <?php endif; ?>
});
</script>

<?php
// Include footer template
include_template('footer');
?>
