<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';
$center_id = has_role('center_admin') ? $_SESSION['center_id'] : (isset($_GET['center_id']) ? (int)$_GET['center_id'] : null);

// Handle teacher actions
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $teacher_id = (int)$_GET['id'];
    
    if ($action === 'delete') {
        try {
            // Check if teacher has circles
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM circles WHERE teacher_user_id = ?");
            $stmt->execute([$teacher_id]);
            $circle_count = $stmt->fetchColumn();
            
            if ($circle_count > 0) {
                $error = 'لا يمكن حذف المعلم لأنه مسؤول عن حلقات. قم بتعيين معلم آخر للحلقات أولاً.';
            } else {
                // Delete teacher
                $stmt = $pdo->prepare("DELETE FROM users WHERE user_id = ? AND role_id = (SELECT role_id FROM roles WHERE role_name = 'teacher')");
                $stmt->execute([$teacher_id]);
                $success = 'تم حذف المعلم بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء حذف المعلم: ' . $e->getMessage();
        }
    } elseif ($action === 'toggle') {
        try {
            // Toggle teacher active status
            $stmt = $pdo->prepare("UPDATE users SET is_active = NOT is_active WHERE user_id = ? AND role_id = (SELECT role_id FROM roles WHERE role_name = 'teacher')");
            $stmt->execute([$teacher_id]);
            $success = 'تم تغيير حالة المعلم بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء تغيير حالة المعلم: ' . $e->getMessage();
        }
    }
}

// Get centers for dropdown if system owner
$centers = [];
if (has_role('system_owner')) {
    try {
        $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
        $stmt->execute();
        $centers = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Get teachers with statistics
try {
    $query = "
        SELECT u.*, c.center_name,
               (SELECT COUNT(*) FROM circles WHERE teacher_user_id = u.user_id) AS circle_count,
               (SELECT COUNT(DISTINCT sce.student_user_id) 
                FROM student_circle_enrollments sce 
                JOIN circles ci ON sce.circle_id = ci.circle_id 
                WHERE ci.teacher_user_id = u.user_id) AS student_count
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE r.role_name = 'teacher'
    ";
    
    if ($center_id) {
        $query .= " AND u.center_id = ?";
        $stmt = $pdo->prepare($query);
        $stmt->execute([$center_id]);
    } else {
        $stmt = $pdo->prepare($query);
        $stmt->execute();
    }
    
    $teachers = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المعلمين: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المعلمين - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if (has_role('system_owner')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="system_owner_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="centers.php">المراكز</a>
                            </li>
                        <?php elseif (has_role('center_admin')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="center_admin_dashboard.php">لوحة التحكم</a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link active" href="teachers.php">المعلمين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="circles.php">الحلقات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">الطلاب</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <main class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-chalkboard-teacher me-2"></i> إدارة المعلمين</h1>
            <a href="add_teacher.php<?php echo $center_id ? '?center_id=' . $center_id : ''; ?>" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة معلم جديد
            </a>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (has_role('system_owner') && !empty($centers)): ?>
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">تصفية حسب المركز</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <select class="form-select" id="center_id" name="center_id">
                                <option value="">جميع المراكز</option>
                                <?php foreach ($centers as $center): ?>
                                    <option value="<?php echo $center['center_id']; ?>" <?php echo $center_id == $center['center_id'] ? 'selected' : ''; ?>>
                                        <?php echo $center['center_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i> تصفية
                            </button>
                            <a href="teachers.php" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i> إعادة ضبط
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if (empty($teachers)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا يوجد معلمين مسجلين حالياً. قم بإضافة معلم جديد.
            </div>
        <?php else: ?>
            <div class="card shadow">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="teachersTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>المعلم</th>
                                    <th>معلومات الاتصال</th>
                                    <?php if (has_role('system_owner')): ?>
                                        <th>المركز</th>
                                    <?php endif; ?>
                                    <th>الإحصائيات</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($teachers as $teacher): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $teacher['full_name']; ?></strong><br>
                                            <small class="text-muted">@<?php echo $teacher['username']; ?></small>
                                        </td>
                                        <td>
                                            <i class="fas fa-envelope me-1"></i> <?php echo $teacher['email']; ?><br>
                                            <?php if (!empty($teacher['phone_number'])): ?>
                                                <i class="fas fa-phone me-1"></i> <?php echo $teacher['phone_number']; ?>
                                            <?php endif; ?>
                                        </td>
                                        <?php if (has_role('system_owner')): ?>
                                            <td><?php echo $teacher['center_name']; ?></td>
                                        <?php endif; ?>
                                        <td>
                                            <span class="badge bg-info me-1" title="الحلقات">
                                                <i class="fas fa-circle me-1"></i> <?php echo $teacher['circle_count']; ?>
                                            </span>
                                            <span class="badge bg-primary" title="الطلاب">
                                                <i class="fas fa-user-graduate me-1"></i> <?php echo $teacher['student_count']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($teacher['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="teacher_details.php?id=<?php echo $teacher['user_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_teacher.php?id=<?php echo $teacher['user_id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="teachers.php?action=toggle&id=<?php echo $teacher['user_id']; ?><?php echo $center_id ? '&center_id=' . $center_id : ''; ?>" class="btn btn-sm btn-secondary" title="<?php echo $teacher['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                                    <i class="fas <?php echo $teacher['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                                </a>
                                                <a href="teachers.php?action=delete&id=<?php echo $teacher['user_id']; ?><?php echo $center_id ? '&center_id=' . $center_id : ''; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المعلم؟')">
                                                    <i class="fas fa-trash-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </main>
    
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#teachersTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                },
                "order": [[ 0, "asc" ]]
            });
        });
    </script>
</body>
</html>
