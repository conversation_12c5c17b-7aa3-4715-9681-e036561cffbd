<?php
/**
 * Helper functions for system settings
 */

/**
 * Create system_settings table if it doesn't exist
 *
 * @param PDO $pdo Database connection
 * @return bool True if successful, false otherwise
 */
function create_settings_table($pdo) {
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS system_settings (
                setting_key VARCHAR(100) PRIMARY KEY,
                setting_value TEXT,
                setting_description TEXT,
                setting_group VARCHAR(50),
                is_public BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        return true;
    } catch (PDOException $e) {
        error_log('Error creating system_settings table: ' . $e->getMessage());
        return false;
    }
}

/**
 * Initialize default system settings
 *
 * @param PDO $pdo Database connection
 * @return bool True if successful, false otherwise
 */
function initialize_default_settings($pdo) {
    try {
        // Default settings
        $default_settings = [
            // General settings
            ['site_name', 'نظام إدارة حلقات تحفيظ القرآن', 'اسم الموقع', 'general', true],
            ['site_description', 'نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم', 'وصف الموقع', 'general', true],
            ['admin_email', '<EMAIL>', 'البريد الإلكتروني للمسؤول', 'general', false],
            ['contact_phone', '+966 12 345 6789', 'رقم الهاتف للتواصل', 'general', true],
            ['contact_email', '<EMAIL>', 'البريد الإلكتروني للتواصل', 'general', true],
            ['contact_address', 'المملكة العربية السعودية', 'عنوان التواصل', 'general', true],

            // Registration settings
            ['allow_registration', '1', 'السماح بالتسجيل في الموقع', 'registration', true],
            ['require_email_verification', '1', 'طلب تأكيد البريد الإلكتروني', 'registration', false],
            ['default_role', 'student', 'الدور الافتراضي للمستخدمين الجدد', 'registration', false],
            ['allow_public_registration', '1', 'السماح بالتسجيل العام', 'registration', true],
            ['registration_approval', '0', 'تفعيل موافقة المشرف على التسجيل', 'registration', false],

            // Memorization settings
            ['default_memorization_levels', 'مبتدئ,متوسط,متقدم', 'مستويات الحفظ الافتراضية', 'memorization', false],
            ['quality_ratings', 'ممتاز,جيد جداً,جيد,مقبول,ضعيف', 'تقييمات جودة الحفظ', 'memorization', false],
            ['tajweed_ratings', 'ممتاز,جيد جداً,جيد,مقبول,ضعيف', 'تقييمات التجويد', 'memorization', false],
            ['fluency_ratings', 'ممتاز,جيد جداً,جيد,مقبول,ضعيف', 'تقييمات الطلاقة', 'memorization', false],
            ['default_assignment_days', '7', 'عدد أيام الواجب الافتراضي', 'memorization', false],

            // Appearance settings
            ['primary_color', '#0d6efd', 'اللون الرئيسي', 'appearance', true],
            ['secondary_color', '#6c757d', 'اللون الثانوي', 'appearance', true],
            ['success_color', '#198754', 'لون النجاح', 'appearance', true],
            ['danger_color', '#dc3545', 'لون الخطر', 'appearance', true],
            ['warning_color', '#ffc107', 'لون التحذير', 'appearance', true],
            ['info_color', '#0dcaf0', 'لون المعلومات', 'appearance', true],
            ['logo_url', 'assets/images/logo.png', 'رابط الشعار', 'appearance', true],
            ['favicon_url', 'assets/images/favicon.ico', 'رابط أيقونة الموقع', 'appearance', true],
            ['footer_text', 'جميع الحقوق محفوظة © نظام إدارة حلقات تحفيظ القرآن', 'نص التذييل', 'appearance', true],
            ['home_image_url', 'assets/images/quran-study.jpg', 'رابط الصورة الرئيسية', 'appearance', true],
            ['enable_dark_mode', '0', 'تفعيل الوضع الداكن', 'appearance', true],
            ['rtl_layout', '1', 'تفعيل تخطيط من اليمين إلى اليسار', 'appearance', true],
            
            // System settings
            ['maintenance_mode', '0', 'وضع الصيانة', 'system', false],
            ['maintenance_message', 'الموقع قيد الصيانة حالياً، يرجى المحاولة لاحقاً', 'رسالة الصيانة', 'system', false],
            ['debug_mode', '0', 'وضع التصحيح', 'system', false],
            ['items_per_page', '20', 'عدد العناصر في الصفحة', 'system', false],
            ['timezone', 'Asia/Riyadh', 'المنطقة الزمنية', 'system', false],
            ['date_format', 'Y-m-d', 'تنسيق التاريخ', 'system', false],
            ['time_format', 'H:i', 'تنسيق الوقت', 'system', false],
        ];

        $stmt = $pdo->prepare("
            INSERT IGNORE INTO system_settings
            (setting_key, setting_value, setting_description, setting_group, is_public)
            VALUES (?, ?, ?, ?, ?)
        ");

        foreach ($default_settings as $setting) {
            $stmt->execute($setting);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log('Error initializing default settings: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get a system setting value
 *
 * @param PDO $pdo Database connection
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value or default
 */
function get_setting($pdo, $key, $default = null) {
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? $result['setting_value'] : $default;
    } catch (PDOException $e) {
        error_log('Error getting setting ' . $key . ': ' . $e->getMessage());
        return $default;
    }
}

/**
 * Update a system setting
 *
 * @param PDO $pdo Database connection
 * @param string $key Setting key
 * @param mixed $value New value
 * @return bool True if successful, false otherwise
 */
function update_setting($pdo, $key, $value) {
    try {
        $stmt = $pdo->prepare("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?");
        $stmt->execute([$value, $key]);
        
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        error_log('Error updating setting ' . $key . ': ' . $e->getMessage());
        return false;
    }
}

/**
 * Get all settings grouped by category
 *
 * @param PDO $pdo Database connection
 * @return array Settings grouped by category
 */
function get_all_settings($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM system_settings ORDER BY setting_group, setting_key");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Group settings by category
        $grouped_settings = [];
        foreach ($settings as $setting) {
            $group = $setting['setting_group'];
            if (!isset($grouped_settings[$group])) {
                $grouped_settings[$group] = [];
            }
            $grouped_settings[$group][] = $setting;
        }
        
        return $grouped_settings;
    } catch (PDOException $e) {
        error_log('Error getting all settings: ' . $e->getMessage());
        return [];
    }
}

/**
 * Apply system settings to the application
 * This function should be called at the beginning of the application
 *
 * @param PDO $pdo Database connection
 */
function apply_system_settings($pdo) {
    // Create settings table if it doesn't exist
    create_settings_table($pdo);
    
    // Initialize default settings
    initialize_default_settings($pdo);
    
    // Apply settings to the application
    // For example, define constants based on settings
    $site_name = get_setting($pdo, 'site_name', 'نظام إدارة حلقات تحفيظ القرآن');
    if (!defined('SITE_NAME')) {
        define('SITE_NAME', $site_name);
    }
    
    // Set timezone
    $timezone = get_setting($pdo, 'timezone', 'Asia/Riyadh');
    date_default_timezone_set($timezone);
    
    // Apply other settings as needed
}
