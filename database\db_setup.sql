-- Create database
CREATE DATABASE IF NOT EXISTS quran_circle_management;
USE quran_circle_management;

-- 1. Users table
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone_number VARCHAR(50),
    profile_picture_url VARCHAR(512),
    role_id INT NOT NULL,
    center_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Roles table
CREATE TABLE IF NOT EXISTS roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY,
    role_name <PERSON><PERSON><PERSON><PERSON>(100) UNIQUE NOT NULL,
    description TEXT
);

-- 3. Centers table
CREATE TABLE IF NOT EXISTS centers (
    center_id INT AUTO_INCREMENT PRIMARY KEY,
    center_name VARCHAR(255) NOT NULL,
    address TEXT,
    contact_person_name VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    logo_url VARCHAR(512),
    director_user_id INT UNIQUE,
    subscription_plan_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. User Center Affiliations table
CREATE TABLE IF NOT EXISTS user_center_affiliations (
    affiliation_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    center_id INT NOT NULL,
    specific_role_in_center VARCHAR(100),
    date_joined DATE NOT NULL,
    is_current BOOLEAN DEFAULT TRUE
);

-- 5. Circles table
CREATE TABLE IF NOT EXISTS circles (
    circle_id INT AUTO_INCREMENT PRIMARY KEY,
    circle_name VARCHAR(255) NOT NULL,
    description TEXT,
    center_id INT NOT NULL,
    teacher_user_id INT NOT NULL,
    level VARCHAR(100),
    schedule_details TEXT,
    max_students INT,
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 6. Student Circle Enrollments table
CREATE TABLE IF NOT EXISTS student_circle_enrollments (
    enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
    student_user_id INT NOT NULL,
    circle_id INT NOT NULL,
    parent_user_id INT,
    enrollment_date DATE NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'withdrawn', 'completed') DEFAULT 'pending',
    notes TEXT
);

-- 7. Attendance Records table
CREATE TABLE IF NOT EXISTS attendance_records (
    attendance_id INT AUTO_INCREMENT PRIMARY KEY,
    enrollment_id INT NOT NULL,
    session_date DATE NOT NULL,
    status ENUM('present', 'absent_excused', 'absent_unexcused', 'late') NOT NULL,
    notes TEXT,
    recorded_by_user_id INT NOT NULL,
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 8. Memorization Schedules table
CREATE TABLE IF NOT EXISTS memorization_schedules (
    schedule_id INT AUTO_INCREMENT PRIMARY KEY,
    enrollment_id INT NOT NULL,
    target_surah_start VARCHAR(100),
    target_ayah_start INT,
    target_surah_end VARCHAR(100),
    target_ayah_end INT,
    target_juz INT,
    target_page_start INT,
    target_page_end INT,
    type ENUM('memorization', 'revision') NOT NULL,
    assigned_date DATE NOT NULL,
    due_date DATE,
    is_completed BOOLEAN DEFAULT FALSE,
    notes TEXT
);

-- 9. Memorization Progress table
CREATE TABLE IF NOT EXISTS memorization_progress (
    progress_id INT AUTO_INCREMENT PRIMARY KEY,
    schedule_id INT,
    enrollment_id INT NOT NULL,
    surah_name VARCHAR(100) NOT NULL,
    ayah_from INT NOT NULL,
    ayah_to INT NOT NULL,
    recitation_date DATETIME NOT NULL,
    memorization_quality ENUM('excellent', 'very_good', 'good', 'fair', 'poor'),
    tajweed_application ENUM('excellent', 'very_good', 'good', 'fair', 'poor'),
    fluency ENUM('excellent', 'very_good', 'good', 'fair', 'poor'),
    teacher_notes TEXT,
    teacher_audio_note_url VARCHAR(512),
    recorded_by_user_id INT NOT NULL
);

-- 10. Assignments table
CREATE TABLE IF NOT EXISTS assignments (
    assignment_id INT AUTO_INCREMENT PRIMARY KEY,
    circle_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    due_date DATETIME,
    attachment_url VARCHAR(512),
    created_by_user_id INT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert default roles
INSERT INTO roles (role_name, description) VALUES 
('system_owner', 'System owner with full access to all features'),
('center_admin', 'Center administrator with access to center management'),
('teacher', 'Teacher with access to teaching features'),
('student', 'Student with access to learning features'),
('parent', 'Parent with access to monitor their children');

-- Add foreign key constraints after all tables are created
ALTER TABLE users
    ADD CONSTRAINT fk_users_roles FOREIGN KEY (role_id) REFERENCES roles(role_id),
    ADD CONSTRAINT fk_users_centers FOREIGN KEY (center_id) REFERENCES centers(center_id);

ALTER TABLE centers
    ADD CONSTRAINT fk_centers_users FOREIGN KEY (director_user_id) REFERENCES users(user_id);

ALTER TABLE user_center_affiliations
    ADD CONSTRAINT fk_affiliations_users FOREIGN KEY (user_id) REFERENCES users(user_id),
    ADD CONSTRAINT fk_affiliations_centers FOREIGN KEY (center_id) REFERENCES centers(center_id);

ALTER TABLE circles
    ADD CONSTRAINT fk_circles_centers FOREIGN KEY (center_id) REFERENCES centers(center_id),
    ADD CONSTRAINT fk_circles_teachers FOREIGN KEY (teacher_user_id) REFERENCES users(user_id);

ALTER TABLE student_circle_enrollments
    ADD CONSTRAINT fk_enrollments_students FOREIGN KEY (student_user_id) REFERENCES users(user_id),
    ADD CONSTRAINT fk_enrollments_circles FOREIGN KEY (circle_id) REFERENCES circles(circle_id),
    ADD CONSTRAINT fk_enrollments_parents FOREIGN KEY (parent_user_id) REFERENCES users(user_id);
