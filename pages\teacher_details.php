<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Check if teacher ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف المعلم غير صحيح');
    redirect('pages/teachers.php');
}

$teacher_id = (int)$_GET['id'];
$error = '';
$success = '';

// Get teacher information
try {
    $stmt = $pdo->prepare("
        SELECT u.*, c.center_name, r.role_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ? AND r.role_name = 'teacher'
    ");
    $stmt->execute([$teacher_id]);
    $teacher = $stmt->fetch();
    
    if (!$teacher) {
        set_flash_message('danger', 'المعلم غير موجود');
        redirect('pages/teachers.php');
    }
    
    // Check if current user has access to this teacher
    if (has_role('center_admin') && $teacher['center_id'] != $_SESSION['center_id']) {
        set_flash_message('danger', 'غير مصرح لك بالوصول إلى بيانات هذا المعلم');
        redirect('pages/teachers.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المعلم: ' . $e->getMessage();
}

// Get teacher's circles
try {
    $stmt = $pdo->prepare("
        SELECT c.*, 
               (SELECT COUNT(*) FROM student_circle_enrollments sce WHERE sce.circle_id = c.circle_id) AS student_count
        FROM circles c
        WHERE c.teacher_user_id = ?
        ORDER BY c.circle_name
    ");
    $stmt->execute([$teacher_id]);
    $circles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get teacher's students
try {
    $stmt = $pdo->prepare("
        SELECT DISTINCT u.user_id, u.full_name, u.email, u.phone_number, u.is_active,
               c.circle_name, sce.enrollment_date, sce.status
        FROM users u
        JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        WHERE c.teacher_user_id = ?
        ORDER BY u.full_name
    ");
    $stmt->execute([$teacher_id]);
    $students = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب: ' . $e->getMessage();
}

// Get teacher's attendance statistics
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT a.session_date) AS total_sessions,
            SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) AS present_count,
            SUM(CASE WHEN a.status = 'absent_excused' THEN 1 ELSE 0 END) AS excused_count,
            SUM(CASE WHEN a.status = 'absent_unexcused' THEN 1 ELSE 0 END) AS unexcused_count,
            SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) AS late_count
        FROM teacher_attendance a
        WHERE a.teacher_user_id = ?
    ");
    $stmt->execute([$teacher_id]);
    $attendance = $stmt->fetch();
} catch (PDOException $e) {
    // If table doesn't exist, just ignore
    $attendance = [
        'total_sessions' => 0,
        'present_count' => 0,
        'excused_count' => 0,
        'unexcused_count' => 0,
        'late_count' => 0
    ];
}

// Page variables
$page_title = 'تفاصيل المعلم: ' . $teacher['full_name'];
$active_page = 'teachers';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'المعلمين', 'url' => 'teachers.php'],
    ['title' => $teacher['full_name']]
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="fas fa-chalkboard-teacher me-2"></i>
        <?php echo $teacher['full_name']; ?>
    </h1>
    <div>
        <a href="edit_teacher.php?id=<?php echo $teacher_id; ?>" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i> تعديل
        </a>
        <a href="teachers.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <!-- Teacher Information -->
    <div class="col-md-4 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> معلومات المعلم</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <?php if (!empty($teacher['profile_picture_url'])): ?>
                        <img src="<?php echo get_root_url() . $teacher['profile_picture_url']; ?>" alt="صورة المعلم" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    <?php else: ?>
                        <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                            <i class="fas fa-user-tie fa-5x text-secondary"></i>
                        </div>
                    <?php endif; ?>
                    <h4><?php echo $teacher['full_name']; ?></h4>
                    <p class="text-muted">@<?php echo $teacher['username']; ?></p>
                    <span class="badge <?php echo $teacher['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                        <?php echo $teacher['is_active'] ? 'نشط' : 'غير نشط'; ?>
                    </span>
                </div>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-building me-2"></i> المركز</h6>
                    <p><?php echo $teacher['center_name']; ?></p>
                </div>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-envelope me-2"></i> البريد الإلكتروني</h6>
                    <p><?php echo $teacher['email']; ?></p>
                </div>
                
                <?php if (!empty($teacher['phone_number'])): ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-phone me-2"></i> رقم الهاتف</h6>
                    <p><?php echo $teacher['phone_number']; ?></p>
                </div>
                <?php endif; ?>
                
                <?php 
                // Check if specialization column exists
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) 
                    FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'specialization'
                ");
                $stmt->execute();
                $specialization_exists = (bool)$stmt->fetchColumn();
                
                if ($specialization_exists && !empty($teacher['specialization'])): 
                ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-graduation-cap me-2"></i> التخصص</h6>
                    <p><?php echo $teacher['specialization']; ?></p>
                </div>
                <?php endif; ?>
                
                <?php 
                // Check if qualifications column exists
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) 
                    FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'users' 
                    AND COLUMN_NAME = 'qualifications'
                ");
                $stmt->execute();
                $qualifications_exists = (bool)$stmt->fetchColumn();
                
                if ($qualifications_exists && !empty($teacher['qualifications'])): 
                ?>
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-certificate me-2"></i> المؤهلات والخبرات</h6>
                    <p><?php echo nl2br($teacher['qualifications']); ?></p>
                </div>
                <?php endif; ?>
                
                <div class="mb-3">
                    <h6 class="fw-bold"><i class="fas fa-calendar-alt me-2"></i> تاريخ التسجيل</h6>
                    <p><?php echo date('Y-m-d', strtotime($teacher['created_at'])); ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Teacher Statistics -->
    <div class="col-md-8 mb-4">
        <div class="row">
            <!-- Circles Count -->
            <div class="col-md-4 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-circle fa-3x mb-3"></i>
                        <h2 class="card-title"><?php echo count($circles); ?></h2>
                        <p class="card-text">الحلقات</p>
                    </div>
                </div>
            </div>
            
            <!-- Students Count -->
            <div class="col-md-4 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-graduate fa-3x mb-3"></i>
                        <h2 class="card-title"><?php echo count($students); ?></h2>
                        <p class="card-text">الطلاب</p>
                    </div>
                </div>
            </div>
            
            <!-- Attendance Rate -->
            <div class="col-md-4 mb-3">
                <div class="card bg-info text-white h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-3x mb-3"></i>
                        <?php 
                        $attendance_rate = 0;
                        if ($attendance['total_sessions'] > 0) {
                            $attendance_rate = round(($attendance['present_count'] / $attendance['total_sessions']) * 100);
                        }
                        ?>
                        <h2 class="card-title"><?php echo $attendance_rate; ?>%</h2>
                        <p class="card-text">نسبة الحضور</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Teacher's Circles -->
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> الحلقات</h5>
            </div>
            <div class="card-body">
                <?php if (empty($circles)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا توجد حلقات مسجلة لهذا المعلم.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover datatable">
                            <thead>
                                <tr>
                                    <th>اسم الحلقة</th>
                                    <th>المستوى</th>
                                    <th>عدد الطلاب</th>
                                    <th>مواعيد الحلقة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($circles as $circle): ?>
                                    <tr>
                                        <td><?php echo $circle['circle_name']; ?></td>
                                        <td><?php echo $circle['level']; ?></td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo $circle['student_count']; ?> / <?php echo $circle['max_students']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $circle['schedule_details']; ?></td>
                                        <td>
                                            <span class="badge <?php echo $circle['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo $circle['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="circle_details.php?id=<?php echo $circle['circle_id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Teacher's Students -->
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0"><i class="fas fa-user-graduate me-2"></i> الطلاب</h5>
            </div>
            <div class="card-body">
                <?php if (empty($students)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا يوجد طلاب مسجلين في حلقات هذا المعلم.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover datatable">
                            <thead>
                                <tr>
                                    <th>اسم الطالب</th>
                                    <th>الحلقة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td>
                                            <?php echo $student['full_name']; ?>
                                            <?php if (!$student['is_active']): ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $student['circle_name']; ?></td>
                                        <td><?php echo date('Y-m-d', strtotime($student['enrollment_date'])); ?></td>
                                        <td>
                                            <?php
                                            $status = $student['status'];
                                            $badge_class = '';
                                            $status_text = '';

                                            switch ($status) {
                                                case 'approved':
                                                    $badge_class = 'bg-success';
                                                    $status_text = 'مقبول';
                                                    break;
                                                case 'pending':
                                                    $badge_class = 'bg-warning text-dark';
                                                    $status_text = 'قيد الانتظار';
                                                    break;
                                                case 'rejected':
                                                    $badge_class = 'bg-danger';
                                                    $status_text = 'مرفوض';
                                                    break;
                                                case 'withdrawn':
                                                    $badge_class = 'bg-secondary';
                                                    $status_text = 'منسحب';
                                                    break;
                                                case 'completed':
                                                    $badge_class = 'bg-info';
                                                    $status_text = 'مكتمل';
                                                    break;
                                                default:
                                                    $badge_class = 'bg-secondary';
                                                    $status_text = $status;
                                            }
                                            ?>
                                            <span class="badge <?php echo $badge_class; ?>"><?php echo $status_text; ?></span>
                                        </td>
                                        <td>
                                            <a href="student_details.php?id=<?php echo $student['user_id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer template
include_template('footer');
?>
