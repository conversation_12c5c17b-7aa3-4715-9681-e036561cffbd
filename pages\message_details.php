<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('../auth/login.php');
}

// Get message ID from URL
$message_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($message_id <= 0) {
    set_flash_message('danger', 'معرف الرسالة غير صحيح');
    redirect('messages.php');
}

$error = '';
$success = '';
$user_id = $_SESSION['user_id'];

// Get message details
try {
    $stmt = $pdo->prepare("
        SELECT m.message_id, m.subject, m.content, m.sent_at, m.read_at,
               sender.user_id AS sender_id, sender.full_name AS sender_name,
               recipient.user_id AS recipient_id, recipient.full_name AS recipient_name
        FROM messages m
        JOIN users sender ON m.sender_user_id = sender.user_id
        JOIN users recipient ON m.recipient_user_id = recipient.user_id
        WHERE m.message_id = ? AND (m.sender_user_id = ? OR m.recipient_user_id = ?)
    ");
    $stmt->execute([$message_id, $user_id, $user_id]);
    $message = $stmt->fetch();

    if (!$message) {
        set_flash_message('danger', 'الرسالة غير موجودة أو غير مصرح لك بالوصول إليها');
        redirect('messages.php');
    }

    // Mark message as read if current user is the recipient and it's not read yet
    if ($message['recipient_id'] == $user_id && !$message['read_at']) {
        $stmt = $pdo->prepare("UPDATE messages SET read_at = NOW() WHERE message_id = ?");
        $stmt->execute([$message_id]);
        $message['read_at'] = date('Y-m-d H:i:s');
    }

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الرسالة: ' . $e->getMessage();
}

// Process reply form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_reply'])) {
    $reply_content = sanitize_input($_POST['reply_content']);

    if (empty($reply_content)) {
        $error = 'يرجى كتابة محتوى الرد';
    } else {
        try {
            // Send reply
            $reply_subject = 'رد: ' . $message['subject'];
            $recipient_id = ($message['sender_id'] == $user_id) ? $message['recipient_id'] : $message['sender_id'];

            $stmt = $pdo->prepare("
                INSERT INTO messages (sender_user_id, recipient_user_id, subject, content, sent_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$user_id, $recipient_id, $reply_subject, $reply_content]);

            $success = 'تم إرسال الرد بنجاح';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إرسال الرد: ' . $e->getMessage();
        }
    }
}

// Process delete message
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_message'])) {
    try {
        // Check if deletion columns exist
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM information_schema.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'messages'
            AND COLUMN_NAME = 'is_deleted_by_sender'
        ");
        $stmt->execute();
        $deletion_columns_exist = (bool)$stmt->fetchColumn();

        if ($deletion_columns_exist) {
            // Use soft delete
            if ($message['sender_id'] == $user_id) {
                $stmt = $pdo->prepare("UPDATE messages SET is_deleted_by_sender = TRUE WHERE message_id = ?");
            } else {
                $stmt = $pdo->prepare("UPDATE messages SET is_deleted_by_recipient = TRUE WHERE message_id = ?");
            }
            $stmt->execute([$message_id]);
        } else {
            // Hard delete (only if user is sender or recipient)
            $stmt = $pdo->prepare("DELETE FROM messages WHERE message_id = ? AND (sender_user_id = ? OR recipient_user_id = ?)");
            $stmt->execute([$message_id, $user_id, $user_id]);
        }

        set_flash_message('success', 'تم حذف الرسالة بنجاح');
        redirect('messages.php');
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء حذف الرسالة: ' . $e->getMessage();
    }
}

// Page variables
$page_title = 'تفاصيل الرسالة';
$active_page = 'messages';

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0"><i class="fas fa-envelope-open me-2"></i> تفاصيل الرسالة</h1>
        <a href="<?php echo get_root_url(); ?>pages/messages.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-2"></i> العودة إلى الرسائل
        </a>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <?php if (isset($message)): ?>
        <!-- Message Details -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        <?php echo htmlspecialchars($message['subject']); ?>
                    </h5>
                    <div>
                        <?php if (!$message['read_at'] && $message['recipient_id'] == $user_id): ?>
                            <span class="badge bg-warning">جديدة</span>
                        <?php else: ?>
                            <span class="badge bg-success">مقروءة</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>من:</strong> <?php echo htmlspecialchars($message['sender_name']); ?>
                    </div>
                    <div class="col-md-6">
                        <strong>إلى:</strong> <?php echo htmlspecialchars($message['recipient_name']); ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>تاريخ الإرسال:</strong> <?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?>
                    </div>
                    <?php if ($message['read_at']): ?>
                    <div class="col-md-6">
                        <strong>تاريخ القراءة:</strong> <?php echo date('Y-m-d H:i', strtotime($message['read_at'])); ?>
                    </div>
                    <?php endif; ?>
                </div>
                <hr>
                <div class="message-content">
                    <h6>محتوى الرسالة:</h6>
                    <div class="bg-light p-3 rounded">
                        <?php echo nl2br(htmlspecialchars($message['content'])); ?>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#replyModal">
                            <i class="fas fa-reply me-1"></i> رد
                        </button>
                        <?php if ($message['sender_id'] != $user_id): ?>
                        <a href="<?php echo get_root_url(); ?>pages/messages.php?new=1&recipient=<?php echo $message['sender_id']; ?>" class="btn btn-info">
                            <i class="fas fa-envelope me-1"></i> رسالة جديدة
                        </a>
                        <?php endif; ?>
                    </div>
                    <div>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash me-1"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reply Modal -->
        <div class="modal fade" id="replyModal" tabindex="-1" aria-labelledby="replyModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="replyModalLabel">رد على الرسالة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?id=' . $message_id; ?>">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label"><strong>الموضوع:</strong></label>
                                <p class="form-text">رد: <?php echo htmlspecialchars($message['subject']); ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><strong>المرسل إليه:</strong></label>
                                <p class="form-text">
                                    <?php 
                                    echo ($message['sender_id'] == $user_id) ? 
                                         htmlspecialchars($message['recipient_name']) : 
                                         htmlspecialchars($message['sender_name']); 
                                    ?>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label for="reply_content" class="form-label">محتوى الرد <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="reply_content" name="reply_content" rows="6" required placeholder="اكتب ردك هنا..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" name="send_reply" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i> إرسال الرد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Delete Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>هل أنت متأكد من رغبتك في حذف هذه الرسالة؟</p>
                        <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']) . '?id=' . $message_id; ?>" style="display: inline;">
                            <button type="submit" name="delete_message" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i> حذف
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
// Include footer template
include_template('footer');
?>
