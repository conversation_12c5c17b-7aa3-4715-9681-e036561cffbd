<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('auth/login.php');
}

// Get teacher ID from query string
$teacher_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Check if teacher ID is valid
if ($teacher_id <= 0) {
    set_flash_message('danger', 'معرف المعلم غير صالح');
    redirect('pages/teachers.php');
}

// Get teacher information
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name, u.email, u.phone_number, u.center_id, c.center_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ? AND r.role_name = 'teacher' AND u.is_active = TRUE
    ");
    $stmt->execute([$teacher_id]);
    $teacher = $stmt->fetch();
    
    if (!$teacher) {
        set_flash_message('danger', 'المعلم غير موجود أو غير نشط');
        redirect('pages/teachers.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المعلم: ' . $e->getMessage();
}

// Check if user has permission to contact this teacher
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$can_contact = false;

try {
    if ($role_name === 'system_owner' || $role_name === 'center_admin') {
        // System owner and center admin can contact any teacher
        $can_contact = true;
    } elseif ($role_name === 'student') {
        // Student can contact their teachers
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM student_circle_enrollments sce
            JOIN circles c ON sce.circle_id = c.circle_id
            WHERE sce.student_user_id = ? AND c.teacher_user_id = ?
        ");
        $stmt->execute([$user_id, $teacher_id]);
        $can_contact = (bool)$stmt->fetchColumn();
    } elseif ($role_name === 'parent') {
        // Parent can contact their children's teachers
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM student_circle_enrollments sce
            JOIN circles c ON sce.circle_id = c.circle_id
            WHERE sce.parent_user_id = ? AND c.teacher_user_id = ?
        ");
        $stmt->execute([$user_id, $teacher_id]);
        $can_contact = (bool)$stmt->fetchColumn();
    } elseif ($role_name === 'teacher') {
        // Teachers can contact other teachers in the same center
        $stmt = $pdo->prepare("
            SELECT center_id FROM users WHERE user_id = ?
        ");
        $stmt->execute([$user_id]);
        $sender_center_id = $stmt->fetchColumn();
        
        $can_contact = ($sender_center_id == $teacher['center_id']);
    }
    
    if (!$can_contact) {
        set_flash_message('danger', 'غير مصرح لك بالتواصل مع هذا المعلم');
        redirect('pages/messages.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
}

// Process send message form
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $subject = sanitize_input($_POST['subject']);
    $message_text = sanitize_input($_POST['message_text']);
    
    // Validate input
    if (empty($subject) || empty($message_text)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // Insert message
            $stmt = $pdo->prepare("
                INSERT INTO messages (sender_user_id, recipient_user_id, subject, message_text, sent_at)
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$user_id, $teacher_id, $subject, $message_text]);
            
            $success = 'تم إرسال الرسالة بنجاح';
            
            // Clear form data after successful submission
            $subject = '';
            $message_text = '';
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إرسال الرسالة: ' . $e->getMessage();
        }
    }
}

// Page variables
$page_title = 'التواصل مع المعلم: ' . $teacher['full_name'];

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo get_root_url(); ?>index.php">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="<?php echo get_root_url(); ?>pages/messages.php">الرسائل</a></li>
            <li class="breadcrumb-item active" aria-current="page">التواصل مع <?php echo $teacher['full_name']; ?></li>
        </ol>
    </nav>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <div class="row">
        <!-- Teacher Information -->
        <div class="col-md-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-user-tie me-2"></i> معلومات المعلم</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px;">
                            <i class="fas fa-user-tie fa-3x text-secondary"></i>
                        </div>
                        <h4><?php echo $teacher['full_name']; ?></h4>
                        <p class="text-muted"><?php echo $teacher['center_name']; ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="fw-bold"><i class="fas fa-envelope me-2"></i> البريد الإلكتروني</h6>
                        <p><?php echo $teacher['email']; ?></p>
                    </div>
                    
                    <?php if (!empty($teacher['phone_number'])): ?>
                    <div class="mb-3">
                        <h6 class="fw-bold"><i class="fas fa-phone me-2"></i> رقم الهاتف</h6>
                        <p><?php echo $teacher['phone_number']; ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Message Form -->
        <div class="col-md-8 mb-4">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-paper-plane me-2"></i> إرسال رسالة</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>">
                        <div class="mb-3">
                            <label for="subject" class="form-label">الموضوع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" required value="<?php echo isset($subject) ? $subject : ''; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="message_text" class="form-label">نص الرسالة <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message_text" name="message_text" rows="6" required><?php echo isset($message_text) ? $message_text : ''; ?></textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?php echo get_root_url(); ?>pages/messages.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i> العودة إلى الرسائل
                            </a>
                            <button type="submit" name="send_message" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i> إرسال الرسالة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> سيتم إرسال الرسالة إلى المعلم <?php echo $teacher['full_name']; ?> ويمكنه الرد عليها من خلال نظام الرسائل.
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer_inner.php';
?>
