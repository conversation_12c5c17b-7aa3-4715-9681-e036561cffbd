<?php
// This file is a redirect to fix the incorrect URL path
// Redirect to the correct path for student assignments

// Get the requested URI
$requested_uri = $_SERVER['REQUEST_URI'];

// Include common functions if available
if (file_exists('../includes/common.php')) {
    require_once '../includes/common.php';

    // Set a flash message to inform the user about the incorrect URL
    if (function_exists('set_flash_message')) {
        set_flash_message('warning', 'تم تصحيح المسار تلقائيًا. الرجاء استخدام الرابط الصحيح في المستقبل.');
    }

    // Redirect to the correct URL
    if (function_exists('get_root_url')) {
        header("Location: " . get_root_url() . "pages/student_assignments.php");
        exit;
    }
}

// Fallback redirect if common.php is not available or functions don't exist
header("Location: /qurann/pages/student_assignments.php");
exit;
?>
