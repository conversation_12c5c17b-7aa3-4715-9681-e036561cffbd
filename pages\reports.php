<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('auth/login.php');
}

// Check if user has appropriate role
if (!has_any_role(['teacher', 'center_admin', 'system_owner'])) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('index.php');
}

$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$error = '';
$success = '';

// Get report type from query string
$report_type = isset($_GET['type']) ? sanitize_input($_GET['type']) : 'attendance';
$valid_report_types = ['attendance', 'memorization', 'students', 'circles'];

if (!in_array($report_type, $valid_report_types)) {
    $report_type = 'attendance';
}

// Get center ID from query string if provided (for system owner)
$center_id = null;
if ($role_name === 'system_owner' && isset($_GET['center_id'])) {
    $center_id = (int)$_GET['center_id'];
}

// Get circle ID from query string if provided
$circle_id = isset($_GET['circle_id']) ? (int)$_GET['circle_id'] : null;

// Get date range from query string if provided
$start_date = isset($_GET['start_date']) ? sanitize_input($_GET['start_date']) : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? sanitize_input($_GET['end_date']) : date('Y-m-d');

// Get centers (for system owner)
$centers = [];
if ($role_name === 'system_owner') {
    try {
        $stmt = $pdo->prepare("
            SELECT center_id, center_name
            FROM centers
            WHERE is_active = TRUE
            ORDER BY center_name
        ");
        $stmt->execute();
        $centers = $stmt->fetchAll();

        // If no center ID provided, use the first one
        if (!$center_id && !empty($centers)) {
            $center_id = $centers[0]['center_id'];
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
    }
}

// Get circles based on role
try {
    if ($role_name === 'teacher') {
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name
            FROM circles c
            WHERE c.teacher_user_id = ? AND c.is_active = TRUE
            ORDER BY c.circle_name
        ");
        $stmt->execute([$user_id]);
    } elseif ($role_name === 'center_admin') {
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name
            FROM circles c
            WHERE c.center_id = ? AND c.is_active = TRUE
            ORDER BY c.circle_name
        ");
        $stmt->execute([$_SESSION['center_id']]);
    } else { // system_owner
        $stmt = $pdo->prepare("
            SELECT c.circle_id, c.circle_name
            FROM circles c
            WHERE c.center_id = ? AND c.is_active = TRUE
            ORDER BY c.circle_name
        ");
        $stmt->execute([$center_id]);
    }

    $circles = $stmt->fetchAll();

    // If no circle ID provided, use the first one
    if (!$circle_id && !empty($circles)) {
        $circle_id = $circles[0]['circle_id'];
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقات: ' . $e->getMessage();
}

// Get report data based on report type
$report_data = [];
$chart_data = [];

if (empty($error)) {
    try {
        if ($report_type === 'attendance') {
            // Get attendance report data
            $stmt = $pdo->prepare("
                SELECT ar.session_date, ar.status, u.full_name AS student_name
                FROM attendance_records ar
                JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
                JOIN users u ON sce.student_user_id = u.user_id
                WHERE sce.circle_id = ? AND ar.session_date BETWEEN ? AND ?
                ORDER BY ar.session_date DESC, u.full_name
            ");
            $stmt->execute([$circle_id, $start_date, $end_date]);
            $report_data = $stmt->fetchAll();

            // Get attendance summary for chart
            $stmt = $pdo->prepare("
                SELECT ar.status, COUNT(*) AS count
                FROM attendance_records ar
                JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
                WHERE sce.circle_id = ? AND ar.session_date BETWEEN ? AND ?
                GROUP BY ar.status
            ");
            $stmt->execute([$circle_id, $start_date, $end_date]);
            $chart_data = $stmt->fetchAll();
        } elseif ($report_type === 'memorization') {
            // Get memorization report data
            $stmt = $pdo->prepare("
                SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to,
                       mp.recitation_date, mp.memorization_quality, mp.tajweed_application, mp.fluency,
                       u.full_name AS student_name
                FROM memorization_progress mp
                JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
                JOIN users u ON sce.student_user_id = u.user_id
                WHERE sce.circle_id = ? AND mp.recitation_date BETWEEN ? AND ?
                ORDER BY mp.recitation_date DESC, u.full_name
            ");
            $stmt->execute([$circle_id, $start_date, $end_date]);
            $report_data = $stmt->fetchAll();

            // Get memorization quality summary for chart
            $stmt = $pdo->prepare("
                SELECT mp.memorization_quality, COUNT(*) AS count
                FROM memorization_progress mp
                JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
                WHERE sce.circle_id = ? AND mp.recitation_date BETWEEN ? AND ?
                GROUP BY mp.memorization_quality
            ");
            $stmt->execute([$circle_id, $start_date, $end_date]);
            $chart_data = $stmt->fetchAll();
        } elseif ($report_type === 'students') {
            // Get students report data
            if ($role_name === 'system_owner') {
                $stmt = $pdo->prepare("
                    SELECT u.user_id, u.full_name, u.email, u.phone_number, u.created_at,
                           c.circle_name, ce.center_name
                    FROM users u
                    JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id
                    JOIN circles c ON sce.circle_id = c.circle_id
                    JOIN centers ce ON c.center_id = ce.center_id
                    WHERE u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
                    AND ce.center_id = ?
                    ORDER BY u.full_name
                ");
                $stmt->execute([$center_id]);
            } elseif ($role_name === 'center_admin') {
                $stmt = $pdo->prepare("
                    SELECT u.user_id, u.full_name, u.email, u.phone_number, u.created_at,
                           c.circle_name
                    FROM users u
                    JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id
                    JOIN circles c ON sce.circle_id = c.circle_id
                    WHERE u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
                    AND c.center_id = ?
                    ORDER BY u.full_name
                ");
                $stmt->execute([$_SESSION['center_id']]);
            } else { // teacher
                $stmt = $pdo->prepare("
                    SELECT u.user_id, u.full_name, u.email, u.phone_number, u.created_at
                    FROM users u
                    JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id
                    WHERE u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
                    AND sce.circle_id = ?
                    ORDER BY u.full_name
                ");
                $stmt->execute([$circle_id]);
            }
            $report_data = $stmt->fetchAll();

            // Get students count by circle for chart
            if ($role_name === 'system_owner') {
                $stmt = $pdo->prepare("
                    SELECT c.circle_name, COUNT(sce.student_user_id) AS count
                    FROM circles c
                    LEFT JOIN student_circle_enrollments sce ON c.circle_id = sce.circle_id
                    WHERE c.center_id = ?
                    GROUP BY c.circle_id, c.circle_name
                    ORDER BY c.circle_name
                ");
                $stmt->execute([$center_id]);
            } elseif ($role_name === 'center_admin') {
                $stmt = $pdo->prepare("
                    SELECT c.circle_name, COUNT(sce.student_user_id) AS count
                    FROM circles c
                    LEFT JOIN student_circle_enrollments sce ON c.circle_id = sce.circle_id
                    WHERE c.center_id = ?
                    GROUP BY c.circle_id, c.circle_name
                    ORDER BY c.circle_name
                ");
                $stmt->execute([$_SESSION['center_id']]);
            } else { // teacher
                $stmt = $pdo->prepare("
                    SELECT 'طلاب الحلقة' AS circle_name, COUNT(sce.student_user_id) AS count
                    FROM student_circle_enrollments sce
                    WHERE sce.circle_id = ?
                ");
                $stmt->execute([$circle_id]);
            }
            $chart_data = $stmt->fetchAll();
        } elseif ($report_type === 'circles') {
            // Get circles report data
            if ($role_name === 'system_owner') {
                $stmt = $pdo->prepare("
                    SELECT c.circle_id, c.circle_name, c.level, c.schedule_details, c.start_date,
                           u.full_name AS teacher_name, ce.center_name,
                           (SELECT COUNT(*) FROM student_circle_enrollments sce WHERE sce.circle_id = c.circle_id) AS student_count
                    FROM circles c
                    JOIN users u ON c.teacher_user_id = u.user_id
                    JOIN centers ce ON c.center_id = ce.center_id
                    WHERE ce.center_id = ?
                    ORDER BY c.circle_name
                ");
                $stmt->execute([$center_id]);
            } elseif ($role_name === 'center_admin') {
                $stmt = $pdo->prepare("
                    SELECT c.circle_id, c.circle_name, c.level, c.schedule_details, c.start_date,
                           u.full_name AS teacher_name,
                           (SELECT COUNT(*) FROM student_circle_enrollments sce WHERE sce.circle_id = c.circle_id) AS student_count
                    FROM circles c
                    JOIN users u ON c.teacher_user_id = u.user_id
                    WHERE c.center_id = ?
                    ORDER BY c.circle_name
                ");
                $stmt->execute([$_SESSION['center_id']]);
            } else { // teacher
                $stmt = $pdo->prepare("
                    SELECT c.circle_id, c.circle_name, c.level, c.schedule_details, c.start_date,
                           (SELECT COUNT(*) FROM student_circle_enrollments sce WHERE sce.circle_id = c.circle_id) AS student_count
                    FROM circles c
                    WHERE c.teacher_user_id = ?
                    ORDER BY c.circle_name
                ");
                $stmt->execute([$user_id]);
            }
            $report_data = $stmt->fetchAll();

            // Get circles count by level for chart
            if ($role_name === 'system_owner') {
                $stmt = $pdo->prepare("
                    SELECT c.level, COUNT(*) AS count
                    FROM circles c
                    WHERE c.center_id = ?
                    GROUP BY c.level
                    ORDER BY c.level
                ");
                $stmt->execute([$center_id]);
            } elseif ($role_name === 'center_admin') {
                $stmt = $pdo->prepare("
                    SELECT c.level, COUNT(*) AS count
                    FROM circles c
                    WHERE c.center_id = ?
                    GROUP BY c.level
                    ORDER BY c.level
                ");
                $stmt->execute([$_SESSION['center_id']]);
            } else { // teacher
                $stmt = $pdo->prepare("
                    SELECT c.level, COUNT(*) AS count
                    FROM circles c
                    WHERE c.teacher_user_id = ?
                    GROUP BY c.level
                    ORDER BY c.level
                ");
                $stmt->execute([$user_id]);
            }
            $chart_data = $stmt->fetchAll();
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات التقرير: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <?php include_once '../includes/header.php'; ?>

    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>التقارير والإحصائيات</h1>
            <div>
                <button class="btn btn-outline-primary me-2" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> طباعة التقرير
                </button>
                <button class="btn btn-outline-success" id="exportBtn">
                    <i class="fas fa-file-excel me-1"></i> تصدير إلى Excel
                </button>
            </div>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-filter me-2"></i> خيارات التقرير
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                            <div class="mb-3">
                                <label for="type" class="form-label">نوع التقرير</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="attendance" <?php echo $report_type === 'attendance' ? 'selected' : ''; ?>>تقرير الحضور والغياب</option>
                                    <option value="memorization" <?php echo $report_type === 'memorization' ? 'selected' : ''; ?>>تقرير الحفظ والمراجعة</option>
                                    <option value="students" <?php echo $report_type === 'students' ? 'selected' : ''; ?>>تقرير الطلاب</option>
                                    <option value="circles" <?php echo $report_type === 'circles' ? 'selected' : ''; ?>>تقرير الحلقات</option>
                                </select>
                            </div>

                            <?php if ($role_name === 'system_owner' && !empty($centers)): ?>
                                <div class="mb-3">
                                    <label for="center_id" class="form-label">المركز</label>
                                    <select class="form-select" id="center_id" name="center_id">
                                        <?php foreach ($centers as $center): ?>
                                            <option value="<?php echo $center['center_id']; ?>" <?php echo $center_id == $center['center_id'] ? 'selected' : ''; ?>>
                                                <?php echo $center['center_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($circles) && ($report_type === 'attendance' || $report_type === 'memorization')): ?>
                                <div class="mb-3">
                                    <label for="circle_id" class="form-label">الحلقة</label>
                                    <select class="form-select" id="circle_id" name="circle_id">
                                        <?php foreach ($circles as $circle): ?>
                                            <option value="<?php echo $circle['circle_id']; ?>" <?php echo $circle_id == $circle['circle_id'] ? 'selected' : ''; ?>>
                                                <?php echo $circle['circle_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            <?php endif; ?>

                            <?php if ($report_type === 'attendance' || $report_type === 'memorization'): ?>
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="end_date" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>" required>
                                </div>
                            <?php endif; ?>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> عرض التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i> معلومات
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>يمكنك استخدام التقارير للحصول على إحصائيات ومعلومات مفصلة عن:</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check-circle text-success me-2"></i> حضور وغياب الطلاب</li>
                            <li><i class="fas fa-check-circle text-success me-2"></i> تقدم الطلاب في الحفظ والمراجعة</li>
                            <li><i class="fas fa-check-circle text-success me-2"></i> معلومات الطلاب</li>
                            <li><i class="fas fa-check-circle text-success me-2"></i> معلومات الحلقات</li>
                        </ul>
                        <hr>
                        <p class="mb-0">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            يمكنك طباعة التقرير أو تصديره إلى ملف Excel للاستخدام الخارجي.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <?php if ($report_type === 'attendance'): ?>
                                <i class="fas fa-calendar-check me-2"></i> تقرير الحضور والغياب
                            <?php elseif ($report_type === 'memorization'): ?>
                                <i class="fas fa-book-reader me-2"></i> تقرير الحفظ والمراجعة
                            <?php elseif ($report_type === 'students'): ?>
                                <i class="fas fa-user-graduate me-2"></i> تقرير الطلاب
                            <?php elseif ($report_type === 'circles'): ?>
                                <i class="fas fa-users me-2"></i> تقرير الحلقات
                            <?php endif; ?>

                            <?php if (!empty($circles) && isset($circle_id) && ($report_type === 'attendance' || $report_type === 'memorization')): ?>
                                <?php
                                foreach ($circles as $circle) {
                                    if ($circle['circle_id'] == $circle_id) {
                                        echo ' - ' . $circle['circle_name'];
                                        break;
                                    }
                                }
                                ?>
                            <?php endif; ?>

                            <?php if ($report_type === 'attendance' || $report_type === 'memorization'): ?>
                                <small class="ms-2">(<?php echo $start_date; ?> - <?php echo $end_date; ?>)</small>
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($report_data)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> لا توجد بيانات متاحة للتقرير المطلوب.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive" id="reportTable">
                                <?php if ($report_type === 'attendance'): ?>
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>الطالب</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $record): ?>
                                                <tr>
                                                    <td><?php echo date('Y-m-d', strtotime($record['session_date'])); ?></td>
                                                    <td><?php echo $record['student_name']; ?></td>
                                                    <td>
                                                        <?php
                                                        $status = $record['status'];
                                                        $badge_class = '';
                                                        $status_text = '';

                                                        switch ($status) {
                                                            case 'present':
                                                                $badge_class = 'success';
                                                                $status_text = 'حاضر';
                                                                break;
                                                            case 'absent_excused':
                                                                $badge_class = 'warning';
                                                                $status_text = 'غائب بعذر';
                                                                break;
                                                            case 'absent_unexcused':
                                                                $badge_class = 'danger';
                                                                $status_text = 'غائب';
                                                                break;
                                                            case 'late':
                                                                $badge_class = 'info';
                                                                $status_text = 'متأخر';
                                                                break;
                                                            default:
                                                                $badge_class = 'secondary';
                                                                $status_text = $status;
                                                        }

                                                        echo '<span class="badge bg-' . $badge_class . '">' . $status_text . '</span>';
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php elseif ($report_type === 'memorization'): ?>
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>الطالب</th>
                                                <th>السورة</th>
                                                <th>من الآية</th>
                                                <th>إلى الآية</th>
                                                <th>جودة الحفظ</th>
                                                <th>التجويد</th>
                                                <th>الطلاقة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $record): ?>
                                                <tr>
                                                    <td><?php echo date('Y-m-d', strtotime($record['recitation_date'])); ?></td>
                                                    <td><?php echo $record['student_name']; ?></td>
                                                    <td><?php echo $record['surah_name']; ?></td>
                                                    <td><?php echo $record['ayah_from']; ?></td>
                                                    <td><?php echo $record['ayah_to']; ?></td>
                                                    <td>
                                                        <?php
                                                        $quality = $record['memorization_quality'];
                                                        $badge_class = '';

                                                        switch ($quality) {
                                                            case 'excellent':
                                                                $badge_class = 'success';
                                                                $quality_text = 'ممتاز';
                                                                break;
                                                            case 'very_good':
                                                                $badge_class = 'primary';
                                                                $quality_text = 'جيد جداً';
                                                                break;
                                                            case 'good':
                                                                $badge_class = 'info';
                                                                $quality_text = 'جيد';
                                                                break;
                                                            case 'fair':
                                                                $badge_class = 'warning';
                                                                $quality_text = 'مقبول';
                                                                break;
                                                            case 'poor':
                                                                $badge_class = 'danger';
                                                                $quality_text = 'ضعيف';
                                                                break;
                                                            default:
                                                                $badge_class = 'secondary';
                                                                $quality_text = $quality;
                                                        }

                                                        echo '<span class="badge bg-' . $badge_class . '">' . $quality_text . '</span>';
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $tajweed = $record['tajweed_application'];
                                                        $badge_class = '';

                                                        switch ($tajweed) {
                                                            case 'excellent':
                                                                $badge_class = 'success';
                                                                $tajweed_text = 'ممتاز';
                                                                break;
                                                            case 'very_good':
                                                                $badge_class = 'primary';
                                                                $tajweed_text = 'جيد جداً';
                                                                break;
                                                            case 'good':
                                                                $badge_class = 'info';
                                                                $tajweed_text = 'جيد';
                                                                break;
                                                            case 'fair':
                                                                $badge_class = 'warning';
                                                                $tajweed_text = 'مقبول';
                                                                break;
                                                            case 'poor':
                                                                $badge_class = 'danger';
                                                                $tajweed_text = 'ضعيف';
                                                                break;
                                                            default:
                                                                $badge_class = 'secondary';
                                                                $tajweed_text = $tajweed;
                                                        }

                                                        echo '<span class="badge bg-' . $badge_class . '">' . $tajweed_text . '</span>';
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $fluency = $record['fluency'];
                                                        $badge_class = '';

                                                        switch ($fluency) {
                                                            case 'excellent':
                                                                $badge_class = 'success';
                                                                $fluency_text = 'ممتاز';
                                                                break;
                                                            case 'very_good':
                                                                $badge_class = 'primary';
                                                                $fluency_text = 'جيد جداً';
                                                                break;
                                                            case 'good':
                                                                $badge_class = 'info';
                                                                $fluency_text = 'جيد';
                                                                break;
                                                            case 'fair':
                                                                $badge_class = 'warning';
                                                                $fluency_text = 'مقبول';
                                                                break;
                                                            case 'poor':
                                                                $badge_class = 'danger';
                                                                $fluency_text = 'ضعيف';
                                                                break;
                                                            default:
                                                                $badge_class = 'secondary';
                                                                $fluency_text = $fluency;
                                                        }

                                                        echo '<span class="badge bg-' . $badge_class . '">' . $fluency_text . '</span>';
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php elseif ($report_type === 'students'): ?>
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>الاسم</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>رقم الهاتف</th>
                                                <?php if ($role_name === 'system_owner'): ?>
                                                    <th>المركز</th>
                                                <?php endif; ?>
                                                <?php if ($role_name === 'system_owner' || $role_name === 'center_admin'): ?>
                                                    <th>الحلقة</th>
                                                <?php endif; ?>
                                                <th>تاريخ التسجيل</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $student): ?>
                                                <tr>
                                                    <td><?php echo $student['full_name']; ?></td>
                                                    <td><?php echo $student['email']; ?></td>
                                                    <td><?php echo $student['phone_number']; ?></td>
                                                    <?php if ($role_name === 'system_owner'): ?>
                                                        <td><?php echo $student['center_name']; ?></td>
                                                    <?php endif; ?>
                                                    <?php if ($role_name === 'system_owner' || $role_name === 'center_admin'): ?>
                                                        <td><?php echo $student['circle_name']; ?></td>
                                                    <?php endif; ?>
                                                    <td><?php echo date('Y-m-d', strtotime($student['created_at'])); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php elseif ($report_type === 'circles'): ?>
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم الحلقة</th>
                                                <th>المستوى</th>
                                                <?php if ($role_name === 'system_owner'): ?>
                                                    <th>المركز</th>
                                                <?php endif; ?>
                                                <?php if ($role_name === 'system_owner' || $role_name === 'center_admin'): ?>
                                                    <th>المعلم</th>
                                                <?php endif; ?>
                                                <th>عدد الطلاب</th>
                                                <th>مواعيد الحلقة</th>
                                                <th>تاريخ البدء</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($report_data as $circle): ?>
                                                <tr>
                                                    <td><?php echo $circle['circle_name']; ?></td>
                                                    <td><?php echo $circle['level']; ?></td>
                                                    <?php if ($role_name === 'system_owner'): ?>
                                                        <td><?php echo $circle['center_name']; ?></td>
                                                    <?php endif; ?>
                                                    <?php if ($role_name === 'system_owner' || $role_name === 'center_admin'): ?>
                                                        <td><?php echo $circle['teacher_name']; ?></td>
                                                    <?php endif; ?>
                                                    <td><?php echo $circle['student_count']; ?></td>
                                                    <td><?php echo $circle['schedule_details']; ?></td>
                                                    <td><?php echo date('Y-m-d', strtotime($circle['start_date'])); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                <?php endif; ?>
                            </div>

                            <div class="mt-4">
                                <h5>الرسم البياني</h5>
                                <div style="height: 300px;">
                                    <canvas id="reportChart"></canvas>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include_once '../includes/footer.php'; ?>

    <!-- SheetJS (Excel Export) -->
    <script src="https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.full.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form when changing report type, center, or circle
            const typeSelect = document.getElementById('type');
            const centerSelect = document.getElementById('center_id');
            const circleSelect = document.getElementById('circle_id');

            if (typeSelect) {
                typeSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }

            if (centerSelect) {
                centerSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }

            if (circleSelect) {
                circleSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }

            // Export to Excel
            const exportBtn = document.getElementById('exportBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    const reportTable = document.getElementById('reportTable');
                    if (reportTable) {
                        const wb = XLSX.utils.table_to_book(reportTable.querySelector('table'), {sheet: "Report"});
                        XLSX.writeFile(wb, 'تقرير_<?php echo $report_type; ?>_<?php echo date('Y-m-d'); ?>.xlsx');
                    }
                });
            }

            // Chart.js
            const ctx = document.getElementById('reportChart');
            if (ctx) {
                <?php if ($report_type === 'attendance' && !empty($chart_data)): ?>
                    // Prepare attendance chart data
                    const labels = [];
                    const data = [];
                    const colors = [];

                    <?php foreach ($chart_data as $item): ?>
                        <?php
                        $status = $item['status'];
                        $status_text = '';
                        $color = '';

                        switch ($status) {
                            case 'present':
                                $status_text = 'حاضر';
                                $color = 'rgba(40, 167, 69, 0.7)';
                                break;
                            case 'absent_excused':
                                $status_text = 'غائب بعذر';
                                $color = 'rgba(255, 193, 7, 0.7)';
                                break;
                            case 'absent_unexcused':
                                $status_text = 'غائب';
                                $color = 'rgba(220, 53, 69, 0.7)';
                                break;
                            case 'late':
                                $status_text = 'متأخر';
                                $color = 'rgba(23, 162, 184, 0.7)';
                                break;
                            default:
                                $status_text = $status;
                                $color = 'rgba(108, 117, 125, 0.7)';
                        }
                        ?>
                        labels.push('<?php echo $status_text; ?>');
                        data.push(<?php echo $item['count']; ?>);
                        colors.push('<?php echo $color; ?>');
                    <?php endforeach; ?>

                    new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: labels,
                            datasets: [{
                                data: data,
                                backgroundColor: colors,
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'right',
                                }
                            }
                        }
                    });
                <?php elseif ($report_type === 'memorization' && !empty($chart_data)): ?>
                    // Prepare memorization chart data
                    const labels = [];
                    const data = [];
                    const colors = [];

                    <?php foreach ($chart_data as $item): ?>
                        <?php
                        $quality = $item['memorization_quality'];
                        $quality_text = '';
                        $color = '';

                        switch ($quality) {
                            case 'excellent':
                                $quality_text = 'ممتاز';
                                $color = 'rgba(40, 167, 69, 0.7)';
                                break;
                            case 'very_good':
                                $quality_text = 'جيد جداً';
                                $color = 'rgba(0, 123, 255, 0.7)';
                                break;
                            case 'good':
                                $quality_text = 'جيد';
                                $color = 'rgba(23, 162, 184, 0.7)';
                                break;
                            case 'fair':
                                $quality_text = 'مقبول';
                                $color = 'rgba(255, 193, 7, 0.7)';
                                break;
                            case 'poor':
                                $quality_text = 'ضعيف';
                                $color = 'rgba(220, 53, 69, 0.7)';
                                break;
                            default:
                                $quality_text = $quality;
                                $color = 'rgba(108, 117, 125, 0.7)';
                        }
                        ?>
                        labels.push('<?php echo $quality_text; ?>');
                        data.push(<?php echo $item['count']; ?>);
                        colors.push('<?php echo $color; ?>');
                    <?php endforeach; ?>

                    new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: labels,
                            datasets: [{
                                data: data,
                                backgroundColor: colors,
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'right',
                                }
                            }
                        }
                    });
                <?php elseif ($report_type === 'students' && !empty($chart_data)): ?>
                    // Prepare students chart data
                    const labels = [];
                    const data = [];

                    <?php foreach ($chart_data as $item): ?>
                        labels.push('<?php echo $item['circle_name']; ?>');
                        data.push(<?php echo $item['count']; ?>);
                    <?php endforeach; ?>

                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'عدد الطلاب',
                                data: data,
                                backgroundColor: 'rgba(0, 123, 255, 0.7)',
                                borderColor: 'rgba(0, 123, 255, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            }
                        }
                    });
                <?php elseif ($report_type === 'circles' && !empty($chart_data)): ?>
                    // Prepare circles chart data
                    const labels = [];
                    const data = [];

                    <?php foreach ($chart_data as $item): ?>
                        labels.push('<?php echo $item['level']; ?>');
                        data.push(<?php echo $item['count']; ?>);
                    <?php endforeach; ?>

                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'عدد الحلقات',
                                data: data,
                                backgroundColor: 'rgba(40, 167, 69, 0.7)',
                                borderColor: 'rgba(40, 167, 69, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            }
                        }
                    });
                <?php endif; ?>
            }
        });
    </script>
</body>
</html>