/* Custom styles for System Owner Dashboard */

/* Dashboard header */
.dashboard-header {
    background: linear-gradient(135deg, #0d6efd, #0a58ca);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 15px rgba(13, 110, 253, 0.2);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/images/pattern.png');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

.dashboard-header .content {
    position: relative;
    z-index: 1;
}

.dashboard-header h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.dashboard-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.dashboard-header .date {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Enhanced stats cards */
.stats-card-enhanced {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    padding: 25px;
    margin-bottom: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-bottom: 5px solid transparent;
}

.stats-card-enhanced:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stats-card-enhanced .icon-wrapper {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.stats-card-enhanced .icon-wrapper i {
    font-size: 2rem;
}

.stats-card-enhanced .stats-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    line-height: 1;
}

.stats-card-enhanced .stats-label {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
}

.stats-card-enhanced .stats-change {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 0.9rem;
    padding: 5px 10px;
    border-radius: 20px;
}

.stats-card-enhanced .stats-change.positive {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.stats-card-enhanced .stats-change.negative {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* Color variations */
.stats-primary-enhanced {
    border-bottom-color: #0d6efd;
}
.stats-primary-enhanced .icon-wrapper {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.stats-success-enhanced {
    border-bottom-color: #198754;
}
.stats-success-enhanced .icon-wrapper {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.stats-warning-enhanced {
    border-bottom-color: #ffc107;
}
.stats-warning-enhanced .icon-wrapper {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.stats-danger-enhanced {
    border-bottom-color: #dc3545;
}
.stats-danger-enhanced .icon-wrapper {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.stats-info-enhanced {
    border-bottom-color: #0dcaf0;
}
.stats-info-enhanced .icon-wrapper {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
}

.stats-purple-enhanced {
    border-bottom-color: #6f42c1;
}
.stats-purple-enhanced .icon-wrapper {
    background-color: rgba(111, 66, 193, 0.1);
    color: #6f42c1;
}

/* Quick actions section */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.action-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 25px 20px;
    text-align: center;
    transition: all 0.3s ease;
    border-top: 5px solid transparent;
}

.action-card:hover {
    transform: translateY(-7px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.action-card .icon-container {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.action-card .icon-container i {
    font-size: 1.8rem;
}

.action-card .action-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.action-card .action-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 15px;
}

/* Color variations for action cards */
.action-primary {
    border-top-color: #0d6efd;
}
.action-primary .icon-container {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.action-success {
    border-top-color: #198754;
}
.action-success .icon-container {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.action-warning {
    border-top-color: #ffc107;
}
.action-warning .icon-container {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.action-danger {
    border-top-color: #dc3545;
}
.action-danger .icon-container {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.action-info {
    border-top-color: #0dcaf0;
}
.action-info .icon-container {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
}

.action-purple {
    border-top-color: #6f42c1;
}
.action-purple .icon-container {
    background-color: rgba(111, 66, 193, 0.1);
    color: #6f42c1;
}

/* Enhanced cards */
.enhanced-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 25px;
    overflow: hidden;
}

.enhanced-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.enhanced-card .card-header {
    padding: 20px 25px;
    border-bottom: none;
}

.enhanced-card .card-body {
    padding: 25px;
}

.enhanced-card .card-footer {
    background: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 25px;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

/* Responsive adjustments */
@media (max-width: 992px) {
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 20px;
    }
    
    .dashboard-header h1 {
        font-size: 1.8rem;
    }
    
    .stats-card-enhanced .stats-value {
        font-size: 2rem;
    }
    
    .action-card {
        padding: 20px 15px;
    }
    
    .action-card .icon-container {
        width: 60px;
        height: 60px;
    }
}
