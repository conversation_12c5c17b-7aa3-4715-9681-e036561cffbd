<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('../auth/login.php');
}

// If user is not a student, show a message but allow access for testing
if (!has_role('student')) {
    set_flash_message('warning', 'هذه الصفحة مخصصة للطلاب. أنت تشاهدها حالياً في وضع المعاينة.');
}

// Get user information
$user_id = $_SESSION['user_id'];
$error = '';
$success = '';

// For testing purposes, if the user is not a student, we'll use a sample student ID
// In a real scenario, we would redirect non-students away from this page
if (!has_role('student')) {
    // Try to find a student to use for demonstration
    try {
        $stmt = $pdo->prepare("
            SELECT user_id FROM users
            JOIN roles ON users.role_id = roles.role_id
            WHERE roles.role_name = 'student'
            LIMIT 1
        ");
        $stmt->execute();
        $demo_student = $stmt->fetch();

        if ($demo_student) {
            $student_id = $demo_student['user_id'];
            $success = 'تم تحميل بيانات طالب للعرض التوضيحي.';
        } else {
            $student_id = $user_id; // Fallback to current user
            $error = 'لم يتم العثور على أي طالب في النظام للعرض التوضيحي.';
        }
    } catch (PDOException $e) {
        $student_id = $user_id; // Fallback to current user
        $error = 'حدث خطأ أثناء البحث عن طالب للعرض التوضيحي: ' . $e->getMessage();
    }
} else {
    $student_id = $user_id;
}

// Get student's circle enrollment
try {
    $stmt = $pdo->prepare("
        SELECT sce.enrollment_id, sce.circle_id, c.circle_name, c.level,
               t.user_id AS teacher_id, t.full_name AS teacher_name
        FROM student_circle_enrollments sce
        JOIN circles c ON sce.circle_id = c.circle_id
        JOIN users t ON c.teacher_user_id = t.user_id
        WHERE sce.student_user_id = ? AND sce.status = 'approved'
        ORDER BY sce.enrollment_date DESC
        LIMIT 1
    ");
    $stmt->execute([$student_id]);
    $enrollment = $stmt->fetch();

    if ($enrollment) {
        $enrollment_id = $enrollment['enrollment_id'];
    } else {
        $error = 'أنت غير مسجل في أي حلقة حالياً';
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
}

// Get student's memorization progress statistics
if (isset($enrollment_id) && empty($error)) {
    try {
        // Get overall progress statistics
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS total_recitations,
                   SUM(CASE WHEN memorization_quality = 'excellent' THEN 1 ELSE 0 END) AS excellent_count,
                   SUM(CASE WHEN memorization_quality = 'very_good' THEN 1 ELSE 0 END) AS very_good_count,
                   SUM(CASE WHEN memorization_quality = 'good' THEN 1 ELSE 0 END) AS good_count,
                   SUM(CASE WHEN memorization_quality = 'fair' THEN 1 ELSE 0 END) AS fair_count,
                   SUM(CASE WHEN memorization_quality = 'poor' THEN 1 ELSE 0 END) AS poor_count,
                   COUNT(DISTINCT surah_name) AS unique_surahs
            FROM memorization_progress
            WHERE enrollment_id = ?
        ");
        $stmt->execute([$enrollment_id]);
        $progress_stats = $stmt->fetch();

        // Get monthly progress statistics for chart
        $stmt = $pdo->prepare("
            SELECT
                DATE_FORMAT(recitation_date, '%Y-%m') AS month,
                COUNT(*) AS recitation_count,
                SUM(ayah_to - ayah_from + 1) AS verses_count
            FROM memorization_progress
            WHERE enrollment_id = ?
            GROUP BY DATE_FORMAT(recitation_date, '%Y-%m')
            ORDER BY month ASC
            LIMIT 12
        ");
        $stmt->execute([$enrollment_id]);
        $monthly_stats = $stmt->fetchAll();

        // Get all memorization progress
        $stmt = $pdo->prepare("
            SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to,
                   mp.recitation_date, mp.memorization_quality, mp.tajweed_application, mp.fluency,
                   mp.teacher_notes, u.full_name AS teacher_name
            FROM memorization_progress mp
            JOIN users u ON mp.recorded_by_user_id = u.user_id
            WHERE mp.enrollment_id = ?
            ORDER BY mp.recitation_date DESC
        ");
        $stmt->execute([$enrollment_id]);
        $all_progress = $stmt->fetchAll();

        // Get current memorization schedule
        $stmt = $pdo->prepare("
            SELECT schedule_id, target_surah_start, target_ayah_start,
                   target_surah_end, target_ayah_end,
                   target_juz, target_page_start, target_page_end,
                   type, assigned_date, due_date, is_completed
            FROM memorization_schedules
            WHERE enrollment_id = ? AND is_completed = FALSE
            ORDER BY due_date ASC
            LIMIT 1
        ");
        $stmt->execute([$enrollment_id]);
        $current_schedule = $stmt->fetch();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات التقدم: ' . $e->getMessage();
    }
}

// Page variables
$page_title = 'تقدمي في الحفظ';
$active_page = 'progress';

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page,
    'use_datatables' => true,
    'extra_css' => '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">'
]);
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0"><i class="fas fa-chart-line me-2"></i> تقدمي في الحفظ</h1>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <?php if (isset($enrollment) && !empty($enrollment)): ?>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> معلومات الحلقة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم الحلقة:</strong> <?php echo $enrollment['circle_name']; ?></p>
                        <p><strong>المستوى:</strong> <?php echo $enrollment['level']; ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المعلم:</strong> <?php echo $enrollment['teacher_name']; ?></p>
                        <p>
                            <a href="<?php echo get_root_url(); ?>pages/teacher_contact.php?id=<?php echo $enrollment['teacher_id']; ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-envelope me-1"></i> التواصل مع المعلم
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <?php if (isset($current_schedule) && !empty($current_schedule)): ?>
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-bookmark me-2"></i> خطة الحفظ الحالية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><?php echo $current_schedule['type'] == 'memorization' ? 'حفظ جديد' : 'مراجعة'; ?></h5>
                            <?php if (!empty($current_schedule['target_surah_start'])): ?>
                                <p>
                                    <strong>المقطع:</strong>
                                    <?php echo $current_schedule['target_surah_start']; ?>
                                    (<?php echo $current_schedule['target_ayah_start']; ?>) -
                                    <?php echo $current_schedule['target_surah_end']; ?>
                                    (<?php echo $current_schedule['target_ayah_end']; ?>)
                                </p>
                            <?php endif; ?>

                            <?php if (!empty($current_schedule['target_juz'])): ?>
                                <p><strong>الجزء:</strong> <?php echo $current_schedule['target_juz']; ?></p>
                            <?php endif; ?>

                            <?php if (!empty($current_schedule['target_page_start'])): ?>
                                <p>
                                    <strong>الصفحات:</strong>
                                    <?php echo $current_schedule['target_page_start']; ?> -
                                    <?php echo $current_schedule['target_page_end']; ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <p><strong>تاريخ التكليف:</strong> <?php echo !empty($current_schedule['assigned_date']) ? date('Y-m-d', strtotime($current_schedule['assigned_date'])) : 'غير محدد'; ?></p>
                            <p><strong>تاريخ الاستحقاق:</strong> <?php echo !empty($current_schedule['due_date']) ? date('Y-m-d', strtotime($current_schedule['due_date'])) : 'غير محدد'; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Progress Statistics -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i> إحصائيات التقدم</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($progress_stats) && $progress_stats['total_recitations'] > 0): ?>
                            <div class="row text-center mb-4">
                                <div class="col-md-4 mb-3">
                                    <div class="stats-card stats-primary">
                                        <i class="fas fa-book-reader"></i>
                                        <div class="stats-value"><?php echo $progress_stats['total_recitations']; ?></div>
                                        <div class="stats-label">عدد التسميعات</div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="stats-card stats-success">
                                        <i class="fas fa-star"></i>
                                        <div class="stats-value"><?php echo $progress_stats['excellent_count']; ?></div>
                                        <div class="stats-label">تقييم ممتاز</div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="stats-card stats-warning">
                                        <i class="fas fa-book"></i>
                                        <div class="stats-value"><?php echo $progress_stats['unique_surahs']; ?></div>
                                        <div class="stats-label">عدد السور</div>
                                    </div>
                                </div>
                            </div>

                            <div class="progress-chart-container">
                                <canvas id="qualityChart" width="400" height="300"></canvas>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> لا توجد إحصائيات متاحة حالياً. ابدأ بتسميع حفظك للمعلم لتظهر الإحصائيات هنا.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i> التقدم الشهري</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($monthly_stats) && !empty($monthly_stats)): ?>
                            <div class="progress-chart-container">
                                <canvas id="monthlyChart" width="400" height="300"></canvas>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> لا توجد إحصائيات شهرية متاحة حالياً.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Memorization History -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i> سجل الحفظ</h5>
            </div>
            <div class="card-body">
                <?php if (isset($all_progress) && !empty($all_progress)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover datatable">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>السورة</th>
                                    <th>الآيات</th>
                                    <th>جودة الحفظ</th>
                                    <th>التجويد</th>
                                    <th>الطلاقة</th>
                                    <th>المعلم</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($all_progress as $progress): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d', strtotime($progress['recitation_date'])); ?></td>
                                        <td><?php echo $progress['surah_name']; ?></td>
                                        <td><?php echo $progress['ayah_from'] . ' - ' . $progress['ayah_to']; ?></td>
                                        <td>
                                            <?php
                                            $quality = $progress['memorization_quality'];
                                            $color = '';
                                            switch ($quality) {
                                                case 'excellent': $color = 'success'; break;
                                                case 'very_good': $color = 'primary'; break;
                                                case 'good': $color = 'info'; break;
                                                case 'fair': $color = 'warning'; break;
                                                case 'poor': $color = 'danger'; break;
                                            }
                                            echo '<span class="badge bg-' . $color . '">' . ucfirst(str_replace('_', ' ', $quality)) . '</span>';
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $tajweed = $progress['tajweed_application'];
                                            $color = '';
                                            switch ($tajweed) {
                                                case 'excellent': $color = 'success'; break;
                                                case 'very_good': $color = 'primary'; break;
                                                case 'good': $color = 'info'; break;
                                                case 'fair': $color = 'warning'; break;
                                                case 'poor': $color = 'danger'; break;
                                            }
                                            echo '<span class="badge bg-' . $color . '">' . ucfirst(str_replace('_', ' ', $tajweed)) . '</span>';
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $fluency = $progress['fluency'];
                                            $color = '';
                                            switch ($fluency) {
                                                case 'excellent': $color = 'success'; break;
                                                case 'very_good': $color = 'primary'; break;
                                                case 'good': $color = 'info'; break;
                                                case 'fair': $color = 'warning'; break;
                                                case 'poor': $color = 'danger'; break;
                                            }
                                            echo '<span class="badge bg-' . $color . '">' . ucfirst(str_replace('_', ' ', $fluency)) . '</span>';
                                            ?>
                                        </td>
                                        <td><?php echo $progress['teacher_name']; ?></td>
                                        <td>
                                            <?php if (!empty($progress['teacher_notes'])): ?>
                                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="<?php echo htmlspecialchars($progress['teacher_notes']); ?>">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> لا يوجد سجل حفظ متاح حالياً.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <h4 class="alert-heading">لم يتم تسجيلك في أي حلقة بعد!</h4>
            <p>يبدو أنك لم تنضم إلى أي حلقة تحفيظ حتى الآن. يرجى التواصل مع إدارة المركز للتسجيل في إحدى الحلقات.</p>
        </div>
    <?php endif; ?>
</div>

<?php if (isset($progress_stats) && $progress_stats['total_recitations'] > 0): ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Quality distribution chart
        var qualityCtx = document.getElementById('qualityChart').getContext('2d');
        var qualityChart = new Chart(qualityCtx, {
            type: 'pie',
            data: {
                labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف'],
                datasets: [{
                    label: 'توزيع جودة الحفظ',
                    data: [
                        <?php echo $progress_stats['excellent_count']; ?>,
                        <?php echo $progress_stats['very_good_count']; ?>,
                        <?php echo $progress_stats['good_count']; ?>,
                        <?php echo $progress_stats['fair_count']; ?>,
                        <?php echo $progress_stats['poor_count']; ?>
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(23, 162, 184, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'توزيع جودة الحفظ'
                    }
                }
            }
        });

        <?php if (isset($monthly_stats) && !empty($monthly_stats)): ?>
        // Monthly progress chart
        var monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        var monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: [
                    <?php
                    foreach ($monthly_stats as $stat) {
                        $date = new DateTime($stat['month'] . '-01');
                        echo "'" . $date->format('F Y') . "', ";
                    }
                    ?>
                ],
                datasets: [{
                    label: 'عدد التسميعات',
                    data: [
                        <?php
                        foreach ($monthly_stats as $stat) {
                            echo $stat['recitation_count'] . ', ';
                        }
                        ?>
                    ],
                    backgroundColor: 'rgba(0, 123, 255, 0.5)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                }, {
                    label: 'عدد الآيات',
                    data: [
                        <?php
                        foreach ($monthly_stats as $stat) {
                            echo $stat['verses_count'] . ', ';
                        }
                        ?>
                    ],
                    backgroundColor: 'rgba(40, 167, 69, 0.5)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1,
                    type: 'line'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'التقدم الشهري'
                    }
                }
            }
        });
        <?php endif; ?>
    });
</script>
<?php endif; ?>

<?php
// Include footer template
include_template('footer');
?>
