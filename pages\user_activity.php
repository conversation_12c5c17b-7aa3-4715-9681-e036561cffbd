<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/activity_logger.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$error = '';
$success = '';

// Get filter parameters
$user_filter = $_GET['user_id'] ?? '';
$activity_filter = $_GET['activity'] ?? '';
$days_filter = (int)($_GET['days'] ?? 7);
$page = (int)($_GET['page'] ?? 1);
$per_page = 50;
$offset = ($page - 1) * $per_page;

// Create user_sessions table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_sessions (
            session_id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            logout_time TIMESTAMP NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            session_duration INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_login_time (login_time),
            INDEX idx_is_active (is_active)
        )
    ");
} catch (PDOException $e) {
    // Table might already exist
}

// Get user activity data from multiple sources
$activities = [];

try {
    // First, check which tables actually exist
    $existing_tables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }

    // 1. User registrations (from users table) - REAL DATA
    $user_registration_query = "
        SELECT
            'تسجيل مستخدم جديد' as activity_type,
            CONCAT('تم تسجيل مستخدم جديد: ', u.full_name, ' (', r.role_name, ')') as description,
            u.created_at as timestamp,
            'النظام' as user_name,
            NULL as user_id,
            NULL as ip_address,
            'USER_REGISTRATION' as category,
            'INFO' as severity
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND u.created_at IS NOT NULL
    ";

    // 2. Student enrollments (from student_circle_enrollments table) - REAL DATA
    $enrollment_query = "
        SELECT
            'تسجيل في الحلقات' as activity_type,
            CONCAT('تسجيل طالب: ', u.full_name, ' في حلقة ', c.circle_name,
                   CASE WHEN p.full_name IS NOT NULL THEN CONCAT(' (ولي الأمر: ', p.full_name, ')') ELSE '' END) as description,
            sce.enrollment_date as timestamp,
            'النظام' as user_name,
            NULL as user_id,
            NULL as ip_address,
            'ENROLLMENT' as category,
            'INFO' as severity
        FROM student_circle_enrollments sce
        JOIN users u ON sce.student_user_id = u.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        LEFT JOIN users p ON sce.parent_user_id = p.user_id
        WHERE sce.enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND sce.enrollment_date IS NOT NULL
    ";

    // 3. Announcements (from announcements table) - REAL DATA
    // Check which column exists for the user who created the announcement
    $announcements_query = "";
    if (in_array('announcements', $existing_tables)) {
        // Check if created_by_user_id or sender_user_id exists
        try {
            $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'created_by_user_id'");
            $stmt->execute();
            $has_created_by = $stmt->rowCount() > 0;

            $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'sender_user_id'");
            $stmt->execute();
            $has_sender = $stmt->rowCount() > 0;

            $user_id_column = $has_sender ? 'sender_user_id' : ($has_created_by ? 'created_by_user_id' : 'NULL');

            $announcements_query = "
                SELECT
                    'إنشاء إعلان' as activity_type,
                    CONCAT('تم إنشاء إعلان: ', a.title, ' (', COALESCE(a.target_role, 'all'), ')') as description,
                    a.created_at as timestamp,
                    COALESCE(u.full_name, 'النظام') as user_name,
                    a.{$user_id_column} as user_id,
                    NULL as ip_address,
                    'ANNOUNCEMENT' as category,
                    'INFO' as severity
                FROM announcements a
                LEFT JOIN users u ON a.{$user_id_column} = u.user_id
                WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                AND a.created_at IS NOT NULL
            ";
        } catch (PDOException $e) {
            // If there's an error, skip announcements
            $announcements_query = "";
        }
    }

    // 4. WhatsApp logs (from whatsapp_logs table) - REAL DATA
    $whatsapp_query = "
        SELECT
            'رسائل الواتساب' as activity_type,
            CONCAT('رسالة واتساب: ', wl.message_type, ' إلى ', wl.recipient_number, ' - ', wl.status) as description,
            wl.created_at as timestamp,
            COALESCE(u.full_name, 'النظام') as user_name,
            wl.sent_by_user_id as user_id,
            NULL as ip_address,
            'WHATSAPP' as category,
            CASE wl.status
                WHEN 'failed' THEN 'ERROR'
                WHEN 'sent' THEN 'INFO'
                ELSE 'INFO'
            END as severity
        FROM whatsapp_logs wl
        LEFT JOIN users u ON wl.sent_by_user_id = u.user_id
        WHERE wl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND wl.created_at IS NOT NULL
    ";

    // 5. Attendance records (from attendance_records table) - REAL DATA
    $attendance_query = "
        SELECT
            'تسجيل الحضور' as activity_type,
            CONCAT('حضور: ', student.full_name, ' - ',
                   CASE ar.status
                       WHEN 'present' THEN 'حاضر'
                       WHEN 'absent_excused' THEN 'غائب بعذر'
                       WHEN 'absent_unexcused' THEN 'غائب بدون عذر'
                       WHEN 'late' THEN 'متأخر'
                       ELSE ar.status
                   END, ' - ', ar.session_date) as description,
            COALESCE(ar.recorded_at, CONCAT(ar.session_date, ' 12:00:00')) as timestamp,
            COALESCE(teacher.full_name, 'النظام') as user_name,
            ar.recorded_by_user_id as user_id,
            NULL as ip_address,
            'ATTENDANCE' as category,
            CASE ar.status
                WHEN 'absent_unexcused' THEN 'WARNING'
                WHEN 'late' THEN 'WARNING'
                ELSE 'INFO'
            END as severity
        FROM attendance_records ar
        JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
        JOIN users student ON sce.student_user_id = student.user_id
        LEFT JOIN users teacher ON ar.recorded_by_user_id = teacher.user_id
        WHERE ar.session_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND ar.session_date IS NOT NULL
    ";

    // 6. Memorization progress (from memorization_progress table) - REAL DATA
    $memorization_query = "
        SELECT
            'تقييم الحفظ' as activity_type,
            CONCAT('تقييم حفظ: ', student.full_name, ' - سورة ', mp.surah_name,
                   ' (آية ', mp.ayah_from, ' إلى ', mp.ayah_to, ') - ', mp.memorization_quality) as description,
            mp.recitation_date as timestamp,
            COALESCE(teacher.full_name, 'النظام') as user_name,
            mp.recorded_by_user_id as user_id,
            NULL as ip_address,
            'MEMORIZATION' as category,
            CASE mp.memorization_quality
                WHEN 'excellent' THEN 'INFO'
                WHEN 'very_good' THEN 'INFO'
                WHEN 'good' THEN 'INFO'
                WHEN 'fair' THEN 'WARNING'
                WHEN 'poor' THEN 'WARNING'
                ELSE 'INFO'
            END as severity
        FROM memorization_progress mp
        JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
        JOIN users student ON sce.student_user_id = student.user_id
        LEFT JOIN users teacher ON mp.recorded_by_user_id = teacher.user_id
        WHERE mp.recitation_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND mp.recitation_date IS NOT NULL
    ";

    // 7. Activity logs (from activity_logs table) - COMPREHENSIVE REAL DATA
    $activity_logs_query = "
        SELECT
            CASE al.category
                WHEN 'AUTH' THEN 'تسجيل دخول/خروج'
                WHEN 'USER' THEN 'إدارة المستخدمين'
                WHEN 'ATTENDANCE' THEN 'تسجيل الحضور'
                WHEN 'MEMORIZATION' THEN 'تقييم الحفظ'
                WHEN 'ANNOUNCEMENT' THEN 'إنشاء إعلان'
                WHEN 'WHATSAPP' THEN 'رسائل الواتساب'
                WHEN 'ENROLLMENT' THEN 'تسجيل في الحلقات'
                WHEN 'SYSTEM' THEN 'أنشطة النظام'
                ELSE al.category
            END as activity_type,
            al.description as description,
            al.created_at as timestamp,
            COALESCE(u.full_name, 'النظام') as user_name,
            al.user_id,
            al.ip_address,
            al.category as category,
            'INFO' as severity
        FROM activity_logs al
        LEFT JOIN users u ON al.user_id = u.user_id
        WHERE al.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND al.created_at IS NOT NULL
    ";

    // 8. System logs (from system_logs table) - REAL DATA
    $system_logs_query = "
        SELECT
            CASE sl.log_category
                WHEN 'AUTH' THEN 'تسجيل دخول/خروج'
                WHEN 'USER' THEN 'إدارة المستخدمين'
                WHEN 'ATTENDANCE' THEN 'تسجيل الحضور'
                WHEN 'CONFIG' THEN 'تغيير الإعدادات'
                WHEN 'WHATSAPP' THEN 'رسائل الواتساب'
                WHEN 'SYSTEM' THEN 'أنشطة النظام'
                ELSE sl.log_category
            END as activity_type,
            sl.message as description,
            sl.created_at as timestamp,
            COALESCE(u.full_name, 'النظام') as user_name,
            sl.user_id,
            sl.ip_address,
            sl.log_category as category,
            sl.log_level as severity
        FROM system_logs sl
        LEFT JOIN users u ON sl.user_id = u.user_id
        WHERE sl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND sl.created_at IS NOT NULL
    ";

    // Build the complete query based on existing tables and real data
    $queries = [];
    $params = [];

    // Always include user registrations (users table always exists)
    if (empty($activity_filter) || $activity_filter === 'تسجيل مستخدم جديد') {
        $queries[] = $user_registration_query;
        $params[] = $days_filter;
    }

    // Include enrollments (student_circle_enrollments table)
    if (empty($activity_filter) || $activity_filter === 'تسجيل في الحلقات') {
        $queries[] = $enrollment_query;
        $params[] = $days_filter;
    }

    // Include announcements if table exists and query is valid
    if ((empty($activity_filter) || $activity_filter === 'إنشاء إعلان') && in_array('announcements', $existing_tables) && !empty($announcements_query)) {
        $queries[] = $announcements_query;
        $params[] = $days_filter;
    }

    // Include WhatsApp logs if table exists
    if ((empty($activity_filter) || $activity_filter === 'رسائل الواتساب') && in_array('whatsapp_logs', $existing_tables)) {
        $queries[] = $whatsapp_query;
        $params[] = $days_filter;
    }

    // Include attendance records if table exists
    if ((empty($activity_filter) || $activity_filter === 'تسجيل الحضور') && in_array('attendance_records', $existing_tables)) {
        $queries[] = $attendance_query;
        $params[] = $days_filter;
    }

    // Include memorization progress if table exists
    if ((empty($activity_filter) || $activity_filter === 'تقييم الحفظ') && in_array('memorization_progress', $existing_tables)) {
        $queries[] = $memorization_query;
        $params[] = $days_filter;
    }

    // Include activity logs if table exists (PRIORITY - most comprehensive)
    if (in_array('activity_logs', $existing_tables)) {
        $queries[] = $activity_logs_query;
        $params[] = $days_filter;
    }

    // Include system logs if table exists and activity_logs doesn't exist
    if (!in_array('activity_logs', $existing_tables) && (empty($activity_filter) || in_array($activity_filter, ['تسجيل دخول/خروج', 'إدارة المستخدمين', 'تغيير الإعدادات', 'أنشطة النظام'])) && in_array('system_logs', $existing_tables)) {
        $queries[] = $system_logs_query;
        $params[] = $days_filter;
    }

    if (!empty($queries)) {
        $final_query = "(" . implode(") UNION (", $queries) . ")";

        // Add user filter if specified
        if (!empty($user_filter)) {
            $final_query = "SELECT * FROM ({$final_query}) as combined WHERE user_id = ?";
            $params[] = $user_filter;
        }

        // Add ordering and pagination
        $final_query .= " ORDER BY timestamp DESC LIMIT {$per_page} OFFSET {$offset}";

        $stmt = $pdo->prepare($final_query);
        $stmt->execute($params);
        $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات النشاط: ' . $e->getMessage();
}

// Get total count for pagination - REAL DATA
try {
    $total_activities = 0;

    // Count user registrations
    if (empty($activity_filter) || $activity_filter === 'تسجيل مستخدم جديد') {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
        $stmt->execute([$days_filter]);
        $total_activities += $stmt->fetchColumn();
    }

    // Count enrollments
    if (empty($activity_filter) || $activity_filter === 'تسجيل في الحلقات') {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM student_circle_enrollments WHERE enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND enrollment_date IS NOT NULL");
        $stmt->execute([$days_filter]);
        $total_activities += $stmt->fetchColumn();
    }

    // Count announcements
    if ((empty($activity_filter) || $activity_filter === 'إنشاء إعلان') && in_array('announcements', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM announcements WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
        $stmt->execute([$days_filter]);
        $total_activities += $stmt->fetchColumn();
    }

    // Count WhatsApp logs
    if ((empty($activity_filter) || $activity_filter === 'رسائل الواتساب') && in_array('whatsapp_logs', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM whatsapp_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
        $stmt->execute([$days_filter]);
        $total_activities += $stmt->fetchColumn();
    }

    // Count attendance records
    if ((empty($activity_filter) || $activity_filter === 'تسجيل الحضور') && in_array('attendance_records', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM attendance_records WHERE session_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND session_date IS NOT NULL");
        $stmt->execute([$days_filter]);
        $total_activities += $stmt->fetchColumn();
    }

    // Count memorization progress
    if ((empty($activity_filter) || $activity_filter === 'تقييم الحفظ') && in_array('memorization_progress', $existing_tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM memorization_progress WHERE recitation_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND recitation_date IS NOT NULL");
        $stmt->execute([$days_filter]);
        $total_activities += $stmt->fetchColumn();
    }

    // Count system logs
    if ((empty($activity_filter) || in_array($activity_filter, ['تسجيل دخول/خروج', 'إدارة المستخدمين', 'تغيير الإعدادات', 'أنشطة النظام'])) && in_array('system_logs', $existing_tables)) {
        $where_condition = "";
        $params_for_count = [$days_filter];

        if (!empty($activity_filter)) {
            $category_map = [
                'تسجيل دخول/خروج' => 'AUTH',
                'إدارة المستخدمين' => 'USER',
                'تغيير الإعدادات' => 'CONFIG',
                'أنشطة النظام' => 'SYSTEM'
            ];
            if (isset($category_map[$activity_filter])) {
                $where_condition = " AND log_category = ?";
                $params_for_count[] = $category_map[$activity_filter];
            }
        }

        $stmt = $pdo->prepare("SELECT COUNT(*) FROM system_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL" . $where_condition);
        $stmt->execute($params_for_count);
        $total_activities += $stmt->fetchColumn();
    }

    // Apply user filter to count if specified
    if (!empty($user_filter) && $total_activities > 0) {
        $user_count_query = "
            SELECT COUNT(*) FROM system_logs
            WHERE user_id = ?
            AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ";
        if (!empty($activity_filter)) {
            $category_map = [
                'تسجيل دخول/خروج' => 'AUTH',
                'إدارة المستخدمين' => 'USER',
                'تسجيل الحضور' => 'ATTENDANCE',
                'تغيير الإعدادات' => 'CONFIG',
                'رسائل الواتساب' => 'WHATSAPP'
            ];
            if (isset($category_map[$activity_filter])) {
                $user_count_query .= " AND log_category = ?";
                $stmt = $pdo->prepare($user_count_query);
                $stmt->execute([$user_filter, $days_filter, $category_map[$activity_filter]]);
            } else {
                $stmt = $pdo->prepare($user_count_query);
                $stmt->execute([$user_filter, $days_filter]);
            }
        } else {
            $stmt = $pdo->prepare($user_count_query);
            $stmt->execute([$user_filter, $days_filter]);
        }
        $total_activities = $stmt->fetchColumn();
    }

    $total_pages = ceil($total_activities / $per_page);

} catch (PDOException $e) {
    $total_activities = 0;
    $total_pages = 0;
}

// Get all users for filter dropdown
try {
    $stmt = $pdo->prepare("
        SELECT user_id, full_name, username
        FROM users
        WHERE is_active = 1
        ORDER BY full_name
    ");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $users = [];
}

// Get activity statistics
try {
    $activity_stats = [];

    // User registrations
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
    $stmt->execute([$days_filter]);
    $user_reg_count = $stmt->fetchColumn();
    if ($user_reg_count > 0) {
        $activity_stats[] = ['log_category' => 'USER_REGISTRATION', 'count' => $user_reg_count, 'unique_users' => 1];
    }

    // Enrollments
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM student_circle_enrollments WHERE enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND enrollment_date IS NOT NULL");
    $stmt->execute([$days_filter]);
    $enrollment_count = $stmt->fetchColumn();
    if ($enrollment_count > 0) {
        $activity_stats[] = ['log_category' => 'ENROLLMENT', 'count' => $enrollment_count, 'unique_users' => 1];
    }

    // Announcements
    if (in_array('announcements', $existing_tables)) {
        try {
            // Check which column exists for the user who created the announcement
            $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'created_by_user_id'");
            $stmt->execute();
            $has_created_by = $stmt->rowCount() > 0;

            $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'sender_user_id'");
            $stmt->execute();
            $has_sender = $stmt->rowCount() > 0;

            $user_id_column = $has_sender ? 'sender_user_id' : ($has_created_by ? 'created_by_user_id' : 'NULL');

            if ($user_id_column !== 'NULL') {
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as count, COUNT(DISTINCT a.{$user_id_column}) as unique_users
                    FROM announcements a
                    WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND a.created_at IS NOT NULL
                ");
                $stmt->execute([$days_filter]);
                $announcement_data = $stmt->fetch();
                if ($announcement_data['count'] > 0) {
                    $activity_stats[] = ['log_category' => 'ANNOUNCEMENT', 'count' => $announcement_data['count'], 'unique_users' => $announcement_data['unique_users']];
                }
            }
        } catch (PDOException $e) {
            // Skip announcements if there's an error
        }
    }

    // WhatsApp logs
    if (in_array('whatsapp_logs', $existing_tables)) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count, COUNT(DISTINCT wl.sent_by_user_id) as unique_users
            FROM whatsapp_logs wl
            WHERE wl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND wl.created_at IS NOT NULL
        ");
        $stmt->execute([$days_filter]);
        $whatsapp_data = $stmt->fetch();
        if ($whatsapp_data['count'] > 0) {
            $activity_stats[] = ['log_category' => 'WHATSAPP', 'count' => $whatsapp_data['count'], 'unique_users' => $whatsapp_data['unique_users']];
        }
    }

    // Attendance records
    if (in_array('attendance_records', $existing_tables)) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count, COUNT(DISTINCT ar.recorded_by_user_id) as unique_users
            FROM attendance_records ar
            WHERE ar.session_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND ar.session_date IS NOT NULL
        ");
        $stmt->execute([$days_filter]);
        $attendance_data = $stmt->fetch();
        if ($attendance_data['count'] > 0) {
            $activity_stats[] = ['log_category' => 'ATTENDANCE', 'count' => $attendance_data['count'], 'unique_users' => $attendance_data['unique_users']];
        }
    }

    // Memorization progress
    if (in_array('memorization_progress', $existing_tables)) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count, COUNT(DISTINCT mp.recorded_by_user_id) as unique_users
            FROM memorization_progress mp
            WHERE mp.recitation_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND mp.recitation_date IS NOT NULL
        ");
        $stmt->execute([$days_filter]);
        $memorization_data = $stmt->fetch();
        if ($memorization_data['count'] > 0) {
            $activity_stats[] = ['log_category' => 'MEMORIZATION', 'count' => $memorization_data['count'], 'unique_users' => $memorization_data['unique_users']];
        }
    }

    // System logs
    if (in_array('system_logs', $existing_tables)) {
        $stmt = $pdo->prepare("
            SELECT
                log_category,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as unique_users
            FROM system_logs
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND user_id IS NOT NULL
            AND created_at IS NOT NULL
            GROUP BY log_category
            ORDER BY count DESC
        ");
        $stmt->execute([$days_filter]);
        $system_log_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $activity_stats = array_merge($activity_stats, $system_log_stats);
    }

    // Sort by count
    usort($activity_stats, function($a, $b) {
        return $b['count'] - $a['count'];
    });

} catch (PDOException $e) {
    $activity_stats = [];
}

// Get most active users
try {
    $active_users = [];

    // Get users from different activity sources
    $user_activities = [];

    // From announcements
    if (in_array('announcements', $existing_tables)) {
        try {
            // Check which column exists for the user who created the announcement
            $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'created_by_user_id'");
            $stmt->execute();
            $has_created_by = $stmt->rowCount() > 0;

            $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'sender_user_id'");
            $stmt->execute();
            $has_sender = $stmt->rowCount() > 0;

            $user_id_column = $has_sender ? 'sender_user_id' : ($has_created_by ? 'created_by_user_id' : 'NULL');

            if ($user_id_column !== 'NULL') {
                $stmt = $pdo->prepare("
                    SELECT
                        u.user_id,
                        u.full_name,
                        u.username,
                        COUNT(*) as activity_count,
                        MAX(a.created_at) as last_activity
                    FROM announcements a
                    JOIN users u ON a.{$user_id_column} = u.user_id
                    WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND a.created_at IS NOT NULL
                    GROUP BY u.user_id, u.full_name, u.username
                ");
                $stmt->execute([$days_filter]);
                $announcement_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($announcement_users as $user) {
                    $key = $user['user_id'];
                    if (!isset($user_activities[$key])) {
                        $user_activities[$key] = $user;
                    } else {
                        $user_activities[$key]['activity_count'] += $user['activity_count'];
                        if ($user['last_activity'] > $user_activities[$key]['last_activity']) {
                            $user_activities[$key]['last_activity'] = $user['last_activity'];
                        }
                    }
                }
            }
        } catch (PDOException $e) {
            // Skip announcements if there's an error
        }
    }

    // From WhatsApp logs
    if (in_array('whatsapp_logs', $existing_tables)) {
        $stmt = $pdo->prepare("
            SELECT
                u.user_id,
                u.full_name,
                u.username,
                COUNT(*) as activity_count,
                MAX(wl.created_at) as last_activity
            FROM whatsapp_logs wl
            JOIN users u ON wl.sent_by_user_id = u.user_id
            WHERE wl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND wl.created_at IS NOT NULL
            GROUP BY u.user_id, u.full_name, u.username
        ");
        $stmt->execute([$days_filter]);
        $whatsapp_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($whatsapp_users as $user) {
            $key = $user['user_id'];
            if (!isset($user_activities[$key])) {
                $user_activities[$key] = $user;
            } else {
                $user_activities[$key]['activity_count'] += $user['activity_count'];
                if ($user['last_activity'] > $user_activities[$key]['last_activity']) {
                    $user_activities[$key]['last_activity'] = $user['last_activity'];
                }
            }
        }
    }

    // From attendance records
    if (in_array('attendance_records', $existing_tables)) {
        $stmt = $pdo->prepare("
            SELECT
                u.user_id,
                u.full_name,
                u.username,
                COUNT(*) as activity_count,
                MAX(COALESCE(ar.recorded_at, CONCAT(ar.session_date, ' 12:00:00'))) as last_activity
            FROM attendance_records ar
            JOIN users u ON ar.recorded_by_user_id = u.user_id
            WHERE ar.session_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND ar.session_date IS NOT NULL
            GROUP BY u.user_id, u.full_name, u.username
        ");
        $stmt->execute([$days_filter]);
        $attendance_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($attendance_users as $user) {
            $key = $user['user_id'];
            if (!isset($user_activities[$key])) {
                $user_activities[$key] = $user;
            } else {
                $user_activities[$key]['activity_count'] += $user['activity_count'];
                if ($user['last_activity'] > $user_activities[$key]['last_activity']) {
                    $user_activities[$key]['last_activity'] = $user['last_activity'];
                }
            }
        }
    }

    // From memorization progress
    if (in_array('memorization_progress', $existing_tables)) {
        $stmt = $pdo->prepare("
            SELECT
                u.user_id,
                u.full_name,
                u.username,
                COUNT(*) as activity_count,
                MAX(mp.recitation_date) as last_activity
            FROM memorization_progress mp
            JOIN users u ON mp.recorded_by_user_id = u.user_id
            WHERE mp.recitation_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND mp.recitation_date IS NOT NULL
            GROUP BY u.user_id, u.full_name, u.username
        ");
        $stmt->execute([$days_filter]);
        $memorization_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($memorization_users as $user) {
            $key = $user['user_id'];
            if (!isset($user_activities[$key])) {
                $user_activities[$key] = $user;
            } else {
                $user_activities[$key]['activity_count'] += $user['activity_count'];
                if ($user['last_activity'] > $user_activities[$key]['last_activity']) {
                    $user_activities[$key]['last_activity'] = $user['last_activity'];
                }
            }
        }
    }

    // From system logs
    if (in_array('system_logs', $existing_tables)) {
        $stmt = $pdo->prepare("
            SELECT
                u.user_id,
                u.full_name,
                u.username,
                COUNT(*) as activity_count,
                MAX(sl.created_at) as last_activity
            FROM system_logs sl
            JOIN users u ON sl.user_id = u.user_id
            WHERE sl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND sl.created_at IS NOT NULL
            GROUP BY u.user_id, u.full_name, u.username
        ");
        $stmt->execute([$days_filter]);
        $system_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($system_users as $user) {
            $key = $user['user_id'];
            if (!isset($user_activities[$key])) {
                $user_activities[$key] = $user;
            } else {
                $user_activities[$key]['activity_count'] += $user['activity_count'];
                if ($user['last_activity'] > $user_activities[$key]['last_activity']) {
                    $user_activities[$key]['last_activity'] = $user['last_activity'];
                }
            }
        }
    }

    // Sort and limit
    $active_users = array_values($user_activities);
    usort($active_users, function($a, $b) {
        return $b['activity_count'] - $a['activity_count'];
    });
    $active_users = array_slice($active_users, 0, 10);

} catch (PDOException $e) {
    $active_users = [];
}

// Page title
$page_title = 'نشاط المستخدمين';

// Include header
include_once '../includes/header_inner.php';
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-history me-2"></i> نشاط المستخدمين</h1>
        <div>
            <div class="btn-group me-2" role="group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-database me-1"></i> البيانات الحقيقية
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="sample_real_data.php">
                        <i class="fas fa-eye me-1"></i> عينة سريعة
                    </a></li>
                    <li><a class="dropdown-item" href="show_real_data.php" target="_blank">
                        <i class="fas fa-table me-1"></i> عرض كامل
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="export_real_data.php" target="_blank">
                        <i class="fas fa-download me-1"></i> تصدير JSON
                    </a></li>
                    <li><a class="dropdown-item" href="export_real_data.php?format=csv" target="_blank">
                        <i class="fas fa-file-csv me-1"></i> تصدير CSV
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="export_real_data.php?download=1" target="_blank">
                        <i class="fas fa-file-download me-1"></i> تحميل JSON
                    </a></li>
                </ul>
            </div>
            <a href="generate_activity_data.php" class="btn btn-success me-2">
                <i class="fas fa-plus me-1"></i> إنشاء أنشطة تجريبية
            </a>
            <a href="system_logs.php" class="btn btn-info me-2">
                <i class="fas fa-file-alt me-1"></i> سجلات النظام
            </a>
            <a href="system_owner_dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo number_format($total_activities); ?></h4>
                            <p class="mb-0">إجمالي الأنشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($active_users); ?></h4>
                            <p class="mb-0">المستخدمين النشطين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($activity_stats); ?></h4>
                            <p class="mb-0">أنواع الأنشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tasks fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $days_filter; ?></h4>
                            <p class="mb-0">أيام المراقبة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>تصفية الأنشطة</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="user_id" class="form-label">المستخدم</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">جميع المستخدمين</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['user_id']; ?>" <?php echo $user_filter == $user['user_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?> (<?php echo htmlspecialchars($user['username']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="activity" class="form-label">نوع النشاط</label>
                            <select class="form-select" id="activity" name="activity">
                                <option value="">جميع الأنشطة</option>
                                <option value="تسجيل مستخدم جديد" <?php echo $activity_filter === 'تسجيل مستخدم جديد' ? 'selected' : ''; ?>>تسجيل مستخدم جديد</option>
                                <option value="تسجيل في الحلقات" <?php echo $activity_filter === 'تسجيل في الحلقات' ? 'selected' : ''; ?>>تسجيل في الحلقات</option>
                                <?php if (in_array('announcements', $existing_tables)): ?>
                                <option value="إنشاء إعلان" <?php echo $activity_filter === 'إنشاء إعلان' ? 'selected' : ''; ?>>إنشاء إعلان</option>
                                <?php endif; ?>
                                <?php if (in_array('whatsapp_logs', $existing_tables)): ?>
                                <option value="رسائل الواتساب" <?php echo $activity_filter === 'رسائل الواتساب' ? 'selected' : ''; ?>>رسائل الواتساب</option>
                                <?php endif; ?>
                                <?php if (in_array('attendance_records', $existing_tables)): ?>
                                <option value="تسجيل الحضور" <?php echo $activity_filter === 'تسجيل الحضور' ? 'selected' : ''; ?>>تسجيل الحضور</option>
                                <?php endif; ?>
                                <?php if (in_array('memorization_progress', $existing_tables)): ?>
                                <option value="تقييم الحفظ" <?php echo $activity_filter === 'تقييم الحفظ' ? 'selected' : ''; ?>>تقييم الحفظ</option>
                                <?php endif; ?>
                                <?php if (in_array('system_logs', $existing_tables)): ?>
                                <option value="تسجيل دخول/خروج" <?php echo $activity_filter === 'تسجيل دخول/خروج' ? 'selected' : ''; ?>>تسجيل دخول/خروج</option>
                                <option value="إدارة المستخدمين" <?php echo $activity_filter === 'إدارة المستخدمين' ? 'selected' : ''; ?>>إدارة المستخدمين</option>
                                <option value="تغيير الإعدادات" <?php echo $activity_filter === 'تغيير الإعدادات' ? 'selected' : ''; ?>>تغيير الإعدادات</option>
                                <option value="أنشطة النظام" <?php echo $activity_filter === 'أنشطة النظام' ? 'selected' : ''; ?>>أنشطة النظام</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="days" class="form-label">آخر (أيام)</label>
                            <select class="form-select" id="days" name="days">
                                <option value="1" <?php echo $days_filter === 1 ? 'selected' : ''; ?>>يوم واحد</option>
                                <option value="7" <?php echo $days_filter === 7 ? 'selected' : ''; ?>>7 أيام</option>
                                <option value="30" <?php echo $days_filter === 30 ? 'selected' : ''; ?>>30 يوم</option>
                                <option value="90" <?php echo $days_filter === 90 ? 'selected' : ''; ?>>90 يوم</option>
                                <option value="365" <?php echo $days_filter === 365 ? 'selected' : ''; ?>>سنة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>تصفية
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات الأنشطة</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($activity_stats)): ?>
                        <?php foreach ($activity_stats as $stat): ?>
                            <?php
                            $category_names = [
                                'USER_REGISTRATION' => 'تسجيل مستخدم جديد',
                                'ENROLLMENT' => 'تسجيل في الحلقات',
                                'ANNOUNCEMENT' => 'إنشاء إعلان',
                                'WHATSAPP' => 'رسائل الواتساب',
                                'ATTENDANCE' => 'تسجيل الحضور',
                                'MEMORIZATION' => 'تقييم الحفظ',
                                'AUTH' => 'تسجيل دخول/خروج',
                                'USER' => 'إدارة المستخدمين',
                                'CONFIG' => 'تغيير الإعدادات',
                                'SYSTEM' => 'أنشطة النظام',
                                'SECURITY' => 'أنشطة أمنية'
                            ];
                            $category_name = $category_names[$stat['log_category']] ?? $stat['log_category'];
                            ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted"><?php echo $category_name; ?></small>
                                <div>
                                    <span class="badge bg-primary"><?php echo number_format($stat['count']); ?></span>
                                    <small class="text-muted">أنشطة</small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted mb-0">لا توجد إحصائيات متاحة</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Timeline -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-timeline me-2"></i>سجل الأنشطة
                <span class="badge bg-secondary"><?php echo number_format($total_activities); ?></span>
            </h5>
            <div>
                عرض <?php echo number_format($offset + 1); ?> - <?php echo number_format(min($offset + $per_page, $total_activities)); ?>
                من <?php echo number_format($total_activities); ?>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($activities)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أنشطة</h5>
                    <p class="text-muted">لم يتم العثور على أنشطة تطابق المعايير المحددة</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>المستخدم</th>
                                <th>نوع النشاط</th>
                                <th>الوصف</th>
                                <th>عنوان IP</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($activities as $activity): ?>
                                <?php
                                $severity_class = '';
                                $severity_icon = '';
                                switch ($activity['severity']) {
                                    case 'ERROR':
                                        $severity_class = 'text-danger';
                                        $severity_icon = 'fas fa-times-circle';
                                        break;
                                    case 'WARNING':
                                        $severity_class = 'text-warning';
                                        $severity_icon = 'fas fa-exclamation-triangle';
                                        break;
                                    case 'INFO':
                                        $severity_class = 'text-success';
                                        $severity_icon = 'fas fa-check-circle';
                                        break;
                                    default:
                                        $severity_class = 'text-info';
                                        $severity_icon = 'fas fa-info-circle';
                                }

                                $activity_icon = '';
                                $activity_color = '';
                                switch ($activity['category']) {
                                    case 'USER_REGISTRATION':
                                        $activity_icon = 'fas fa-user-plus';
                                        $activity_color = 'text-success';
                                        break;
                                    case 'ENROLLMENT':
                                        $activity_icon = 'fas fa-graduation-cap';
                                        $activity_color = 'text-info';
                                        break;
                                    case 'ANNOUNCEMENT':
                                        $activity_icon = 'fas fa-bullhorn';
                                        $activity_color = 'text-warning';
                                        break;
                                    case 'WHATSAPP':
                                        $activity_icon = 'fab fa-whatsapp';
                                        $activity_color = 'text-success';
                                        break;
                                    case 'ATTENDANCE':
                                        $activity_icon = 'fas fa-calendar-check';
                                        $activity_color = 'text-primary';
                                        break;
                                    case 'MEMORIZATION':
                                        $activity_icon = 'fas fa-book-open';
                                        $activity_color = 'text-success';
                                        break;
                                    case 'AUTH':
                                        $activity_icon = 'fas fa-sign-in-alt';
                                        $activity_color = 'text-primary';
                                        break;
                                    case 'USER':
                                        $activity_icon = 'fas fa-users';
                                        $activity_color = 'text-info';
                                        break;
                                    case 'CONFIG':
                                        $activity_icon = 'fas fa-cogs';
                                        $activity_color = 'text-warning';
                                        break;
                                    case 'SYSTEM':
                                        $activity_icon = 'fas fa-server';
                                        $activity_color = 'text-secondary';
                                        break;
                                    default:
                                        $activity_icon = 'fas fa-bell';
                                        $activity_color = 'text-secondary';
                                }
                                ?>
                                <tr>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i:s', strtotime($activity['timestamp'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($activity['user_name']): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($activity['user_name']); ?></div>
                                                    <small class="text-muted">ID: <?php echo $activity['user_id']; ?></small>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">النظام</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="<?php echo $activity_color; ?>">
                                            <i class="<?php echo $activity_icon; ?> me-1"></i>
                                            <?php echo $activity['activity_type']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="activity-description" style="max-width: 400px;">
                                            <?php echo htmlspecialchars($activity['description']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted font-monospace">
                                            <?php echo $activity['ip_address'] ?? 'غير محدد'; ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="<?php echo $severity_class; ?>">
                                            <i class="<?php echo $severity_icon; ?> me-1"></i>
                                            <?php echo $activity['severity']; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($total_pages > 1): ?>
            <div class="card-footer">
                <nav aria-label="تصفح الأنشطة">
                    <ul class="pagination pagination-sm justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                    السابق
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);
                        ?>

                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                    التالي
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>

    <!-- Most Active Users -->
    <?php if (!empty($active_users)): ?>
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-trophy me-2"></i>أكثر المستخدمين نشاطاً</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach (array_slice($active_users, 0, 6) as $index => $user): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <div class="me-3">
                                            <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center">
                                                <span class="fw-bold"><?php echo $index + 1; ?></span>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h6>
                                            <p class="text-muted mb-1 small">@<?php echo htmlspecialchars($user['username']); ?></p>
                                            <div class="d-flex justify-content-between">
                                                <span class="badge bg-success"><?php echo number_format($user['activity_count']); ?> نشاط</span>
                                                <small class="text-muted">
                                                    آخر نشاط: <?php echo date('M d', strtotime($user['last_activity'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-lg {
    width: 48px;
    height: 48px;
}

.activity-description {
    word-break: break-word;
    line-height: 1.4;
}

.table td {
    vertical-align: middle;
}

.font-monospace {
    font-family: 'Courier New', monospace;
}
</style>

<?php
// Include footer
require_once '../includes/footer.php';
?>
