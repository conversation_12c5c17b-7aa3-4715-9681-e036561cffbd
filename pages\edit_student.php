<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Check if student ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف الطالب غير صحيح');
    redirect('pages/students.php');
}

$student_id = (int)$_GET['id'];
$error = '';
$success = '';

// Get student information
try {
    $stmt = $pdo->prepare("
        SELECT u.*, c.center_name, r.role_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ? AND r.role_name = 'student'
    ");
    $stmt->execute([$student_id]);
    $student = $stmt->fetch();

    if (!$student) {
        set_flash_message('danger', 'الطالب غير موجود');
        redirect('pages/students.php');
    }

    // Check if current user has access to this student
    if (has_role('center_admin') && $student['center_id'] != $_SESSION['center_id']) {
        set_flash_message('danger', 'غير مصرح لك بالوصول إلى بيانات هذا الطالب');
        redirect('pages/students.php');
    }

    // Get current circle enrollment
    $stmt = $pdo->prepare("
        SELECT sce.enrollment_id, sce.circle_id, sce.status, c.circle_name, c.level,
               t.full_name AS teacher_name
        FROM student_circle_enrollments sce
        JOIN circles c ON sce.circle_id = c.circle_id
        JOIN users t ON c.teacher_user_id = t.user_id
        WHERE sce.student_user_id = ? AND sce.status = 'approved'
        ORDER BY sce.enrollment_date DESC
        LIMIT 1
    ");
    $stmt->execute([$student_id]);
    $current_enrollment = $stmt->fetch();

    // Get parent relations
    $stmt = $pdo->prepare("
        SELECT psr.relation_id, psr.parent_user_id, psr.relation_type, psr.is_primary,
               p.full_name AS parent_name, p.email AS parent_email, p.phone_number AS parent_phone
        FROM parent_student_relations psr
        JOIN users p ON psr.parent_user_id = p.user_id
        WHERE psr.student_user_id = ?
        ORDER BY psr.is_primary DESC, p.full_name
    ");
    $stmt->execute([$student_id]);
    $parent_relations = $stmt->fetchAll();


    // Check if birth_date column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'birth_date'
    ");
    $stmt->execute();
    $birth_date_exists = (bool)$stmt->fetchColumn();

    // Check if gender column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'gender'
    ");
    $stmt->execute();
    $gender_exists = (bool)$stmt->fetchColumn();

    // Check if address column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME = 'address'
    ");
    $stmt->execute();
    $address_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطالب: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $selected_center_id = has_role('center_admin') ? $_SESSION['center_id'] : (int)$_POST['center_id'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Circle enrollment
    $circle_id = isset($_POST['circle_id']) ? (int)$_POST['circle_id'] : null;

    // Parent relations
    $parent_ids = isset($_POST['parent_ids']) ? $_POST['parent_ids'] : [];
    $relation_types = isset($_POST['relation_types']) ? $_POST['relation_types'] : [];
    $primary_parent_id = isset($_POST['primary_parent_id']) ? (int)$_POST['primary_parent_id'] : null;
    $new_parent_id = isset($_POST['new_parent_id']) ? (int)$_POST['new_parent_id'] : null;
    $new_relation_type = isset($_POST['new_relation_type']) ? sanitize_input($_POST['new_relation_type']) : 'أب';

    // Optional fields based on schema
    $birth_date = $birth_date_exists ? sanitize_input($_POST['birth_date']) : null;
    $gender = $gender_exists ? sanitize_input($_POST['gender']) : null;
    $address = $address_exists ? sanitize_input($_POST['address']) : null;

    // New password (optional)
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate required fields
    if (empty($full_name)) {
        $error = 'يرجى إدخال الاسم الكامل';
    } elseif (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (has_role('system_owner') && empty($selected_center_id)) {
        $error = 'يرجى اختيار المركز';
    } elseif (!empty($new_password) && $new_password !== $confirm_password) {
        $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
    } else {
        try {
            // Check if email already exists for another user
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
            $stmt->execute([$email, $student_id]);
            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد آخر';
            } else {
                // Begin transaction
                $pdo->beginTransaction();

                // Build update query based on existing columns
                $query = "UPDATE users SET full_name = ?, email = ?, phone_number = ?, center_id = ?, is_active = ?";
                $params = [$full_name, $email, $phone_number, $selected_center_id, $is_active];

                if ($birth_date_exists) {
                    $query .= ", birth_date = ?";
                    $params[] = $birth_date;
                }

                if ($gender_exists) {
                    $query .= ", gender = ?";
                    $params[] = $gender;
                }

                if ($address_exists) {
                    $query .= ", address = ?";
                    $params[] = $address;
                }

                $query .= " WHERE user_id = ?";
                $params[] = $student_id;

                // Update student information
                $stmt = $pdo->prepare($query);
                $stmt->execute($params);

                // Update password if provided
                if (!empty($new_password)) {
                    $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE user_id = ?");
                    $stmt->execute([$password_hash, $student_id]);
                }

                // Handle profile picture upload if provided
                if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/images/profiles/';

                    // Create directory if it doesn't exist
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }

                    $file_extension = pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION);
                    $new_filename = 'user_' . $student_id . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_filename;

                    if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                        // Update user with profile picture URL
                        $profile_picture_url = 'assets/images/profiles/' . $new_filename;
                        $stmt = $pdo->prepare("UPDATE users SET profile_picture_url = ? WHERE user_id = ?");
                        $stmt->execute([$profile_picture_url, $student_id]);
                    }
                }

                // Handle circle enrollment change
                if ($circle_id) {
                    // Check if student is already enrolled in this circle
                    $stmt = $pdo->prepare("
                        SELECT enrollment_id FROM student_circle_enrollments
                        WHERE student_user_id = ? AND circle_id = ? AND status = 'approved'
                    ");
                    $stmt->execute([$student_id, $circle_id]);
                    $existing_enrollment = $stmt->fetch();

                    if (!$existing_enrollment) {
                        // Set all current enrollments to 'withdrawn'
                        $stmt = $pdo->prepare("
                            UPDATE student_circle_enrollments
                            SET status = 'withdrawn'
                            WHERE student_user_id = ? AND status = 'approved'
                        ");
                        $stmt->execute([$student_id]);

                        // Create new enrollment
                        $stmt = $pdo->prepare("
                            INSERT INTO student_circle_enrollments
                            (student_user_id, circle_id, enrollment_date, status)
                            VALUES (?, ?, CURDATE(), 'approved')
                        ");
                        $stmt->execute([$student_id, $circle_id]);
                    }
                }

                // Handle parent relations
                if (!empty($parent_ids)) {
                    // Update existing relations
                    foreach ($parent_ids as $index => $parent_id) {
                        $is_primary = ($parent_id == $primary_parent_id) ? 1 : 0;
                        $relation_type = $relation_types[$index] ?? 'أب';

                        $stmt = $pdo->prepare("
                            UPDATE parent_student_relations
                            SET relation_type = ?, is_primary = ?
                            WHERE parent_user_id = ? AND student_user_id = ?
                        ");
                        $stmt->execute([$relation_type, $is_primary, $parent_id, $student_id]);
                    }
                }

                // Add new parent relation if selected
                if ($new_parent_id) {
                    // Check if relation already exists
                    $stmt = $pdo->prepare("
                        SELECT relation_id FROM parent_student_relations
                        WHERE parent_user_id = ? AND student_user_id = ?
                    ");
                    $stmt->execute([$new_parent_id, $student_id]);

                    if (!$stmt->fetch()) {
                        // Set is_primary to 1 if this is the first parent
                        $stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM parent_student_relations
                            WHERE student_user_id = ?
                        ");
                        $stmt->execute([$student_id]);
                        $is_primary = ($stmt->fetchColumn() == 0) ? 1 : 0;

                        // Create new relation
                        $stmt = $pdo->prepare("
                            INSERT INTO parent_student_relations
                            (parent_user_id, student_user_id, relation_type, is_primary)
                            VALUES (?, ?, ?, ?)
                        ");
                        $stmt->execute([$new_parent_id, $student_id, $new_relation_type, $is_primary]);
                    }
                }

                // Commit transaction
                $pdo->commit();

                $success = 'تم تحديث بيانات الطالب بنجاح';

                // Refresh student data
                $stmt = $pdo->prepare("
                    SELECT u.*, c.center_name, r.role_name
                    FROM users u
                    JOIN roles r ON u.role_id = r.role_id
                    LEFT JOIN centers c ON u.center_id = c.center_id
                    WHERE u.user_id = ? AND r.role_name = 'student'
                ");
                $stmt->execute([$student_id]);
                $student = $stmt->fetch();

                // Refresh circle enrollment
                $stmt = $pdo->prepare("
                    SELECT sce.enrollment_id, sce.circle_id, sce.status, c.circle_name, c.level,
                           t.full_name AS teacher_name
                    FROM student_circle_enrollments sce
                    JOIN circles c ON sce.circle_id = c.circle_id
                    JOIN users t ON c.teacher_user_id = t.user_id
                    WHERE sce.student_user_id = ? AND sce.status = 'approved'
                    ORDER BY sce.enrollment_date DESC
                    LIMIT 1
                ");
                $stmt->execute([$student_id]);
                $current_enrollment = $stmt->fetch();

                // Refresh parent relations
                $stmt = $pdo->prepare("
                    SELECT psr.relation_id, psr.parent_user_id, psr.relation_type, psr.is_primary,
                           p.full_name AS parent_name, p.email AS parent_email, p.phone_number AS parent_phone
                    FROM parent_student_relations psr
                    JOIN users p ON psr.parent_user_id = p.user_id
                    WHERE psr.student_user_id = ?
                    ORDER BY psr.is_primary DESC, p.full_name
                ");
                $stmt->execute([$student_id]);
                $parent_relations = $stmt->fetchAll();
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء تحديث بيانات الطالب: ' . $e->getMessage();
        }
    }
}

// Get available circles for the student's center
$center_id_for_circles = $student['center_id'];
$circles_stmt = $pdo->prepare("
    SELECT c.circle_id, c.circle_name, c.level, u.full_name AS teacher_name,
           (SELECT COUNT(*) FROM student_circle_enrollments sce WHERE sce.circle_id = c.circle_id AND sce.status = 'approved') AS current_students,
           c.max_students
    FROM circles c
    JOIN users u ON c.teacher_user_id = u.user_id
    WHERE c.center_id = ? AND c.is_active = TRUE
    ORDER BY c.circle_name
");
$circles_stmt->execute([$center_id_for_circles]);
$available_circles = $circles_stmt->fetchAll();

// Get available parents for the student's center
$center_id_for_parents = $student['center_id'];
$parents_stmt = $pdo->prepare("
    SELECT u.user_id, u.full_name, u.email, u.phone_number
    FROM users u
    JOIN roles r ON u.role_id = r.role_id
    WHERE r.role_name = 'parent' AND u.center_id = ? AND u.is_active = TRUE
    ORDER BY u.full_name
");
$parents_stmt->execute([$center_id_for_parents]);
$available_parents = $parents_stmt->fetchAll();

// Page variables
$page_title = 'تعديل بيانات الطالب: ' . $student['full_name'];
$active_page = 'students';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : 'center_admin_dashboard.php'],
    ['title' => 'الطلاب', 'url' => 'students.php'],
    ['title' => 'تعديل بيانات الطالب']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);

// Include page header template
include_template('page_header', [
    'page_title' => $page_title,
    'page_icon' => 'fas fa-user-graduate'
]);
?>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-edit me-2"></i> تعديل بيانات الطالب</h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" value="<?php echo $student['username']; ?>" readonly disabled>
                    <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required value="<?php echo $student['full_name']; ?>">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" required value="<?php echo $student['email']; ?>">
                </div>

                <div class="col-md-6 mb-3">
                    <label for="phone_number" class="form-label">رقم الهاتف</label>
                    <input type="text" class="form-control" id="phone_number" name="phone_number" value="<?php echo $student['phone_number']; ?>">
                </div>
            </div>

            <?php if ($birth_date_exists): ?>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date" value="<?php echo $student['birth_date']; ?>">
                </div>

                <?php if ($gender_exists): ?>
                <div class="col-md-6 mb-3">
                    <label for="gender" class="form-label">الجنس</label>
                    <select class="form-select" id="gender" name="gender">
                        <option value="">-- اختر الجنس --</option>
                        <option value="male" <?php echo $student['gender'] == 'male' ? 'selected' : ''; ?>>ذكر</option>
                        <option value="female" <?php echo $student['gender'] == 'female' ? 'selected' : ''; ?>>أنثى</option>
                    </select>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <?php if ($address_exists): ?>
            <div class="row">
                <div class="col-md-12 mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo $student['address']; ?></textarea>
                </div>
            </div>
            <?php endif; ?>

            <?php if (has_role('system_owner')): ?>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="center_id" class="form-label">المركز <span class="text-danger">*</span></label>
                    <select class="form-select" id="center_id" name="center_id" required>
                        <option value="">-- اختر المركز --</option>
                        <?php
                        // Get centers
                        $centers_stmt = $pdo->query("SELECT center_id, center_name FROM centers ORDER BY center_name");
                        $centers = $centers_stmt->fetchAll();

                        foreach ($centers as $center):
                        ?>
                            <option value="<?php echo $center['center_id']; ?>" <?php echo $student['center_id'] == $center['center_id'] ? 'selected' : ''; ?>>
                                <?php echo $center['center_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="new_password" name="new_password">
                    <div class="form-text">اتركها فارغة إذا كنت لا ترغب في تغيير كلمة المرور</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                </div>
            </div>

            <!-- Circle Assignment Section -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-circle me-2"></i> تعيين الحلقة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <?php if ($current_enrollment): ?>
                                <div class="alert alert-info">
                                    <strong>الحلقة الحالية:</strong> <?php echo $current_enrollment['circle_name']; ?> (المستوى: <?php echo $current_enrollment['level']; ?>, المعلم: <?php echo $current_enrollment['teacher_name']; ?>)
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <strong>تنبيه:</strong> الطالب غير مسجل في أي حلقة حالياً.
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="circle_id" class="form-label">تغيير الحلقة</label>
                            <select class="form-select" id="circle_id" name="circle_id">
                                <option value="">-- اختر الحلقة --</option>
                                <?php foreach ($available_circles as $circle):
                                    $is_full = ($circle['current_students'] >= $circle['max_students']);
                                    $is_current = $current_enrollment && $current_enrollment['circle_id'] == $circle['circle_id'];
                                ?>
                                    <option value="<?php echo $circle['circle_id']; ?>"
                                            <?php echo $is_current ? 'selected' : ''; ?>
                                            <?php echo (!$is_current && $is_full) ? 'disabled' : ''; ?>>
                                        <?php echo $circle['circle_name']; ?> -
                                        المستوى: <?php echo $circle['level']; ?> -
                                        المعلم: <?php echo $circle['teacher_name']; ?> -
                                        الطلاب: <?php echo $circle['current_students']; ?>/<?php echo $circle['max_students']; ?>
                                        <?php echo $is_full ? ' (ممتلئة)' : ''; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">اختر الحلقة التي تريد نقل الطالب إليها. الحلقات الممتلئة غير متاحة للاختيار.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Parent Relations Section -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0"><i class="fas fa-user-friends me-2"></i> علاقات ولي الأمر</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($parent_relations)): ?>
                        <div class="alert alert-warning">
                            <strong>تنبيه:</strong> الطالب ليس لديه أولياء أمور مرتبطين به.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive mb-3">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>ولي الأمر</th>
                                        <th>نوع العلاقة</th>
                                        <th>ولي الأمر الرئيسي</th>
                                        <th>معلومات الاتصال</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($parent_relations as $index => $relation): ?>
                                        <tr>
                                            <td>
                                                <input type="hidden" name="parent_ids[]" value="<?php echo $relation['parent_user_id']; ?>">
                                                <?php echo $relation['parent_name']; ?>
                                            </td>
                                            <td>
                                                <select class="form-select" name="relation_types[]">
                                                    <option value="أب" <?php echo $relation['relation_type'] == 'أب' ? 'selected' : ''; ?>>أب</option>
                                                    <option value="أم" <?php echo $relation['relation_type'] == 'أم' ? 'selected' : ''; ?>>أم</option>
                                                    <option value="جد" <?php echo $relation['relation_type'] == 'جد' ? 'selected' : ''; ?>>جد</option>
                                                    <option value="جدة" <?php echo $relation['relation_type'] == 'جدة' ? 'selected' : ''; ?>>جدة</option>
                                                    <option value="أخ" <?php echo $relation['relation_type'] == 'أخ' ? 'selected' : ''; ?>>أخ</option>
                                                    <option value="أخت" <?php echo $relation['relation_type'] == 'أخت' ? 'selected' : ''; ?>>أخت</option>
                                                    <option value="عم" <?php echo $relation['relation_type'] == 'عم' ? 'selected' : ''; ?>>عم</option>
                                                    <option value="عمة" <?php echo $relation['relation_type'] == 'عمة' ? 'selected' : ''; ?>>عمة</option>
                                                    <option value="خال" <?php echo $relation['relation_type'] == 'خال' ? 'selected' : ''; ?>>خال</option>
                                                    <option value="خالة" <?php echo $relation['relation_type'] == 'خالة' ? 'selected' : ''; ?>>خالة</option>
                                                    <option value="وصي" <?php echo $relation['relation_type'] == 'وصي' ? 'selected' : ''; ?>>وصي</option>
                                                    <option value="أخرى" <?php echo $relation['relation_type'] == 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                                                </select>
                                            </td>
                                            <td class="text-center">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="primary_parent_id" value="<?php echo $relation['parent_user_id']; ?>" <?php echo $relation['is_primary'] ? 'checked' : ''; ?>>
                                                </div>
                                            </td>
                                            <td>
                                                <small>
                                                    <i class="fas fa-envelope me-1"></i> <?php echo $relation['parent_email']; ?><br>
                                                    <i class="fas fa-phone me-1"></i> <?php echo $relation['parent_phone']; ?>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="new_parent_id" class="form-label">إضافة ولي أمر جديد</label>
                            <select class="form-select" id="new_parent_id" name="new_parent_id">
                                <option value="">-- اختر ولي أمر --</option>
                                <?php
                                // Filter out parents that are already related to this student
                                $existing_parent_ids = array_column($parent_relations, 'parent_user_id');

                                foreach ($available_parents as $parent):
                                    if (!in_array($parent['user_id'], $existing_parent_ids)):
                                ?>
                                    <option value="<?php echo $parent['user_id']; ?>">
                                        <?php echo $parent['full_name']; ?> -
                                        <?php echo $parent['email']; ?> -
                                        <?php echo $parent['phone_number']; ?>
                                    </option>
                                <?php
                                    endif;
                                endforeach;
                                ?>
                            </select>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="new_relation_type" class="form-label">نوع العلاقة</label>
                            <select class="form-select" id="new_relation_type" name="new_relation_type">
                                <option value="أب">أب</option>
                                <option value="أم">أم</option>
                                <option value="جد">جد</option>
                                <option value="جدة">جدة</option>
                                <option value="أخ">أخ</option>
                                <option value="أخت">أخت</option>
                                <option value="عم">عم</option>
                                <option value="عمة">عمة</option>
                                <option value="خال">خال</option>
                                <option value="خالة">خالة</option>
                                <option value="وصي">وصي</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                    <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                </div>

                <div class="col-md-6 mb-3">
                    <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $student['is_active'] ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">
                            حساب نشط
                        </label>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="students.php" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>

<?php if (!empty($student['profile_picture_url'])): ?>
<div class="card shadow mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="card-title mb-0"><i class="fas fa-image me-2"></i> الصورة الحالية</h5>
    </div>
    <div class="card-body text-center">
        <img src="<?php echo get_root_url() . $student['profile_picture_url']; ?>" alt="صورة الطالب" class="img-fluid rounded" style="max-height: 300px;">
    </div>
</div>
<?php endif; ?>

<?php
// Include footer template
include_template('footer');
?>
