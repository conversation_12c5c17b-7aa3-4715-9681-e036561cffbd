<?php
// Include common functions and definitions
require_once 'includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
    exit;
}

$success = '';
$error = '';

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Check if announcement_reads table exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'announcement_reads'
    ");
    $stmt->execute();
    $table_exists = (bool)$stmt->fetchColumn();
    
    if (!$table_exists) {
        // Create the announcement_reads table with ON DELETE CASCADE
        $pdo->exec("
            CREATE TABLE announcement_reads (
                read_id INT AUTO_INCREMENT PRIMARY KEY,
                announcement_id INT NOT NULL,
                user_id INT NOT NULL,
                read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                UNIQUE KEY (announcement_id, user_id)
            )
        ");
        
        $success = 'تم إنشاء جدول قراءات الإعلانات بنجاح مع إضافة قيود الحذف المتتابع.';
    } else {
        // Drop existing foreign key constraints
        $stmt = $pdo->prepare("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'announcement_reads'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $stmt->execute();
        $constraints = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($constraints as $constraint) {
            $pdo->exec("ALTER TABLE announcement_reads DROP FOREIGN KEY `$constraint`");
        }
        
        // Add new foreign key constraints with ON DELETE CASCADE
        $pdo->exec("
            ALTER TABLE announcement_reads
            ADD CONSTRAINT fk_announcement_reads_announcement_id
            FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id) ON DELETE CASCADE
        ");
        
        $pdo->exec("
            ALTER TABLE announcement_reads
            ADD CONSTRAINT fk_announcement_reads_user_id
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        ");
        
        $success = 'تم تحديث قيود جدول قراءات الإعلانات بنجاح لتفعيل الحذف المتتابع.';
    }
    
    // Commit transaction
    $pdo->commit();
    
    // Set flash message
    set_flash_message('success', $success);
    
} catch (PDOException $e) {
    // Rollback transaction
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $error = 'حدث خطأ أثناء تحديث قيود جدول قراءات الإعلانات: ' . $e->getMessage();
    set_flash_message('danger', $error);
}

// Redirect to announcements page
redirect('pages/announcements.php');
?>
