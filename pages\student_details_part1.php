<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('../auth/login.php');
}

// Get user role
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$error = '';
$success = '';

// Check if student ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    set_flash_message('danger', 'معرف الطالب غير صالح');
    redirect('parent_dashboard.php');
}

$student_id = (int)$_GET['id'];

// Check if user has permission to view this student's details
$has_permission = false;

if ($role_name === 'system_owner' || $role_name === 'center_admin') {
    $has_permission = true;
} elseif ($role_name === 'teacher') {
    // Check if the student is in one of the teacher's circles
    try {
        $stmt = $pdo->prepare("
            SELECT 1
            FROM student_circle_enrollments sce
            JOIN circles c ON sce.circle_id = c.circle_id
            WHERE sce.student_user_id = ? AND c.teacher_user_id = ?
            LIMIT 1
        ");
        $stmt->execute([$student_id, $user_id]);
        $has_permission = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
    }
} elseif ($role_name === 'parent') {
    // Check if the student is a child of this parent
    try {
        $stmt = $pdo->prepare("
            SELECT 1
            FROM Parent_Student_Relations
            WHERE parent_user_id = ? AND student_user_id = ?
            LIMIT 1
        ");
        $stmt->execute([$user_id, $student_id]);
        $has_permission = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
    }
} elseif ($role_name === 'student') {
    // Students can only view their own details
    $has_permission = ($student_id === $user_id);
}

if (!$has_permission) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى بيانات هذا الطالب');
    redirect('parent_dashboard.php');
}

// Get student information
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.username, u.full_name, u.email, u.phone_number, u.profile_picture_url,
               c.center_name
        FROM users u
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ? AND u.role_id = (SELECT role_id FROM roles WHERE role_name = 'student')
    ");
    $stmt->execute([$student_id]);
    $student = $stmt->fetch();
    
    if (!$student) {
        set_flash_message('danger', 'الطالب غير موجود');
        redirect('parent_dashboard.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطالب: ' . $e->getMessage();
}

// Get student's circle information
try {
    $stmt = $pdo->prepare("
        SELECT c.circle_id, c.circle_name, c.level, c.schedule_details,
               t.user_id AS teacher_id, t.full_name AS teacher_name,
               ce.center_name, sce.enrollment_id, sce.enrollment_date
        FROM student_circle_enrollments sce
        JOIN circles c ON sce.circle_id = c.circle_id
        JOIN users t ON c.teacher_user_id = t.user_id
        JOIN centers ce ON c.center_id = ce.center_id
        WHERE sce.student_user_id = ? AND sce.status = 'approved'
    ");
    $stmt->execute([$student_id]);
    $circle = $stmt->fetch();
    
    if ($circle) {
        $enrollment_id = $circle['enrollment_id'];
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
}

// Get student's parents
try {
    $stmt = $pdo->prepare("
        SELECT psr.relation_id, psr.relation_type, psr.is_primary,
               u.user_id, u.full_name, u.email, u.phone_number
        FROM Parent_Student_Relations psr
        JOIN users u ON psr.parent_user_id = u.user_id
        WHERE psr.student_user_id = ?
        ORDER BY psr.is_primary DESC, u.full_name
    ");
    $stmt->execute([$student_id]);
    $parents = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات أولياء الأمور: ' . $e->getMessage();
}

// Get student's memorization progress
if (isset($enrollment_id)) {
    try {
        // Get recent memorization progress
        $stmt = $pdo->prepare("
            SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to, 
                   mp.recitation_date, mp.memorization_quality, mp.tajweed_application, mp.fluency,
                   mp.teacher_notes, u.full_name AS teacher_name
            FROM memorization_progress mp
            JOIN users u ON mp.recorded_by_user_id = u.user_id
            WHERE mp.enrollment_id = ?
            ORDER BY mp.recitation_date DESC
            LIMIT 10
        ");
        $stmt->execute([$enrollment_id]);
        $memorization_progress = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات التقدم: ' . $e->getMessage();
    }
    
    // Get attendance records
    try {
        $stmt = $pdo->prepare("
            SELECT ar.attendance_id, ar.session_date, ar.status, ar.notes
            FROM attendance_records ar
            WHERE ar.enrollment_id = ?
            ORDER BY ar.session_date DESC
            LIMIT 10
        ");
        $stmt->execute([$enrollment_id]);
        $attendance_records = $stmt->fetchAll();
        
        // Calculate attendance statistics
        $stmt = $pdo->prepare("
            SELECT COUNT(*) AS total_sessions,
                   SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) AS present_count,
                   SUM(CASE WHEN status = 'absent_excused' THEN 1 ELSE 0 END) AS excused_count,
                   SUM(CASE WHEN status = 'absent_unexcused' THEN 1 ELSE 0 END) AS unexcused_count,
                   SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) AS late_count
            FROM attendance_records
            WHERE enrollment_id = ?
        ");
        $stmt->execute([$enrollment_id]);
        $attendance_stats = $stmt->fetch();
        
        // Calculate attendance percentage
        if ($attendance_stats['total_sessions'] > 0) {
            $attendance_percentage = round(($attendance_stats['present_count'] / $attendance_stats['total_sessions']) * 100);
        } else {
            $attendance_percentage = 0;
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الحضور: ' . $e->getMessage();
    }
    
    // Get current memorization schedule
    try {
        $stmt = $pdo->prepare("
            SELECT schedule_id, target_surah_start, target_ayah_start, 
                   target_surah_end, target_ayah_end, 
                   target_juz, target_page_start, target_page_end,
                   type, assigned_date, due_date, is_completed
            FROM memorization_schedules
            WHERE enrollment_id = ? AND is_completed = FALSE
            ORDER BY due_date ASC
            LIMIT 1
        ");
        $stmt->execute([$enrollment_id]);
        $current_schedule = $stmt->fetch();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات خطة الحفظ: ' . $e->getMessage();
    }
    
    // Get assignments
    try {
        $stmt = $pdo->prepare("
            SELECT a.assignment_id, a.title, a.description, a.due_date,
                   sa.status, sa.submission_date, sa.grade, sa.feedback
            FROM assignments a
            JOIN student_assignments sa ON a.assignment_id = sa.assignment_id
            WHERE sa.student_user_id = ? AND a.circle_id = ?
            ORDER BY a.due_date DESC
            LIMIT 10
        ");
        $stmt->execute([$student_id, $circle['circle_id']]);
        $assignments = $stmt->fetchAll();
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء استرجاع بيانات الواجبات: ' . $e->getMessage();
    }
}
?>
