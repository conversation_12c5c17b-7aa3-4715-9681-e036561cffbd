<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in
if (!is_logged_in()) {
    set_flash_message('danger', 'يرجى تسجيل الدخول أولاً');
    redirect('auth/login.php');
}

// Check if circle ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    set_flash_message('danger', 'معرف الحلقة غير صالح');
    redirect('index.php');
}

$circle_id = (int)$_GET['id'];
$user_id = $_SESSION['user_id'];
$role_name = $_SESSION['role_name'];
$error = '';
$success = '';

// Check if user has access to this circle
$has_access = false;

if ($role_name === 'system_owner') {
    $has_access = true;
} elseif ($role_name === 'center_admin') {
    try {
        $stmt = $pdo->prepare("
            SELECT c.circle_id
            FROM circles c
            WHERE c.circle_id = ? AND c.center_id = ?
        ");
        $stmt->execute([$circle_id, $_SESSION['center_id']]);
        $has_access = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
    }
} elseif ($role_name === 'teacher') {
    try {
        $stmt = $pdo->prepare("
            SELECT c.circle_id
            FROM circles c
            WHERE c.circle_id = ? AND c.teacher_user_id = ?
        ");
        $stmt->execute([$circle_id, $user_id]);
        $has_access = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
    }
} elseif ($role_name === 'student') {
    try {
        $stmt = $pdo->prepare("
            SELECT sce.enrollment_id
            FROM student_circle_enrollments sce
            WHERE sce.circle_id = ? AND sce.student_user_id = ? AND sce.status = 'approved'
        ");
        $stmt->execute([$circle_id, $user_id]);
        $has_access = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
    }
} elseif ($role_name === 'parent') {
    try {
        $stmt = $pdo->prepare("
            SELECT sce.enrollment_id
            FROM student_circle_enrollments sce
            WHERE sce.circle_id = ? AND sce.parent_user_id = ? AND sce.status = 'approved'
        ");
        $stmt->execute([$circle_id, $user_id]);
        $has_access = $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        $error = 'حدث خطأ أثناء التحقق من الصلاحيات: ' . $e->getMessage();
    }
}

if (!$has_access && empty($error)) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الحلقة');
    redirect('index.php');
}

// Get circle information
try {
    $stmt = $pdo->prepare("
        SELECT c.*, ce.center_name, u.full_name AS teacher_name, u.email AS teacher_email, u.phone_number AS teacher_phone
        FROM circles c
        JOIN centers ce ON c.center_id = ce.center_id
        JOIN users u ON c.teacher_user_id = u.user_id
        WHERE c.circle_id = ?
    ");
    $stmt->execute([$circle_id]);
    $circle = $stmt->fetch();

    if (!$circle) {
        set_flash_message('danger', 'لم يتم العثور على الحلقة');
        redirect('index.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحلقة: ' . $e->getMessage();
}

// Get students in the circle
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name, u.profile_picture_url, sce.enrollment_date, sce.status,
               p.full_name AS parent_name, p.phone_number AS parent_phone
        FROM student_circle_enrollments sce
        JOIN users u ON sce.student_user_id = u.user_id
        LEFT JOIN users p ON sce.parent_user_id = p.user_id
        WHERE sce.circle_id = ?
        ORDER BY sce.status, u.full_name
    ");
    $stmt->execute([$circle_id]);
    $students = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الطلاب: ' . $e->getMessage();
}

// Get recent attendance records
try {
    $stmt = $pdo->prepare("
        SELECT DISTINCT ar.session_date, COUNT(ar.attendance_id) AS total_records,
               SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) AS present_count,
               SUM(CASE WHEN ar.status = 'absent_excused' THEN 1 ELSE 0 END) AS excused_count,
               SUM(CASE WHEN ar.status = 'absent_unexcused' THEN 1 ELSE 0 END) AS unexcused_count,
               SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) AS late_count
        FROM attendance_records ar
        JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
        WHERE sce.circle_id = ?
        GROUP BY ar.session_date
        ORDER BY ar.session_date DESC
        LIMIT 5
    ");
    $stmt->execute([$circle_id]);
    $attendance_records = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع سجلات الحضور: ' . $e->getMessage();
}

// Get recent memorization progress
try {
    $stmt = $pdo->prepare("
        SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to,
               mp.recitation_date, mp.memorization_quality,
               u.full_name AS student_name
        FROM memorization_progress mp
        JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
        JOIN users u ON sce.student_user_id = u.user_id
        WHERE sce.circle_id = ?
        ORDER BY mp.recitation_date DESC
        LIMIT 10
    ");
    $stmt->execute([$circle_id]);
    $recent_progress = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات التقدم: ' . $e->getMessage();
}

// Get recent assignments
try {
    $stmt = $pdo->prepare("
        SELECT a.assignment_id, a.title, a.due_date, a.created_at,
               COUNT(sa.student_assignment_id) AS submission_count
        FROM assignments a
        LEFT JOIN student_assignments sa ON a.assignment_id = sa.assignment_id AND sa.status IN ('submitted', 'graded')
        WHERE a.circle_id = ?
        GROUP BY a.assignment_id
        ORDER BY a.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$circle_id]);
    $recent_assignments = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الواجبات: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $circle['circle_name']; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <?php include_once '../includes/header.php'; ?>

    <div class="container py-5">
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <h1 class="mb-4"><?php echo $circle['circle_name']; ?></h1>

                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> معلومات الحلقة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <?php if (!empty($circle['description'])): ?>
                                    <p><?php echo $circle['description']; ?></p>
                                <?php endif; ?>

                                <p><strong>المركز:</strong> <?php echo $circle['center_name']; ?></p>
                                <p><strong>المستوى:</strong> <?php echo $circle['level']; ?></p>
                                <p><strong>المواعيد:</strong> <?php echo $circle['schedule_details']; ?></p>

                                <?php if (!empty($circle['max_students'])): ?>
                                    <p><strong>الحد الأقصى للطلاب:</strong> <?php echo $circle['max_students']; ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <?php if (!empty($circle['start_date'])): ?>
                                    <p><strong>تاريخ البداية:</strong> <?php echo date('Y-m-d', strtotime($circle['start_date'])); ?></p>
                                <?php endif; ?>

                                <?php if (!empty($circle['end_date'])): ?>
                                    <p><strong>تاريخ النهاية:</strong> <?php echo date('Y-m-d', strtotime($circle['end_date'])); ?></p>
                                <?php endif; ?>

                                <p>
                                    <strong>الحالة:</strong>
                                    <?php if ($circle['is_active']): ?>
                                        <span class="badge bg-success">نشطة</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشطة</span>
                                    <?php endif; ?>
                                </p>

                                <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d', strtotime($circle['created_at'])); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-user-tie me-2"></i> معلومات المعلم</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>الاسم:</strong> <?php echo $circle['teacher_name']; ?></p>
                                <p><strong>البريد الإلكتروني:</strong> <?php echo $circle['teacher_email']; ?></p>
                            </div>
                            <div class="col-md-6">
                                <?php if (!empty($circle['teacher_phone'])): ?>
                                    <p><strong>رقم الهاتف:</strong> <?php echo $circle['teacher_phone']; ?></p>
                                <?php endif; ?>

                                <p>
                                    <a href="teacher_contact.php?id=<?php echo $circle['teacher_user_id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-envelope me-1"></i> التواصل مع المعلم
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Attendance Records -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-calendar-check me-2"></i> آخر سجلات الحضور</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($attendance_records)): ?>
                            <p class="text-center">لا توجد سجلات حضور حالياً.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الحاضرين</th>
                                            <th>الغائبين بعذر</th>
                                            <th>الغائبين بدون عذر</th>
                                            <th>المتأخرين</th>
                                            <th>نسبة الحضور</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($attendance_records as $record): ?>
                                            <tr>
                                                <td><?php echo date('Y-m-d', strtotime($record['session_date'])); ?></td>
                                                <td>
                                                    <span class="attendance-present">
                                                        <?php echo $record['present_count']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="attendance-excused">
                                                        <?php echo $record['excused_count']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="attendance-absent">
                                                        <?php echo $record['unexcused_count']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="attendance-late">
                                                        <?php echo $record['late_count']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                    $total = $record['total_records'];
                                                    $present = $record['present_count'];
                                                    $late = $record['late_count'];

                                                    $attendance_rate = $total > 0 ? round((($present + $late) / $total) * 100) : 0;

                                                    echo '<div class="progress" style="height: 20px;">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: ' . $attendance_rate . '%;"
                                                                 aria-valuenow="' . $attendance_rate . '" aria-valuemin="0" aria-valuemax="100">
                                                                ' . $attendance_rate . '%
                                                            </div>
                                                          </div>';
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="attendance.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-sm btn-outline-info">عرض كل سجلات الحضور</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Memorization Progress -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0"><i class="fas fa-book-reader me-2"></i> آخر تقدم في الحفظ</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_progress)): ?>
                            <p class="text-center">لا يوجد تقدم مسجل حالياً.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>السورة</th>
                                            <th>الآيات</th>
                                            <th>التقييم</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_progress as $progress): ?>
                                            <tr>
                                                <td><?php echo $progress['student_name']; ?></td>
                                                <td><?php echo $progress['surah_name']; ?></td>
                                                <td><?php echo $progress['ayah_from'] . ' - ' . $progress['ayah_to']; ?></td>
                                                <td>
                                                    <?php
                                                    $quality = $progress['memorization_quality'];
                                                    $color = '';
                                                    switch ($quality) {
                                                        case 'excellent': $color = 'success'; break;
                                                        case 'very_good': $color = 'primary'; break;
                                                        case 'good': $color = 'info'; break;
                                                        case 'fair': $color = 'warning'; break;
                                                        case 'poor': $color = 'danger'; break;
                                                    }
                                                    echo '<span class="badge bg-' . $color . '">' . ucfirst(str_replace('_', ' ', $quality)) . '</span>';
                                                    ?>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($progress['recitation_date'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="memorization.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-sm btn-outline-warning">عرض كل التقدم</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Quick Actions -->
                <?php if ($role_name === 'teacher' || $role_name === 'center_admin'): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><i class="fas fa-bolt me-2"></i> إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="attendance.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-info">
                                    <i class="fas fa-calendar-check me-1"></i> تسجيل الحضور
                                </a>
                                <a href="memorization.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-success">
                                    <i class="fas fa-book-reader me-1"></i> تسجيل تسميع
                                </a>
                                <a href="assignments.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-warning">
                                    <i class="fas fa-tasks me-1"></i> إدارة الواجبات
                                </a>
                                <a href="circle_announcements.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-secondary">
                                    <i class="fas fa-bullhorn me-1"></i> إرسال إعلان
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Students List -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i> الطلاب (<?php echo count($students); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($students)): ?>
                            <p class="text-center">لا يوجد طلاب مسجلين في هذه الحلقة حالياً.</p>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($students as $student): ?>
                                    <div class="list-group-item">
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo !empty($student['profile_picture_url']) ? '../' . $student['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>"
                                                 class="rounded-circle me-3" width="40" height="40" alt="صورة الطالب">
                                            <div>
                                                <h6 class="mb-0"><?php echo $student['full_name']; ?></h6>
                                                <?php if ($student['status'] !== 'approved'): ?>
                                                    <span class="badge bg-warning"><?php echo $student['status']; ?></span>
                                                <?php endif; ?>
                                                <?php if (!empty($student['parent_name'])): ?>
                                                    <small class="text-muted d-block">ولي الأمر: <?php echo $student['parent_name']; ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php if ($role_name === 'teacher' || $role_name === 'center_admin'): ?>
                                            <div class="mt-2">
                                                <a href="student_details.php?id=<?php echo $student['user_id']; ?>" class="btn btn-sm btn-outline-primary">التفاصيل</a>
                                                <a href="memorization.php?student_id=<?php echo $student['user_id']; ?>&circle_id=<?php echo $circle_id; ?>" class="btn btn-sm btn-outline-success">تسميع</a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Assignments -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0"><i class="fas fa-tasks me-2"></i> آخر الواجبات</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_assignments)): ?>
                            <p class="text-center">لا توجد واجبات مسجلة حالياً.</p>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($recent_assignments as $assignment): ?>
                                    <a href="assignment_details.php?id=<?php echo $assignment['assignment_id']; ?>" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?php echo $assignment['title']; ?></h6>
                                            <small>
                                                <?php
                                                $due_date = strtotime($assignment['due_date']);
                                                $now = time();
                                                $days_diff = round(($due_date - $now) / (60 * 60 * 24));

                                                if ($days_diff < 0) {
                                                    echo '<span class="badge bg-danger">انتهى</span>';
                                                } elseif ($days_diff == 0) {
                                                    echo '<span class="badge bg-warning">اليوم</span>';
                                                } else {
                                                    echo '<span class="badge bg-info">متبقي ' . $days_diff . ' يوم</span>';
                                                }
                                                ?>
                                            </small>
                                        </div>
                                        <small class="d-block">تاريخ الاستحقاق: <?php echo date('Y-m-d', $due_date); ?></small>
                                        <small class="d-block">التسليمات: <?php echo $assignment['submission_count']; ?></small>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="assignments.php?circle_id=<?php echo $circle_id; ?>" class="btn btn-sm btn-outline-warning">عرض كل الواجبات</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include_once '../includes/footer.php'; ?>
</body>
</html>
