<?php
// Include common functions and definitions
require_once 'includes/common.php';

// Create a simple admin user
$username = 'admin';
$password = '123456';
$password_hash = password_hash($password, PASSWORD_DEFAULT);
$full_name = 'مدير النظام';
$email = '<EMAIL>';
$role_id = 1; // system_owner
$is_active = 1;

try {
    // Check if user already exists
    $stmt = $pdo->prepare("SELECT user_id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "المستخدم موجود بالفعل.<br>";
        
        // Update password
        $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = ?");
        $stmt->execute([$password_hash, $username]);
        echo "تم تحديث كلمة المرور بنجاح.<br>";
    } else {
        // Create new user
        $stmt = $pdo->prepare("INSERT INTO users (username, password_hash, full_name, email, role_id, is_active) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$username, $password_hash, $full_name, $email, $role_id, $is_active]);
        echo "تم إنشاء المستخدم بنجاح.<br>";
    }
    
    echo "اسم المستخدم: " . $username . "<br>";
    echo "كلمة المرور: " . $password . "<br>";
    
    // Show the hashed password for verification
    echo "كلمة المرور المشفرة: " . $password_hash . "<br>";
    
    // Verify the password
    $verify = password_verify($password, $password_hash);
    echo "التحقق من كلمة المرور: " . ($verify ? "صحيح" : "خاطئ") . "<br>";
    
} catch (PDOException $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
