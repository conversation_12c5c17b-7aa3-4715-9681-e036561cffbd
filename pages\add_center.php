<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner or center_admin role
if (!is_logged_in() || !(has_role('system_owner') || has_role('center_admin'))) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success = '';
$error = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $center_name = sanitize_input($_POST['center_name']);
    $address = sanitize_input($_POST['address']);
    $contact_person_name = sanitize_input($_POST['contact_person_name']);
    $contact_email = sanitize_input($_POST['contact_email']);
    $contact_phone = sanitize_input($_POST['contact_phone']);
    
    // Validate required fields
    if (empty($center_name)) {
        $error = 'يرجى إدخال اسم المركز';
    } elseif (empty($address)) {
        $error = 'يرجى إدخال عنوان المركز';
    } elseif (empty($contact_person_name)) {
        $error = 'يرجى إدخال اسم مسؤول الاتصال';
    } elseif (empty($contact_email)) {
        $error = 'يرجى إدخال البريد الإلكتروني للاتصال';
    } elseif (empty($contact_phone)) {
        $error = 'يرجى إدخال رقم الهاتف للاتصال';
    } else {
        try {
            // Check if center name already exists
            $stmt = $pdo->prepare("SELECT center_id FROM centers WHERE center_name = ?");
            $stmt->execute([$center_name]);
            if ($stmt->rowCount() > 0) {
                $error = 'اسم المركز موجود بالفعل، يرجى اختيار اسم آخر';
            } else {
                // Insert new center
                $stmt = $pdo->prepare("
                    INSERT INTO centers (
                        center_name, address, contact_person_name, 
                        contact_email, contact_phone, is_active
                    ) VALUES (?, ?, ?, ?, ?, 1)
                ");
                $stmt->execute([
                    $center_name, $address, $contact_person_name, 
                    $contact_email, $contact_phone
                ]);
                
                $center_id = $pdo->lastInsertId();
                
                // Handle logo upload if provided
                if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/images/centers/';
                    
                    // Create directory if it doesn't exist
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }
                    
                    $file_extension = pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION);
                    $new_filename = 'center_' . $center_id . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_filename;
                    
                    if (move_uploaded_file($_FILES['logo']['tmp_name'], $upload_path)) {
                        // Update center with logo URL
                        $logo_url = 'assets/images/centers/' . $new_filename;
                        $stmt = $pdo->prepare("UPDATE centers SET logo_url = ? WHERE center_id = ?");
                        $stmt->execute([$logo_url, $center_id]);
                    }
                }
                
                $success = 'تم إضافة المركز بنجاح';
                
                // Redirect to centers list or center details
                if (has_role('system_owner')) {
                    set_flash_message('success', $success);
                    redirect('pages/centers.php');
                }
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إضافة المركز: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مركز جديد - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if (has_role('system_owner')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="system_owner_dashboard.php">لوحة التحكم</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="centers.php">المراكز</a>
                            </li>
                        <?php elseif (has_role('center_admin')): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="center_admin_dashboard.php">لوحة التحكم</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <main class="container py-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <?php if (has_role('system_owner')): ?>
                    <li class="breadcrumb-item"><a href="system_owner_dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="centers.php">المراكز</a></li>
                <?php elseif (has_role('center_admin')): ?>
                    <li class="breadcrumb-item"><a href="center_admin_dashboard.php">لوحة التحكم</a></li>
                <?php endif; ?>
                <li class="breadcrumb-item active" aria-current="page">إضافة مركز جديد</li>
            </ol>
        </nav>
        
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0"><i class="fas fa-plus-circle me-2"></i> إضافة مركز جديد</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="center_name" class="form-label">اسم المركز <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="center_name" name="center_name" required value="<?php echo isset($_POST['center_name']) ? $_POST['center_name'] : ''; ?>">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="logo" class="form-label">شعار المركز</label>
                            <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                            <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">عنوان المركز <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo isset($_POST['address']) ? $_POST['address'] : ''; ?></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="contact_person_name" class="form-label">اسم مسؤول الاتصال <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="contact_person_name" name="contact_person_name" required value="<?php echo isset($_POST['contact_person_name']) ? $_POST['contact_person_name'] : ''; ?>">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="contact_email" class="form-label">البريد الإلكتروني للاتصال <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="contact_email" name="contact_email" required value="<?php echo isset($_POST['contact_email']) ? $_POST['contact_email'] : ''; ?>">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="contact_phone" class="form-label">رقم الهاتف للاتصال <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="contact_phone" name="contact_phone" required value="<?php echo isset($_POST['contact_phone']) ? $_POST['contact_phone'] : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <?php if (has_role('system_owner')): ?>
                            <a href="centers.php" class="btn btn-secondary">إلغاء</a>
                        <?php elseif (has_role('center_admin')): ?>
                            <a href="center_admin_dashboard.php" class="btn btn-secondary">إلغاء</a>
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary">إضافة المركز</button>
                    </div>
                </form>
            </div>
        </div>
    </main>
    
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
