<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has parent role
if (!is_logged_in() || !has_role('parent')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Get parent information
$parent_id = $_SESSION['user_id'];

// Get children information
try {
    $stmt = $pdo->prepare("
        SELECT u.user_id, u.full_name, u.profile_picture_url,
               c.circle_name, c.level,
               t.full_name AS teacher_name,
               ce.center_name
        FROM users u
        JOIN student_circle_enrollments sce ON u.user_id = sce.student_user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        JOIN users t ON c.teacher_user_id = t.user_id
        JOIN centers ce ON c.center_id = ce.center_id
        WHERE sce.parent_user_id = ? AND sce.status = 'approved'
    ");
    $stmt->execute([$parent_id]);
    $children = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الأبناء: ' . $e->getMessage();
}

// Get recent memorization progress for children
try {
    $stmt = $pdo->prepare("
        SELECT mp.progress_id, mp.surah_name, mp.ayah_from, mp.ayah_to,
               mp.recitation_date, mp.memorization_quality, mp.tajweed_application,
               u.full_name AS student_name
        FROM memorization_progress mp
        JOIN student_circle_enrollments sce ON mp.enrollment_id = sce.enrollment_id
        JOIN users u ON sce.student_user_id = u.user_id
        WHERE sce.parent_user_id = ?
        ORDER BY mp.recitation_date DESC
        LIMIT 10
    ");
    $stmt->execute([$parent_id]);
    $recent_progress = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات التقدم: ' . $e->getMessage();
}

// Get recent attendance records for children
try {
    $stmt = $pdo->prepare("
        SELECT ar.attendance_id, ar.session_date, ar.status,
               u.full_name AS student_name, c.circle_name
        FROM attendance_records ar
        JOIN student_circle_enrollments sce ON ar.enrollment_id = sce.enrollment_id
        JOIN users u ON sce.student_user_id = u.user_id
        JOIN circles c ON sce.circle_id = c.circle_id
        WHERE sce.parent_user_id = ?
        ORDER BY ar.session_date DESC
        LIMIT 10
    ");
    $stmt->execute([$parent_id]);
    $recent_attendance = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الحضور: ' . $e->getMessage();
}

// Get unread messages
try {
    $stmt = $pdo->prepare("
        SELECT m.message_id, m.subject, m.sent_at, u.full_name AS sender_name
        FROM messages m
        JOIN users u ON m.sender_user_id = u.user_id
        WHERE m.recipient_user_id = ? AND m.read_at IS NULL
        ORDER BY m.sent_at DESC
        LIMIT 5
    ");
    $stmt->execute([$parent_id]);
    $unread_messages = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع الرسائل: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم ولي الأمر - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link active" href="parent_dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="my_children.php">أبنائي</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="messages.php">الرسائل</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="settings.php">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container py-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <h1 class="mb-4">مرحباً، <?php echo $_SESSION['full_name']; ?></h1>

        <!-- Children Summary -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-child me-2"></i> أبنائي</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($children)): ?>
                            <p class="text-center">لا يوجد أبناء مسجلين حالياً.</p>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($children as $child): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <img src="<?php echo !empty($child['profile_picture_url']) ? '../' . $child['profile_picture_url'] : '../assets/images/default-avatar.png'; ?>"
                                                     class="rounded-circle mb-3" width="80" height="80" alt="صورة الطالب">
                                                <h5 class="card-title"><?php echo $child['full_name']; ?></h5>
                                                <p class="card-text">
                                                    <span class="d-block"><strong>الحلقة:</strong> <?php echo $child['circle_name']; ?></span>
                                                    <span class="d-block"><strong>المستوى:</strong> <?php echo $child['level']; ?></span>
                                                    <span class="d-block"><strong>المعلم:</strong> <?php echo $child['teacher_name']; ?></span>
                                                    <span class="d-block"><strong>المركز:</strong> <?php echo $child['center_name']; ?></span>
                                                </p>
                                                <a href="student_details.php?id=<?php echo $child['user_id']; ?>" class="btn btn-primary">عرض التفاصيل</a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Progress and Attendance -->
        <div class="row mb-4">
            <!-- Recent Memorization Progress -->
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-book-reader me-2"></i> آخر تقدم في الحفظ</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_progress)): ?>
                            <p class="text-center">لا يوجد تقدم مسجل حالياً.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>السورة</th>
                                            <th>الآيات</th>
                                            <th>التقييم</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_progress as $progress): ?>
                                            <tr>
                                                <td><?php echo $progress['student_name']; ?></td>
                                                <td><?php echo $progress['surah_name']; ?></td>
                                                <td><?php echo $progress['ayah_from'] . ' - ' . $progress['ayah_to']; ?></td>
                                                <td>
                                                    <?php
                                                    $quality = $progress['memorization_quality'];
                                                    $color = '';
                                                    switch ($quality) {
                                                        case 'excellent': $color = 'success'; break;
                                                        case 'very_good': $color = 'primary'; break;
                                                        case 'good': $color = 'info'; break;
                                                        case 'fair': $color = 'warning'; break;
                                                        case 'poor': $color = 'danger'; break;
                                                    }
                                                    echo '<span class="badge bg-' . $color . '">' . ucfirst(str_replace('_', ' ', $quality)) . '</span>';
                                                    ?>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($progress['recitation_date'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="memorization_progress.php" class="btn btn-sm btn-outline-success">عرض الكل</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Attendance -->
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0"><i class="fas fa-calendar-check me-2"></i> آخر سجلات الحضور</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_attendance)): ?>
                            <p class="text-center">لا يوجد سجلات حضور حالياً.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الحلقة</th>
                                            <th>التاريخ</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_attendance as $attendance): ?>
                                            <tr>
                                                <td><?php echo $attendance['student_name']; ?></td>
                                                <td><?php echo $attendance['circle_name']; ?></td>
                                                <td><?php echo date('Y-m-d', strtotime($attendance['session_date'])); ?></td>
                                                <td>
                                                    <?php
                                                    $status = $attendance['status'];
                                                    $icon = '';
                                                    $text = '';
                                                    $class = '';

                                                    switch ($status) {
                                                        case 'present':
                                                            $icon = 'check-circle';
                                                            $text = 'حاضر';
                                                            $class = 'attendance-present';
                                                            break;
                                                        case 'absent_excused':
                                                            $icon = 'exclamation-circle';
                                                            $text = 'غائب بعذر';
                                                            $class = 'attendance-excused';
                                                            break;
                                                        case 'absent_unexcused':
                                                            $icon = 'times-circle';
                                                            $text = 'غائب';
                                                            $class = 'attendance-absent';
                                                            break;
                                                        case 'late':
                                                            $icon = 'clock';
                                                            $text = 'متأخر';
                                                            $class = 'attendance-late';
                                                            break;
                                                    }

                                                    echo '<span class="' . $class . '"><i class="fas fa-' . $icon . '"></i> ' . $text . '</span>';
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="attendance_records.php" class="btn btn-sm btn-outline-info">عرض الكل</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unread Messages -->
        <div class="row">
            <div class="col-md-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0"><i class="fas fa-envelope me-2"></i> الرسائل غير المقروءة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($unread_messages)): ?>
                            <p class="text-center">لا توجد رسائل غير مقروءة.</p>
                        <?php else: ?>
                            <div class="list-group">
                                <?php foreach ($unread_messages as $message): ?>
                                    <a href="<?php echo get_root_url(); ?>pages/message_details.php?id=<?php echo $message['message_id']; ?>" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1"><?php echo $message['subject']; ?></h6>
                                            <small><?php echo date('Y-m-d H:i', strtotime($message['sent_at'])); ?></small>
                                        </div>
                                        <p class="mb-1">من: <?php echo $message['sender_name']; ?></p>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-3">
                                <a href="messages.php" class="btn btn-sm btn-outline-warning">عرض كل الرسائل</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p>نظام متكامل لإدارة حلقات تحفيظ القرآن الكريم</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="../index.php" class="text-white">الرئيسية</a></li>
                        <li><a href="about.php" class="text-white">عن النظام</a></li>
                        <li><a href="contact.php" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +966 12 345 6789</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="../js/script.js"></script>

    <?php
    // Include announcements modal
    include_once '../includes/announcement_modal.php';
    ?>
</body>
</html>
