<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

echo "<h1>اختبار سريع لنظام نشاط المستخدمين</h1>";

try {
    // Test 1: Check existing tables
    echo "<h2>1. فحص الجداول الموجودة:</h2>";
    $existing_tables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existing_tables[] = $row[0];
    }
    
    echo "<ul>";
    foreach (['users', 'student_circle_enrollments', 'announcements', 'whatsapp_logs', 'attendance_records', 'memorization_progress', 'system_logs'] as $table) {
        $exists = in_array($table, $existing_tables);
        echo "<li style='color: " . ($exists ? 'green' : 'red') . "'>{$table}: " . ($exists ? '✓ موجود' : '✗ غير موجود') . "</li>";
    }
    echo "</ul>";

    // Test 2: Check announcements table structure if it exists
    if (in_array('announcements', $existing_tables)) {
        echo "<h2>2. فحص هيكل جدول الإعلانات:</h2>";
        
        $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'created_by_user_id'");
        $stmt->execute();
        $has_created_by = $stmt->rowCount() > 0;
        
        $stmt = $pdo->prepare("SHOW COLUMNS FROM announcements LIKE 'sender_user_id'");
        $stmt->execute();
        $has_sender = $stmt->rowCount() > 0;
        
        echo "<ul>";
        echo "<li style='color: " . ($has_created_by ? 'green' : 'red') . "'>created_by_user_id: " . ($has_created_by ? '✓ موجود' : '✗ غير موجود') . "</li>";
        echo "<li style='color: " . ($has_sender ? 'green' : 'red') . "'>sender_user_id: " . ($has_sender ? '✓ موجود' : '✗ غير موجود') . "</li>";
        echo "</ul>";
        
        $user_id_column = $has_sender ? 'sender_user_id' : ($has_created_by ? 'created_by_user_id' : 'NULL');
        echo "<p><strong>العمود المستخدم:</strong> {$user_id_column}</p>";
    }

    // Test 3: Check attendance_records table structure if it exists
    if (in_array('attendance_records', $existing_tables)) {
        echo "<h2>3. فحص هيكل جدول سجلات الحضور:</h2>";
        
        $stmt = $pdo->prepare("SHOW COLUMNS FROM attendance_records LIKE 'attendance_date'");
        $stmt->execute();
        $has_attendance_date = $stmt->rowCount() > 0;
        
        $stmt = $pdo->prepare("SHOW COLUMNS FROM attendance_records LIKE 'session_date'");
        $stmt->execute();
        $has_session_date = $stmt->rowCount() > 0;
        
        echo "<ul>";
        echo "<li style='color: " . ($has_attendance_date ? 'green' : 'red') . "'>attendance_date: " . ($has_attendance_date ? '✓ موجود' : '✗ غير موجود') . "</li>";
        echo "<li style='color: " . ($has_session_date ? 'green' : 'red') . "'>session_date: " . ($has_session_date ? '✓ موجود' : '✗ غير موجود') . "</li>";
        echo "</ul>";
        
        $date_column = $has_session_date ? 'session_date' : ($has_attendance_date ? 'attendance_date' : 'NULL');
        echo "<p><strong>العمود المستخدم:</strong> {$date_column}</p>";
    }

    // Test 4: Test simple queries
    echo "<h2>4. اختبار الاستعلامات البسيطة:</h2>";
    $days_filter = 30;
    
    // Test user registrations
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
        $stmt->execute([$days_filter]);
        $recent_users = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ المستخدمين الجدد (آخر 30 يوم): <strong>{$recent_users}</strong></p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ خطأ في استعلام المستخدمين: " . $e->getMessage() . "</p>";
    }
    
    // Test enrollments
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM student_circle_enrollments WHERE enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY) AND enrollment_date IS NOT NULL");
        $stmt->execute([$days_filter]);
        $recent_enrollments = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ التسجيلات الجديدة (آخر 30 يوم): <strong>{$recent_enrollments}</strong></p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ خطأ في استعلام التسجيلات: " . $e->getMessage() . "</p>";
    }
    
    // Test announcements if table exists
    if (in_array('announcements', $existing_tables)) {
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM announcements WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at IS NOT NULL");
            $stmt->execute([$days_filter]);
            $recent_announcements = $stmt->fetchColumn();
            echo "<p style='color: green;'>✓ الإعلانات الجديدة (آخر 30 يوم): <strong>{$recent_announcements}</strong></p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في استعلام الإعلانات: " . $e->getMessage() . "</p>";
        }
    }

    // Test 5: Test complex query
    echo "<h2>5. اختبار الاستعلام المعقد:</h2>";
    
    try {
        $user_registration_query = "
            SELECT 
                'تسجيل مستخدم جديد' as activity_type,
                CONCAT('تم تسجيل مستخدم جديد: ', u.full_name, ' (', r.role_name, ')') as description,
                u.created_at as timestamp,
                'النظام' as user_name
            FROM users u
            JOIN roles r ON u.role_id = r.role_id
            WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND u.created_at IS NOT NULL
            LIMIT 3
        ";
        
        $enrollment_query = "
            SELECT 
                'تسجيل في الحلقات' as activity_type,
                CONCAT('تسجيل طالب: ', u.full_name, ' في حلقة ', c.circle_name,
                       CASE WHEN p.full_name IS NOT NULL THEN CONCAT(' (ولي الأمر: ', p.full_name, ')') ELSE '' END) as description,
                sce.enrollment_date as timestamp,
                'النظام' as user_name
            FROM student_circle_enrollments sce
            JOIN users u ON sce.student_user_id = u.user_id
            JOIN circles c ON sce.circle_id = c.circle_id
            LEFT JOIN users p ON sce.parent_user_id = p.user_id
            WHERE sce.enrollment_date >= DATE_SUB(NOW(), INTERVAL ? DAY)
            AND sce.enrollment_date IS NOT NULL
            LIMIT 3
        ";
        
        $final_query = "(" . $user_registration_query . ") UNION (" . $enrollment_query . ") ORDER BY timestamp DESC LIMIT 5";
        
        $stmt = $pdo->prepare($final_query);
        $stmt->execute([$days_filter, $days_filter]);
        $sample_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($sample_activities)) {
            echo "<p style='color: green;'>✓ تم تنفيذ الاستعلام المعقد بنجاح!</p>";
            echo "<h3>عينة من الأنشطة:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>نوع النشاط</th><th>الوصف</th><th>التاريخ</th></tr>";
            foreach ($sample_activities as $activity) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($activity['activity_type']) . "</td>";
                echo "<td>" . htmlspecialchars($activity['description']) . "</td>";
                echo "<td>" . htmlspecialchars($activity['timestamp']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ الاستعلام نجح لكن لا توجد أنشطة حديثة</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ خطأ في الاستعلام المعقد: " . $e->getMessage() . "</p>";
    }

    // Test 6: Recommendations
    echo "<h2>6. التوصيات:</h2>";
    echo "<ul>";
    
    $total_recent = ($recent_users ?? 0) + ($recent_enrollments ?? 0) + ($recent_announcements ?? 0);
    
    if ($total_recent == 0) {
        echo "<li style='color: red;'>⚠️ لا توجد أنشطة حديثة. يُنصح بإضافة بيانات تجريبية.</li>";
        echo "<li><a href='add_real_activity_data.php' style='color: blue;'>👉 إضافة بيانات تجريبية</a></li>";
    } else {
        echo "<li style='color: green;'>✅ يوجد {$total_recent} نشاط حديث. النظام جاهز!</li>";
    }
    
    echo "<li><a href='user_activity.php' style='color: green; font-weight: bold;'>🚀 انتقل إلى صفحة نشاط المستخدمين</a></li>";
    echo "</ul>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ عام: " . $e->getMessage() . "</p>";
}

echo "<br><hr><p><a href='../dashboard.php'>العودة للوحة التحكم</a></p>";
?>
