<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

$success_message = '';
$error_message = '';

try {
    // Create some sample attendance records if table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'attendance_records'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        // Get some sample data for attendance
        $stmt = $pdo->prepare("
            SELECT sce.enrollment_id, u.full_name as student_name, u.user_id as student_id
            FROM student_circle_enrollments sce 
            JOIN users u ON sce.student_user_id = u.user_id 
            LIMIT 5
        ");
        $stmt->execute();
        $enrollments = $stmt->fetchAll();
        
        // Get a teacher user
        $stmt = $pdo->prepare("
            SELECT user_id FROM users 
            WHERE role_id = (SELECT role_id FROM roles WHERE role_name = 'teacher') 
            LIMIT 1
        ");
        $stmt->execute();
        $teacher_id = $stmt->fetchColumn();
        
        if ($teacher_id && !empty($enrollments)) {
            $attendance_count = 0;
            foreach ($enrollments as $enrollment) {
                // Create attendance records for the last few days
                for ($i = 1; $i <= 3; $i++) {
                    $date = date('Y-m-d', strtotime("-{$i} days"));
                    $statuses = ['present', 'absent_excused', 'absent_unexcused', 'late'];
                    $status = $statuses[array_rand($statuses)];
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO attendance_records (enrollment_id, attendance_date, status, recorded_by_user_id, recorded_at)
                        VALUES (?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE status = VALUES(status)
                    ");
                    
                    if ($stmt->execute([
                        $enrollment['enrollment_id'],
                        $date,
                        $status,
                        $teacher_id,
                        date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(28800, 64800)) // Random time between 8 AM and 6 PM
                    ])) {
                        $attendance_count++;
                    }
                }
            }
            $success_message .= "تم إنشاء {$attendance_count} سجل حضور. ";
        }
    }
    
    // Create some sample memorization progress if table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'memorization_progress'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        // Get some sample data for memorization
        $stmt = $pdo->prepare("
            SELECT sce.enrollment_id, u.full_name as student_name
            FROM student_circle_enrollments sce 
            JOIN users u ON sce.student_user_id = u.user_id 
            LIMIT 3
        ");
        $stmt->execute();
        $enrollments = $stmt->fetchAll();
        
        // Get a teacher user
        $stmt = $pdo->prepare("
            SELECT user_id FROM users 
            WHERE role_id = (SELECT role_id FROM roles WHERE role_name = 'teacher') 
            LIMIT 1
        ");
        $stmt->execute();
        $teacher_id = $stmt->fetchColumn();
        
        if ($teacher_id && !empty($enrollments)) {
            $memorization_count = 0;
            $surahs = [
                ['الفاتحة', 1, 7],
                ['البقرة', 1, 50],
                ['آل عمران', 1, 30],
                ['النساء', 1, 25],
                ['المائدة', 1, 20]
            ];
            
            foreach ($enrollments as $enrollment) {
                for ($i = 1; $i <= 2; $i++) {
                    $surah = $surahs[array_rand($surahs)];
                    $qualities = ['excellent', 'very_good', 'good', 'fair', 'poor'];
                    $quality = $qualities[array_rand($qualities)];
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO memorization_progress (
                            enrollment_id, surah_name, ayah_from, ayah_to, 
                            memorization_quality, recitation_date, recorded_by_user_id
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    if ($stmt->execute([
                        $enrollment['enrollment_id'],
                        $surah[0],
                        $surah[1],
                        $surah[2],
                        $quality,
                        date('Y-m-d', strtotime("-{$i} days")),
                        $teacher_id
                    ])) {
                        $memorization_count++;
                    }
                }
            }
            $success_message .= "تم إنشاء {$memorization_count} سجل تقييم حفظ. ";
        }
    }
    
    // Create some system logs
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'system_logs'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        $log_count = 0;
        $sample_logs = [
            ['AUTH', 'تسجيل دخول ناجح للمستخدم: admin', 'INFO'],
            ['AUTH', 'تسجيل خروج للمستخدم: admin', 'INFO'],
            ['USER', 'تحديث بيانات المستخدم: أحمد محمد', 'INFO'],
            ['CONFIG', 'تغيير إعداد النظام: site_name', 'INFO'],
            ['SYSTEM', 'تم تشغيل النظام بنجاح', 'INFO'],
            ['WHATSAPP', 'إرسال رسالة واتساب نجح: إشعار غياب', 'INFO'],
            ['WHATSAPP', 'إرسال رسالة واتساب فشل: رقم غير صحيح', 'ERROR'],
        ];
        
        foreach ($sample_logs as $log) {
            for ($i = 1; $i <= 2; $i++) {
                $stmt = $pdo->prepare("
                    INSERT INTO system_logs (log_category, message, log_level, user_id, ip_address, user_agent, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                
                if ($stmt->execute([
                    $log[0],
                    $log[1],
                    $log[2],
                    $_SESSION['user_id'],
                    $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                    $_SERVER['HTTP_USER_AGENT'] ?? 'System Init Script',
                    date('Y-m-d H:i:s', strtotime("-{$i} days") + rand(0, 86400))
                ])) {
                    $log_count++;
                }
            }
        }
        $success_message .= "تم إنشاء {$log_count} سجل نظام. ";
    }
    
    // Create some new user registrations (update existing users' created_at)
    $stmt = $pdo->prepare("
        UPDATE users 
        SET created_at = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY) 
        WHERE user_id IN (
            SELECT * FROM (
                SELECT user_id FROM users ORDER BY user_id DESC LIMIT 3
            ) as temp
        )
    ");
    if ($stmt->execute()) {
        $success_message .= "تم تحديث تواريخ تسجيل المستخدمين. ";
    }
    
    // Create some new enrollments (update existing enrollments' enrollment_date)
    $stmt = $pdo->prepare("
        UPDATE student_circle_enrollments 
        SET enrollment_date = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY) 
        WHERE enrollment_id IN (
            SELECT * FROM (
                SELECT enrollment_id FROM student_circle_enrollments ORDER BY enrollment_id DESC LIMIT 3
            ) as temp
        )
    ");
    if ($stmt->execute()) {
        $success_message .= "تم تحديث تواريخ التسجيل في الحلقات. ";
    }
    
    if (empty($success_message)) {
        $success_message = "تم تهيئة بيانات نشاط المستخدمين بنجاح!";
    }
    
} catch (PDOException $e) {
    $error_message = 'حدث خطأ أثناء تهيئة البيانات: ' . $e->getMessage();
}

// Redirect back to user activity page
if (!empty($success_message)) {
    set_flash_message('success', $success_message);
} else {
    set_flash_message('danger', $error_message ?: 'حدث خطأ غير معروف');
}

redirect('user_activity.php');
?>
