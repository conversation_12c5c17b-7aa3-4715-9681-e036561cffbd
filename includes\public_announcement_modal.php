<?php
/**
 * Public Announcement Modal Component
 *
 * This file contains the modal that displays public announcements to all visitors
 * when they visit the website.
 */

// Get public announcements
$public_modal_announcements = [];
try {
    // Check if columns exist in the announcements table
    $media_type_exists = column_exists($pdo, 'announcements', 'media_type');
    $media_url_exists = column_exists($pdo, 'announcements', 'media_url');
    $background_color_exists = column_exists($pdo, 'announcements', 'background_color');
    $text_color_exists = column_exists($pdo, 'announcements', 'text_color');
    $image_url_exists = column_exists($pdo, 'announcements', 'image_url');

    $stmt = $pdo->prepare("
        SELECT a.announcement_id, a.title, a.content, a.sender_user_id, a.target_role, a.target_center_id,
               a.is_active, a.created_at, a.updated_at,
               " . ($media_type_exists ? "a.media_type" : "'none'") . " AS media_type,
               " . ($media_url_exists ? "a.media_url" : "NULL") . " AS media_url,
               " . ($image_url_exists ? "a.image_url" : "NULL") . " AS image_url,
               " . ($background_color_exists ? "a.background_color" : "'#ffffff'") . " AS background_color,
               " . ($text_color_exists ? "a.text_color" : "'#000000'") . " AS text_color,
               u.full_name AS sender_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        WHERE a.is_active = TRUE
        AND a.is_public = TRUE
        AND CURRENT_DATE BETWEEN a.start_date AND a.end_date
        ORDER BY a.created_at DESC
        LIMIT 1
    ");
    $stmt->execute();
    $public_modal_announcements = $stmt->fetchAll();
} catch (PDOException $e) {
    // Silently fail
    error_log('Error fetching public modal announcements: ' . $e->getMessage());
}

// Only show the modal if there are public announcements
if (!empty($public_modal_announcements)):
    $announcement = $public_modal_announcements[0];
?>

<!-- Public Announcements Modal -->
<div class="modal fade" id="publicAnnouncementsModal" tabindex="-1" aria-labelledby="publicAnnouncementsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content"
             style="background-color: <?php echo !empty($announcement['background_color']) ? $announcement['background_color'] : '#ffffff'; ?>;
                    color: <?php echo !empty($announcement['text_color']) ? $announcement['text_color'] : '#000000'; ?>;">
            <div class="modal-header">
                <h5 class="modal-title" id="publicAnnouncementsModalLabel">
                    <i class="fas fa-bullhorn me-2"></i> <?php echo $announcement['title']; ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php if (!empty($announcement['media_url'])): ?>
                    <div class="text-center mb-3">
                        <?php if ($announcement['media_type'] === 'image'): ?>
                            <img src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" alt="صورة الإعلان" class="img-fluid rounded" style="max-height: 300px;">
                        <?php elseif ($announcement['media_type'] === 'video'): ?>
                            <video controls class="img-fluid rounded" style="max-height: 300px;">
                                <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="video/mp4">
                                متصفحك لا يدعم تشغيل الفيديو.
                            </video>
                        <?php elseif ($announcement['media_type'] === 'audio'): ?>
                            <audio controls class="w-100">
                                <source src="<?php echo strpos($announcement['media_url'], 'http') === 0 ? $announcement['media_url'] : '../' . $announcement['media_url']; ?>" type="audio/mpeg">
                                متصفحك لا يدعم تشغيل الصوت.
                            </audio>
                        <?php endif; ?>
                    </div>
                <?php elseif (!empty($announcement['image_url'])): ?>
                    <div class="text-center mb-3">
                        <img src="<?php echo $announcement['image_url']; ?>" class="img-fluid rounded" alt="<?php echo $announcement['title']; ?>" style="max-height: 300px;">
                    </div>
                <?php endif; ?>

                <div class="announcement-content" style="padding: 15px; border-radius: 5px;">
                    <?php echo $announcement['content']; ?>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i> <?php echo $announcement['sender_name']; ?> |
                        <i class="fas fa-calendar-alt me-1"></i> <?php echo date('Y-m-d', strtotime($announcement['created_at'])); ?>
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <a href="public_announcements.php" class="btn btn-outline-secondary">
                    <i class="fas fa-list me-1"></i> عرض كل الإعلانات
                </a>
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Show the public announcements modal when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Check if the modal has been shown before in this session
        const modalShown = sessionStorage.getItem('publicAnnouncementModalShown');

        if (!modalShown) {
            // Initialize the modal
            const modalElement = document.getElementById('publicAnnouncementsModal');
            if (modalElement) {
                const announcementsModal = new bootstrap.Modal(modalElement);
                announcementsModal.show();

                // Mark as shown in this session
                sessionStorage.setItem('publicAnnouncementModalShown', 'true');
            }
        }
    });
</script>

<?php endif; ?>
