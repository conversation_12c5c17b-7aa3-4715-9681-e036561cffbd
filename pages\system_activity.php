<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Initialize variables
$activities = [];
$error = '';
$filter_type = isset($_GET['type']) ? $_GET['type'] : 'all';
$filter_date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-d', strtotime('-30 days'));
$filter_date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Get system activity
try {
    // Base query parts
    $user_registration_query = "
        SELECT 'New User Registration' AS activity_type,
               u.full_name AS subject,
               u.created_at AS timestamp,
               CONCAT(r.role_name, ' - ', COALESCE(c.center_name, 'No Center')) AS details,
               u.user_id AS related_id
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
    ";
    
    $center_creation_query = "
        SELECT 'New Center Created' AS activity_type,
               c.center_name AS subject,
               c.created_at AS timestamp,
               CONCAT('Director: ', COALESCE(u.full_name, 'Not assigned')) AS details,
               c.center_id AS related_id
        FROM centers c
        LEFT JOIN users u ON c.director_user_id = u.user_id
    ";
    
    $circle_creation_query = "
        SELECT 'New Circle Created' AS activity_type,
               ci.circle_name AS subject,
               ci.created_at AS timestamp,
               CONCAT('Center: ', c.center_name, ', Teacher: ', u.full_name) AS details,
               ci.circle_id AS related_id
        FROM circles ci
        JOIN centers c ON ci.center_id = c.center_id
        JOIN users u ON ci.teacher_user_id = u.user_id
    ";
    
    $announcement_query = "
        SELECT 'New Announcement' AS activity_type,
               a.title AS subject,
               a.created_at AS timestamp,
               CONCAT('By: ', u.full_name, ', Target: ', 
                     CASE 
                         WHEN a.target_role = 'all' THEN 'All Users'
                         ELSE a.target_role
                     END) AS details,
               a.announcement_id AS related_id
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
    ";
    
    $assignment_query = "
        SELECT 'New Assignment' AS activity_type,
               a.title AS subject,
               a.created_at AS timestamp,
               CONCAT('Circle: ', c.circle_name, ', By: ', u.full_name) AS details,
               a.assignment_id AS related_id
        FROM assignments a
        JOIN circles c ON a.circle_id = c.circle_id
        JOIN users u ON a.created_by_user_id = u.user_id
    ";
    
    // Date filter condition
    $date_condition = "WHERE timestamp BETWEEN :date_from AND :date_to";
    
    // Type filter condition
    $type_condition = "";
    if ($filter_type !== 'all') {
        $type_condition = "AND activity_type = :activity_type";
    }
    
    // Build the complete query
    $query = "";
    
    // Add each activity type based on filter
    if ($filter_type === 'all' || $filter_type === 'New User Registration') {
        $query .= "($user_registration_query $date_condition $type_condition) UNION ";
    }
    
    if ($filter_type === 'all' || $filter_type === 'New Center Created') {
        $query .= "($center_creation_query $date_condition $type_condition) UNION ";
    }
    
    if ($filter_type === 'all' || $filter_type === 'New Circle Created') {
        $query .= "($circle_creation_query $date_condition $type_condition) UNION ";
    }
    
    if ($filter_type === 'all' || $filter_type === 'New Announcement') {
        $query .= "($announcement_query $date_condition $type_condition) UNION ";
    }
    
    if ($filter_type === 'all' || $filter_type === 'New Assignment') {
        $query .= "($assignment_query $date_condition $type_condition) UNION ";
    }
    
    // Remove the last UNION and add ORDER BY and LIMIT
    $query = rtrim($query, " UNION ");
    $query .= " ORDER BY timestamp DESC LIMIT :offset, :per_page";
    
    // Prepare and execute the query
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':date_from', $filter_date_from);
    $stmt->bindParam(':date_to', $filter_date_to . ' 23:59:59');
    
    if ($filter_type !== 'all') {
        $stmt->bindParam(':activity_type', $filter_type);
    }
    
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':per_page', $per_page, PDO::PARAM_INT);
    $stmt->execute();
    
    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Count total for pagination
    $count_query = str_replace("LIMIT :offset, :per_page", "", $query);
    $count_query = "SELECT COUNT(*) FROM ($count_query) AS count_table";
    
    $stmt = $pdo->prepare($count_query);
    $stmt->bindParam(':date_from', $filter_date_from);
    $stmt->bindParam(':date_to', $filter_date_to . ' 23:59:59');
    
    if ($filter_type !== 'all') {
        $stmt->bindParam(':activity_type', $filter_type);
    }
    
    $stmt->execute();
    $total_activities = $stmt->fetchColumn();
    $total_pages = ceil($total_activities / $per_page);
    
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع سجل النشاط: ' . $e->getMessage();
}

// Page title
$page_title = 'سجل النشاط';

// Include header
include_once '../includes/header.php';
?>

<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-history me-2"></i> سجل النشاط</h1>
        <a href="system_owner_dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للوحة التحكم
        </a>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <!-- Filter Form -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i> تصفية النتائج</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="type" class="form-label">نوع النشاط</label>
                    <select name="type" id="type" class="form-select">
                        <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>جميع الأنشطة</option>
                        <option value="New User Registration" <?php echo $filter_type === 'New User Registration' ? 'selected' : ''; ?>>تسجيل مستخدم جديد</option>
                        <option value="New Center Created" <?php echo $filter_type === 'New Center Created' ? 'selected' : ''; ?>>إنشاء مركز جديد</option>
                        <option value="New Circle Created" <?php echo $filter_type === 'New Circle Created' ? 'selected' : ''; ?>>إنشاء حلقة جديدة</option>
                        <option value="New Announcement" <?php echo $filter_type === 'New Announcement' ? 'selected' : ''; ?>>إعلان جديد</option>
                        <option value="New Assignment" <?php echo $filter_type === 'New Assignment' ? 'selected' : ''; ?>>واجب جديد</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $filter_date_from; ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $filter_date_to; ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Activity List -->
    <div class="card shadow">
        <div class="card-header bg-warning text-dark">
            <h5 class="card-title mb-0"><i class="fas fa-list me-2"></i> قائمة الأنشطة</h5>
        </div>
        <div class="card-body">
            <?php if (empty($activities)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <p class="lead">لا توجد أنشطة مطابقة لمعايير البحث</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نوع النشاط</th>
                                <th>الموضوع</th>
                                <th>التفاصيل</th>
                                <th>التاريخ والوقت</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($activities as $activity): 
                                $icon = '';
                                $color = '';
                                $link = '#';
                                
                                switch ($activity['activity_type']) {
                                    case 'New User Registration':
                                        $icon = 'fa-user-plus';
                                        $color = 'success';
                                        $link = 'user_details.php?id=' . $activity['related_id'];
                                        break;
                                    case 'New Center Created':
                                        $icon = 'fa-building';
                                        $color = 'info';
                                        $link = 'center_details.php?id=' . $activity['related_id'];
                                        break;
                                    case 'New Circle Created':
                                        $icon = 'fa-circle';
                                        $color = 'warning';
                                        $link = 'circle_details.php?id=' . $activity['related_id'];
                                        break;
                                    case 'New Announcement':
                                        $icon = 'fa-bullhorn';
                                        $color = 'danger';
                                        $link = 'system_announcements.php?id=' . $activity['related_id'];
                                        break;
                                    case 'New Assignment':
                                        $icon = 'fa-tasks';
                                        $color = 'primary';
                                        $link = 'assignment_details.php?id=' . $activity['related_id'];
                                        break;
                                    default:
                                        $icon = 'fa-bell';
                                        $color = 'secondary';
                                }
                            ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-<?php echo $color; ?>">
                                            <i class="fas <?php echo $icon; ?> me-1"></i>
                                            <?php 
                                                $activity_type_ar = [
                                                    'New User Registration' => 'تسجيل مستخدم جديد',
                                                    'New Center Created' => 'إنشاء مركز جديد',
                                                    'New Circle Created' => 'إنشاء حلقة جديدة',
                                                    'New Announcement' => 'إعلان جديد',
                                                    'New Assignment' => 'واجب جديد'
                                                ];
                                                echo $activity_type_ar[$activity['activity_type']] ?? $activity['activity_type'];
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($activity['subject']); ?></td>
                                    <td><?php echo htmlspecialchars($activity['details']); ?></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($activity['timestamp'])); ?></td>
                                    <td>
                                        <a href="<?php echo $link; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&type=<?php echo $filter_type; ?>&date_from=<?php echo $filter_date_from; ?>&date_to=<?php echo $filter_date_to; ?>">
                                        <i class="fas fa-chevron-right"></i> السابق
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&type=<?php echo $filter_type; ?>&date_from=<?php echo $filter_date_from; ?>&date_to=<?php echo $filter_date_to; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&type=<?php echo $filter_type; ?>&date_from=<?php echo $filter_date_from; ?>&date_to=<?php echo $filter_date_to; ?>">
                                        التالي <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
