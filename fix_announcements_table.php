<?php
// Include common functions and definitions
require_once 'includes/common.php';

// Check if user is logged in and has system_owner role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
    exit;
}

$success = '';
$error = '';

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Check if announcements table exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'announcements'
    ");
    $stmt->execute();
    $table_exists = (bool)$stmt->fetchColumn();
    
    if (!$table_exists) {
        // Create the announcements table with the correct structure
        $pdo->exec("
            CREATE TABLE announcements (
                announcement_id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                sender_user_id INT NOT NULL,
                target_role VARCHAR(50) DEFAULT 'all',
                target_center_id INT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (sender_user_id) REFERENCES users(user_id),
                FOREIGN KEY (target_center_id) REFERENCES centers(center_id)
            )
        ");
        
        $success = 'تم إنشاء جدول الإعلانات بنجاح.';
    } else {
        // Check if center_id column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'center_id'
        ");
        $stmt->execute();
        $center_id_exists = (bool)$stmt->fetchColumn();
        
        // Check if target_center_id column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'target_center_id'
        ");
        $stmt->execute();
        $target_center_id_exists = (bool)$stmt->fetchColumn();
        
        // Check if created_by_user_id column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'created_by_user_id'
        ");
        $stmt->execute();
        $created_by_user_id_exists = (bool)$stmt->fetchColumn();
        
        // Check if sender_user_id column exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'sender_user_id'
        ");
        $stmt->execute();
        $sender_user_id_exists = (bool)$stmt->fetchColumn();
        
        // Fix the table structure
        if ($center_id_exists && !$target_center_id_exists) {
            // Rename center_id to target_center_id
            $pdo->exec("ALTER TABLE announcements CHANGE center_id target_center_id INT NULL");
            $success .= 'تم تغيير اسم العمود center_id إلى target_center_id بنجاح. ';
        } elseif (!$center_id_exists && !$target_center_id_exists) {
            // Add target_center_id column
            $pdo->exec("ALTER TABLE announcements ADD COLUMN target_center_id INT NULL");
            $pdo->exec("ALTER TABLE announcements ADD FOREIGN KEY (target_center_id) REFERENCES centers(center_id)");
            $success .= 'تم إضافة العمود target_center_id بنجاح. ';
        }
        
        if ($created_by_user_id_exists && !$sender_user_id_exists) {
            // Rename created_by_user_id to sender_user_id
            $pdo->exec("ALTER TABLE announcements CHANGE created_by_user_id sender_user_id INT NOT NULL");
            $success .= 'تم تغيير اسم العمود created_by_user_id إلى sender_user_id بنجاح. ';
        } elseif (!$created_by_user_id_exists && !$sender_user_id_exists) {
            // Add sender_user_id column
            $pdo->exec("ALTER TABLE announcements ADD COLUMN sender_user_id INT NOT NULL");
            $pdo->exec("ALTER TABLE announcements ADD FOREIGN KEY (sender_user_id) REFERENCES users(user_id)");
            $success .= 'تم إضافة العمود sender_user_id بنجاح. ';
        }
        
        // Check if start_date and end_date columns exist
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'start_date'
        ");
        $stmt->execute();
        $start_date_exists = (bool)$stmt->fetchColumn();
        
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'announcements' 
            AND COLUMN_NAME = 'end_date'
        ");
        $stmt->execute();
        $end_date_exists = (bool)$stmt->fetchColumn();
        
        if (!$start_date_exists) {
            // Add start_date column with default value of current date
            $pdo->exec("ALTER TABLE announcements ADD COLUMN start_date DATE NOT NULL DEFAULT CURRENT_DATE");
            $success .= 'تم إضافة العمود start_date بنجاح. ';
        }
        
        if (!$end_date_exists) {
            // Add end_date column with default value of 30 days from now
            $pdo->exec("ALTER TABLE announcements ADD COLUMN end_date DATE NOT NULL DEFAULT DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)");
            $success .= 'تم إضافة العمود end_date بنجاح. ';
        }
        
        if (empty($success)) {
            $success = 'جدول الإعلانات بالفعل يحتوي على الهيكل الصحيح.';
        }
    }
    
    // Check if announcement_reads table exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'announcement_reads'
    ");
    $stmt->execute();
    $reads_table_exists = (bool)$stmt->fetchColumn();
    
    if (!$reads_table_exists) {
        // Create the announcement_reads table
        $pdo->exec("
            CREATE TABLE announcement_reads (
                read_id INT AUTO_INCREMENT PRIMARY KEY,
                announcement_id INT NOT NULL,
                user_id INT NOT NULL,
                read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (announcement_id) REFERENCES announcements(announcement_id),
                FOREIGN KEY (user_id) REFERENCES users(user_id),
                UNIQUE KEY (announcement_id, user_id)
            )
        ");
        
        $success .= ' تم إنشاء جدول قراءات الإعلانات بنجاح.';
    }
    
    // Commit transaction
    $pdo->commit();
    
    // Set flash message
    set_flash_message('success', $success);
    
} catch (PDOException $e) {
    // Rollback transaction
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $error = 'حدث خطأ أثناء إصلاح جدول الإعلانات: ' . $e->getMessage();
    set_flash_message('danger', $error);
}

// Redirect to announcements page
redirect('pages/announcements.php');
?>
