<?php
// Include common functions and definitions
require_once '../includes/common.php';
require_once '../includes/announcement_stats.php';

// Initialize variables
$search_query = isset($_GET['q']) ? sanitize_input($_GET['q']) : '';
$target_role = isset($_GET['target_role']) ? sanitize_input($_GET['target_role']) : '';
$target_center_id = isset($_GET['target_center_id']) ? (int)$_GET['target_center_id'] : '';
$media_type = isset($_GET['media_type']) ? sanitize_input($_GET['media_type']) : '';
$date_from = isset($_GET['date_from']) ? sanitize_input($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize_input($_GET['date_to']) : '';
$sort_by = isset($_GET['sort_by']) ? sanitize_input($_GET['sort_by']) : 'created_at';
$sort_order = isset($_GET['sort_order']) ? sanitize_input($_GET['sort_order']) : 'DESC';
$is_public = isset($_GET['is_public']) ? (int)$_GET['is_public'] : '';
$is_featured = isset($_GET['is_featured']) ? (int)$_GET['is_featured'] : '';
$is_active = isset($_GET['is_active']) ? (int)$_GET['is_active'] : '';

// Get announcements based on search criteria
try {
    // Check if columns exist in the announcements table
    $is_public_exists = column_exists($pdo, 'announcements', 'is_public');
    $is_featured_exists = column_exists($pdo, 'announcements', 'is_featured');
    $media_type_exists = column_exists($pdo, 'announcements', 'media_type');
    $media_url_exists = column_exists($pdo, 'announcements', 'media_url');
    $background_color_exists = column_exists($pdo, 'announcements', 'background_color');
    $text_color_exists = column_exists($pdo, 'announcements', 'text_color');
    $view_count_exists = column_exists($pdo, 'announcements', 'view_count');
    $click_count_exists = column_exists($pdo, 'announcements', 'click_count');
    $start_date_exists = column_exists($pdo, 'announcements', 'start_date');
    $end_date_exists = column_exists($pdo, 'announcements', 'end_date');

    // Build the query with conditional columns
    $query = "
        SELECT a.announcement_id, a.title, a.content, a.sender_user_id,
               a.target_role, a.target_center_id, a.is_active,
               " . ($is_public_exists ? "a.is_public," : "0 AS is_public,") . "
               " . ($is_featured_exists ? "a.is_featured," : "0 AS is_featured,") . "
               " . ($media_type_exists ? "a.media_type," : "'none' AS media_type,") . "
               " . ($media_url_exists ? "a.media_url," : "NULL AS media_url,") . "
               " . ($background_color_exists ? "a.background_color," : "'#ffffff' AS background_color,") . "
               " . ($text_color_exists ? "a.text_color," : "'#000000' AS text_color,") . "
               " . ($start_date_exists ? "a.start_date," : "a.created_at AS start_date,") . "
               " . ($end_date_exists ? "a.end_date," : "DATE_ADD(a.created_at, INTERVAL 30 DAY) AS end_date,") . "
               a.created_at, a.updated_at,
               " . ($view_count_exists ? "COALESCE(a.view_count, 0)" : "0") . " AS view_count,
               " . ($click_count_exists ? "COALESCE(a.click_count, 0)" : "0") . " AS click_count,
               u.full_name AS sender_name
        FROM announcements a
        JOIN users u ON a.sender_user_id = u.user_id
        WHERE 1=1
    ";
    $params = [];

    // Add search conditions
    if (!empty($search_query)) {
        $query .= " AND (a.title LIKE ? OR a.content LIKE ?)";
        $params[] = "%$search_query%";
        $params[] = "%$search_query%";
    }

    if (!empty($target_role) && $target_role !== 'all') {
        $query .= " AND a.target_role = ?";
        $params[] = $target_role;
    }

    if (!empty($target_center_id)) {
        $query .= " AND a.target_center_id = ?";
        $params[] = $target_center_id;
    }

    if ($media_type_exists && !empty($media_type) && $media_type !== 'all') {
        $query .= " AND a.media_type = ?";
        $params[] = $media_type;
    }

    if (!empty($date_from)) {
        $query .= " AND a.created_at >= ?";
        $params[] = $date_from . ' 00:00:00';
    }

    if (!empty($date_to)) {
        $query .= " AND a.created_at <= ?";
        $params[] = $date_to . ' 23:59:59';
    }

    if ($is_public_exists && $is_public !== '') {
        $query .= " AND a.is_public = ?";
        $params[] = $is_public;
    }

    if ($is_featured_exists && $is_featured !== '') {
        $query .= " AND a.is_featured = ?";
        $params[] = $is_featured;
    }

    if ($is_active !== '') {
        $query .= " AND a.is_active = ?";
        $params[] = $is_active;
    }

    // Add sorting
    $allowed_sort_fields = ['title', 'created_at'];

    // Add optional sort fields if they exist
    if ($start_date_exists) {
        $allowed_sort_fields[] = 'start_date';
    }

    if ($end_date_exists) {
        $allowed_sort_fields[] = 'end_date';
    }

    if ($view_count_exists) {
        $allowed_sort_fields[] = 'view_count';
    }

    if ($click_count_exists) {
        $allowed_sort_fields[] = 'click_count';
    }

    $allowed_sort_orders = ['ASC', 'DESC'];

    if (in_array($sort_by, $allowed_sort_fields) && in_array($sort_order, $allowed_sort_orders)) {
        $query .= " ORDER BY a.$sort_by $sort_order";
    } else {
        $query .= " ORDER BY a.created_at DESC";
    }

    // Execute the query
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $announcements = $stmt->fetchAll();

    // Get centers for filter
    $centers_stmt = $pdo->query("SELECT center_id, center_name FROM centers ORDER BY center_name");
    $centers = $centers_stmt->fetchAll();

} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء البحث عن الإعلانات: ' . $e->getMessage();
}

// Page variables
$page_title = 'بحث الإعلانات';
$active_page = 'announcements';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => has_role('system_owner') ? 'system_owner_dashboard.php' : (has_role('center_admin') ? 'center_admin_dashboard.php' : 'dashboard.php')],
    ['title' => 'الإعلانات', 'url' => 'announcements.php'],
    ['title' => 'بحث الإعلانات']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="fas fa-search me-2"></i>
        بحث الإعلانات
    </h1>
    <div>
        <a href="announcements.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
        <?php if (has_role('system_owner') || has_role('center_admin')): ?>
        <a href="system_announcements.php?new=1" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> إضافة إعلان جديد
        </a>
        <?php endif; ?>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i> خيارات البحث</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="search_announcements.php">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="q" class="form-label">كلمة البحث</label>
                    <input type="text" class="form-control" id="q" name="q" value="<?php echo $search_query; ?>" placeholder="ابحث في العنوان أو المحتوى">
                </div>

                <div class="col-md-4 mb-3">
                    <label for="target_role" class="form-label">الفئة المستهدفة</label>
                    <select class="form-select" id="target_role" name="target_role">
                        <option value="">-- الكل --</option>
                        <option value="all" <?php echo $target_role === 'all' ? 'selected' : ''; ?>>الجميع</option>
                        <option value="system_owner" <?php echo $target_role === 'system_owner' ? 'selected' : ''; ?>>مدير النظام</option>
                        <option value="center_admin" <?php echo $target_role === 'center_admin' ? 'selected' : ''; ?>>مدير المركز</option>
                        <option value="teacher" <?php echo $target_role === 'teacher' ? 'selected' : ''; ?>>المعلم</option>
                        <option value="student" <?php echo $target_role === 'student' ? 'selected' : ''; ?>>الطالب</option>
                        <option value="parent" <?php echo $target_role === 'parent' ? 'selected' : ''; ?>>ولي الأمر</option>
                        <option value="center_specific" <?php echo $target_role === 'center_specific' ? 'selected' : ''; ?>>مركز محدد</option>
                    </select>
                </div>

                <div class="col-md-4 mb-3" id="center_selection" <?php echo $target_role !== 'center_specific' ? 'style="display: none;"' : ''; ?>>
                    <label for="target_center_id" class="form-label">المركز المستهدف</label>
                    <select class="form-select" id="target_center_id" name="target_center_id">
                        <option value="">-- اختر المركز --</option>
                        <?php foreach ($centers as $center): ?>
                            <option value="<?php echo $center['center_id']; ?>" <?php echo $target_center_id == $center['center_id'] ? 'selected' : ''; ?>>
                                <?php echo $center['center_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="media_type" class="form-label">نوع الوسائط</label>
                    <select class="form-select" id="media_type" name="media_type">
                        <option value="">-- الكل --</option>
                        <option value="none" <?php echo $media_type === 'none' ? 'selected' : ''; ?>>بدون وسائط</option>
                        <option value="image" <?php echo $media_type === 'image' ? 'selected' : ''; ?>>صورة</option>
                        <option value="video" <?php echo $media_type === 'video' ? 'selected' : ''; ?>>فيديو</option>
                        <option value="audio" <?php echo $media_type === 'audio' ? 'selected' : ''; ?>>صوت</option>
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                </div>

                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                </div>

                <div class="col-md-3 mb-3">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <div class="input-group">
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="created_at" <?php echo $sort_by === 'created_at' ? 'selected' : ''; ?>>تاريخ الإنشاء</option>
                            <option value="title" <?php echo $sort_by === 'title' ? 'selected' : ''; ?>>العنوان</option>
                            <option value="start_date" <?php echo $sort_by === 'start_date' ? 'selected' : ''; ?>>تاريخ البداية</option>
                            <option value="end_date" <?php echo $sort_by === 'end_date' ? 'selected' : ''; ?>>تاريخ النهاية</option>
                            <option value="view_count" <?php echo $sort_by === 'view_count' ? 'selected' : ''; ?>>عدد المشاهدات</option>
                            <option value="click_count" <?php echo $sort_by === 'click_count' ? 'selected' : ''; ?>>عدد النقرات</option>
                        </select>
                        <select class="form-select" id="sort_order" name="sort_order">
                            <option value="DESC" <?php echo $sort_order === 'DESC' ? 'selected' : ''; ?>>تنازلي</option>
                            <option value="ASC" <?php echo $sort_order === 'ASC' ? 'selected' : ''; ?>>تصاعدي</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">ظهور للزوار</label>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_public" id="is_public_all" value="" <?php echo $is_public === '' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_public_all">الكل</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_public" id="is_public_yes" value="1" <?php echo $is_public === '1' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_public_yes">نعم</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_public" id="is_public_no" value="0" <?php echo $is_public === '0' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_public_no">لا</label>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <label class="form-label">إعلان مميز</label>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_featured" id="is_featured_all" value="" <?php echo $is_featured === '' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_featured_all">الكل</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_featured" id="is_featured_yes" value="1" <?php echo $is_featured === '1' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_featured_yes">نعم</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_featured" id="is_featured_no" value="0" <?php echo $is_featured === '0' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_featured_no">لا</label>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <label class="form-label">الحالة</label>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_active" id="is_active_all" value="" <?php echo $is_active === '' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active_all">الكل</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_active" id="is_active_yes" value="1" <?php echo $is_active === '1' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active_yes">نشط</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="is_active" id="is_active_no" value="0" <?php echo $is_active === '0' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active_no">غير نشط</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="search_announcements.php" class="btn btn-secondary">إعادة تعيين</a>
                <button type="submit" class="btn btn-primary">بحث</button>
            </div>
        </form>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-bullhorn me-2"></i> نتائج البحث</h5>
    </div>
    <div class="card-body">
        <?php if (empty($announcements)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> لا توجد إعلانات تطابق معايير البحث.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>المرسل</th>
                            <th>الفئة المستهدفة</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>المشاهدات</th>
                            <th>النقرات</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($announcements as $announcement): ?>
                            <tr>
                                <td><?php echo $announcement['title']; ?></td>
                                <td><?php echo $announcement['sender_name']; ?></td>
                                <td>
                                    <?php
                                    switch ($announcement['target_role']) {
                                        case 'all':
                                            echo 'الجميع';
                                            break;
                                        case 'system_owner':
                                            echo 'مدير النظام';
                                            break;
                                        case 'center_admin':
                                            echo 'مدير المركز';
                                            break;
                                        case 'teacher':
                                            echo 'المعلم';
                                            break;
                                        case 'student':
                                            echo 'الطالب';
                                            break;
                                        case 'parent':
                                            echo 'ولي الأمر';
                                            break;
                                        case 'center_specific':
                                            echo 'مركز محدد';
                                            break;
                                        default:
                                            echo $announcement['target_role'];
                                    }
                                    ?>
                                </td>
                                <td><?php echo isset($announcement['start_date']) ? $announcement['start_date'] : '-'; ?></td>
                                <td><?php echo isset($announcement['end_date']) ? $announcement['end_date'] : '-'; ?></td>
                                <td><?php echo isset($announcement['view_count']) ? number_format($announcement['view_count']) : '0'; ?></td>
                                <td><?php echo isset($announcement['click_count']) ? number_format($announcement['click_count']) : '0'; ?></td>
                                <td>
                                    <?php if (isset($announcement['is_active']) && $announcement['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="announcement_details.php?id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (has_role('system_owner') || has_role('center_admin')): ?>
                                            <a href="announcement_stats.php?id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-primary" title="الإحصائيات">
                                                <i class="fas fa-chart-bar"></i>
                                            </a>
                                            <a href="system_announcements.php?id=<?php echo $announcement['announcement_id']; ?>" class="btn btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show/hide center selection based on target role
        const targetRoleSelect = document.getElementById('target_role');
        const centerSelection = document.getElementById('center_selection');

        targetRoleSelect.addEventListener('change', function() {
            if (this.value === 'center_specific') {
                centerSelection.style.display = 'block';
            } else {
                centerSelection.style.display = 'none';
            }
        });
    });
</script>

<?php
// Include footer template
include_template('footer');
?>
