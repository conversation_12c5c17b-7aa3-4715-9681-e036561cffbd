<?php
// Include common functions and definitions
require_once '../includes/common.php';

// Check if user is logged in and has appropriate role
if (!is_logged_in() || !has_role('system_owner')) {
    set_flash_message('danger', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    redirect('auth/login.php');
}

// Check if user ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('danger', 'معرف المستخدم غير صحيح');
    redirect('pages/users.php');
}

$user_id = (int)$_GET['id'];
$error = '';
$success = '';

// Get user information
try {
    $stmt = $pdo->prepare("
        SELECT u.*, c.center_name, r.role_name
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        LEFT JOIN centers c ON u.center_id = c.center_id
        WHERE u.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        set_flash_message('danger', 'المستخدم غير موجود');
        redirect('pages/users.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المستخدم: ' . $e->getMessage();
}

// Get centers for dropdown
try {
    $stmt = $pdo->prepare("SELECT center_id, center_name FROM centers WHERE is_active = TRUE ORDER BY center_name");
    $stmt->execute();
    $centers = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات المراكز: ' . $e->getMessage();
}

// Get roles for dropdown
try {
    $stmt = $pdo->prepare("SELECT role_id, role_name FROM roles ORDER BY role_name");
    $stmt->execute();
    $roles = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء استرجاع بيانات الأدوار: ' . $e->getMessage();
}

// Check if columns exist
try {
    // Check if specialization column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'specialization'
    ");
    $stmt->execute();
    $specialization_exists = (bool)$stmt->fetchColumn();
    
    // Check if qualifications column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'qualifications'
    ");
    $stmt->execute();
    $qualifications_exists = (bool)$stmt->fetchColumn();
    
    // Check if birth_date column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'birth_date'
    ");
    $stmt->execute();
    $birth_date_exists = (bool)$stmt->fetchColumn();
    
    // Check if gender column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'gender'
    ");
    $stmt->execute();
    $gender_exists = (bool)$stmt->fetchColumn();
    
    // Check if address column exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'address'
    ");
    $stmt->execute();
    $address_exists = (bool)$stmt->fetchColumn();
} catch (PDOException $e) {
    $error = 'حدث خطأ أثناء التحقق من أعمدة الجدول: ' . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate input
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone_number = sanitize_input($_POST['phone_number']);
    $role_id = (int)$_POST['role_id'];
    $center_id = !empty($_POST['center_id']) ? (int)$_POST['center_id'] : null;
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Optional fields based on column existence
    $specialization = $specialization_exists ? sanitize_input($_POST['specialization'] ?? '') : '';
    $qualifications = $qualifications_exists ? sanitize_input($_POST['qualifications'] ?? '') : '';
    $birth_date = $birth_date_exists ? sanitize_input($_POST['birth_date'] ?? '') : '';
    $gender = $gender_exists ? sanitize_input($_POST['gender'] ?? '') : '';
    $address = $address_exists ? sanitize_input($_POST['address'] ?? '') : '';
    
    // New password (optional)
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validate required fields
    if (empty($full_name)) {
        $error = 'يرجى إدخال الاسم الكامل';
    } elseif (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!empty($new_password) && $new_password !== $confirm_password) {
        $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
    } else {
        try {
            // Check if email already exists for another user
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
            $stmt->execute([$email, $user_id]);
            if ($stmt->rowCount() > 0) {
                $error = 'البريد الإلكتروني موجود بالفعل، يرجى استخدام بريد آخر';
            } else {
                // Begin transaction
                $pdo->beginTransaction();
                
                // Build update query based on existing columns
                $query = "UPDATE users SET full_name = ?, email = ?, phone_number = ?, role_id = ?, center_id = ?, is_active = ?";
                $params = [$full_name, $email, $phone_number, $role_id, $center_id, $is_active];
                
                if ($specialization_exists) {
                    $query .= ", specialization = ?";
                    $params[] = $specialization;
                }
                
                if ($qualifications_exists) {
                    $query .= ", qualifications = ?";
                    $params[] = $qualifications;
                }
                
                if ($birth_date_exists) {
                    $query .= ", birth_date = ?";
                    $params[] = $birth_date;
                }
                
                if ($gender_exists) {
                    $query .= ", gender = ?";
                    $params[] = $gender;
                }
                
                if ($address_exists) {
                    $query .= ", address = ?";
                    $params[] = $address;
                }
                
                $query .= " WHERE user_id = ?";
                $params[] = $user_id;
                
                // Update user information
                $stmt = $pdo->prepare($query);
                $stmt->execute($params);
                
                // Update password if provided
                if (!empty($new_password)) {
                    $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE user_id = ?");
                    $stmt->execute([$password_hash, $user_id]);
                }
                
                // Handle profile picture upload if provided
                if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/images/profiles/';
                    
                    // Create directory if it doesn't exist
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }
                    
                    $file_extension = pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION);
                    $new_filename = 'user_' . $user_id . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_filename;
                    
                    if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
                        // Update user with profile picture URL
                        $profile_picture_url = 'assets/images/profiles/' . $new_filename;
                        $stmt = $pdo->prepare("UPDATE users SET profile_picture_url = ? WHERE user_id = ?");
                        $stmt->execute([$profile_picture_url, $user_id]);
                    }
                }
                
                // Commit transaction
                $pdo->commit();
                
                $success = 'تم تحديث بيانات المستخدم بنجاح';
                
                // Refresh user data
                $stmt = $pdo->prepare("
                    SELECT u.*, c.center_name, r.role_name
                    FROM users u
                    JOIN roles r ON u.role_id = r.role_id
                    LEFT JOIN centers c ON u.center_id = c.center_id
                    WHERE u.user_id = ?
                ");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $error = 'حدث خطأ أثناء تحديث بيانات المستخدم: ' . $e->getMessage();
        }
    }
}

// Page variables
$page_title = 'تعديل بيانات المستخدم: ' . $user['full_name'];
$active_page = 'users';

// Set up breadcrumbs
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => 'system_owner_dashboard.php'],
    ['title' => 'المستخدمين', 'url' => 'users.php'],
    ['title' => 'تعديل بيانات المستخدم']
];

// Include header template
include_template('header', [
    'page_title' => $page_title,
    'active_page' => $active_page
]);

// Include breadcrumb template
include_template('breadcrumb', ['breadcrumbs' => $breadcrumbs]);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="fas fa-user-edit me-2"></i>
        تعديل بيانات المستخدم: <?php echo $user['full_name']; ?>
    </h1>
    <div>
        <a href="user_details.php?id=<?php echo $user_id; ?>" class="btn btn-info">
            <i class="fas fa-eye me-1"></i> عرض التفاصيل
        </a>
        <a href="users.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0"><i class="fas fa-edit me-2"></i> تعديل بيانات المستخدم</h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" value="<?php echo $user['username']; ?>" readonly disabled>
                    <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required value="<?php echo $user['full_name']; ?>">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="new_password" name="new_password">
                    <div class="form-text">اتركها فارغة إذا كنت لا ترغب في تغيير كلمة المرور</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" required value="<?php echo $user['email']; ?>">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="phone_number" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="phone_number" name="phone_number" value="<?php echo $user['phone_number']; ?>">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="role_id" class="form-label">الدور <span class="text-danger">*</span></label>
                    <select class="form-select" id="role_id" name="role_id" required>
                        <option value="">-- اختر الدور --</option>
                        <?php foreach ($roles as $role): ?>
                            <option value="<?php echo $role['role_id']; ?>" <?php echo $user['role_id'] == $role['role_id'] ? 'selected' : ''; ?>>
                                <?php echo $role['role_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="center_id" class="form-label">المركز</label>
                    <select class="form-select" id="center_id" name="center_id">
                        <option value="">-- اختر المركز --</option>
                        <?php foreach ($centers as $center): ?>
                            <option value="<?php echo $center['center_id']; ?>" <?php echo $user['center_id'] == $center['center_id'] ? 'selected' : ''; ?>>
                                <?php echo $center['center_name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <?php if ($specialization_exists || $qualifications_exists): ?>
            <div class="row">
                <?php if ($specialization_exists): ?>
                <div class="col-md-6 mb-3">
                    <label for="specialization" class="form-label">التخصص</label>
                    <input type="text" class="form-control" id="specialization" name="specialization" value="<?php echo $user['specialization'] ?? ''; ?>">
                </div>
                <?php endif; ?>
                
                <div class="col-md-<?php echo $specialization_exists ? '6' : '12'; ?> mb-3">
                    <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                    <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                    <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                </div>
            </div>
            <?php else: ?>
            <div class="mb-3">
                <label for="profile_picture" class="form-label">الصورة الشخصية</label>
                <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*">
                <div class="form-text">الحد الأقصى لحجم الملف: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
            </div>
            <?php endif; ?>
            
            <?php if ($qualifications_exists): ?>
            <div class="mb-3">
                <label for="qualifications" class="form-label">المؤهلات والخبرات</label>
                <textarea class="form-control" id="qualifications" name="qualifications" rows="3"><?php echo $user['qualifications'] ?? ''; ?></textarea>
            </div>
            <?php endif; ?>
            
            <?php if ($birth_date_exists || $gender_exists): ?>
            <div class="row">
                <?php if ($birth_date_exists): ?>
                <div class="col-md-6 mb-3">
                    <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date" value="<?php echo $user['birth_date'] ?? ''; ?>">
                </div>
                <?php endif; ?>
                
                <?php if ($gender_exists): ?>
                <div class="col-md-<?php echo $birth_date_exists ? '6' : '12'; ?> mb-3">
                    <label for="gender" class="form-label">الجنس</label>
                    <select class="form-select" id="gender" name="gender">
                        <option value="">-- اختر الجنس --</option>
                        <option value="ذكر" <?php echo (isset($user['gender']) && $user['gender'] == 'ذكر') ? 'selected' : ''; ?>>ذكر</option>
                        <option value="أنثى" <?php echo (isset($user['gender']) && $user['gender'] == 'أنثى') ? 'selected' : ''; ?>>أنثى</option>
                    </select>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <?php if ($address_exists): ?>
            <div class="mb-3">
                <label for="address" class="form-label">العنوان</label>
                <textarea class="form-control" id="address" name="address" rows="2"><?php echo $user['address'] ?? ''; ?></textarea>
            </div>
            <?php endif; ?>
            
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $user['is_active'] ? 'checked' : ''; ?>>
                <label class="form-check-label" for="is_active">حساب نشط</label>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="user_details.php?id=<?php echo $user_id; ?>" class="btn btn-secondary">إلغاء</a>
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </form>
    </div>
</div>

<?php if (!empty($user['profile_picture_url'])): ?>
<div class="card shadow mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="card-title mb-0"><i class="fas fa-image me-2"></i> الصورة الحالية</h5>
    </div>
    <div class="card-body text-center">
        <img src="<?php echo get_root_url() . $user['profile_picture_url']; ?>" alt="صورة المستخدم" class="img-fluid rounded" style="max-height: 300px;">
    </div>
</div>
<?php endif; ?>

<?php
// Include footer template
include_template('footer');
?>
