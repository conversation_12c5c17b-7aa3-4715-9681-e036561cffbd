<?php
/**
 * Header Template
 *
 * This template includes the HTML head and navigation bar
 *
 * @param string $page_title - The title of the page
 * @param string $active_page - The current active page for navigation highlighting
 */

// Default values
$page_title = $page_title ?? SITE_NAME;
$active_page = $active_page ?? '';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <?php if (isset($use_datatables) && $use_datatables): ?>
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <?php endif; ?>

    <?php if (isset($use_summernote) && $use_summernote): ?>
    <!-- Summernote CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
    <?php endif; ?>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo get_root_url(); ?>css/style.css">

    <?php if (isset($extra_css)): ?>
    <?php echo $extra_css; ?>
    <?php endif; ?>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="<?php echo get_root_url(); ?>index.php"><?php echo SITE_NAME; ?></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <?php if (is_logged_in()): ?>
                            <!-- رابط الرئيسية للمستخدمين المسجلين -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo $active_page == 'home' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>index.php">الرئيسية</a>
                            </li>

                            <?php if (has_role('system_owner')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'dashboard' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/system_owner_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'centers' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/centers.php">المراكز</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'users' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/users.php">المستخدمين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'parents' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/parents.php">أولياء الأمور</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'reports' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/reports.php">التقارير</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'settings' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/system_settings.php">الإعدادات</a>
                                </li>
                            <?php elseif (has_role('center_admin')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'dashboard' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/center_admin_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'teachers' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/teachers.php">المعلمين</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'circles' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/circles.php">الحلقات</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'students' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/students.php">الطلاب</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'parents' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/parents.php">أولياء الأمور</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'reports' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/reports.php">التقارير</a>
                                </li>
                            <?php elseif (has_role('teacher')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'dashboard' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/teacher_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'my_circles' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/my_circles.php">حلقاتي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'students' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/teacher_students.php">طلابي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'attendance' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/attendance.php">الحضور</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'memorization' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/memorization.php">الحفظ</a>
                                </li>
                            <?php elseif (has_role('student')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'dashboard' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/student_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'progress' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/my_progress.php">تقدمي</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'assignments' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/student_assignments.php">الواجبات</a>
                                </li>
                            <?php elseif (has_role('parent')): ?>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'dashboard' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/parent_dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link <?php echo $active_page == 'children' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/my_children.php">أبنائي</a>
                                </li>
                            <?php endif; ?>

                            <li class="nav-item">
                                <a class="nav-link <?php echo $active_page == 'announcements' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/announcements.php">الإعلانات</a>
                            </li>
                        <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $active_page == 'home' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>index.php">الرئيسية</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $active_page == 'about' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/about.php">عن النظام</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $active_page == 'contact' ? 'active' : ''; ?>" href="<?php echo get_root_url(); ?>pages/contact.php">اتصل بنا</a>
                            </li>
                        <?php endif; ?>
                    </ul>

                    <div class="d-flex">
                        <?php if (is_logged_in()): ?>
                            <div class="dropdown">
                                <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-1"></i> <?php echo $_SESSION['full_name']; ?>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="<?php echo get_root_url(); ?>pages/profile.php">الملف الشخصي</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo get_root_url(); ?>auth/logout.php">تسجيل الخروج</a></li>
                                </ul>
                            </div>
                        <?php else: ?>
                            <a href="<?php echo get_root_url(); ?>auth/login.php" class="btn btn-outline-light me-2">تسجيل الدخول</a>
                            <a href="<?php echo get_root_url(); ?>auth/register.php" class="btn btn-light">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="container py-4">
        <?php if (isset($_SESSION['flash_message']) && isset($_SESSION['flash_type'])): ?>
            <div class="alert alert-<?php echo $_SESSION['flash_type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['flash_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
        <?php endif; ?>
